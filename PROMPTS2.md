# ScINTEG模型深度审查Prompts集合

## 📋 目录

- [ScINTEG模型深度审查Prompts集合](#scinteg模型深度审查prompts集合)
  - [📋 目录](#-目录)
  - [📖 简介](#-简介)
    - [使用方法](#使用方法)
  - [1. 架构设计审查](#1-架构设计审查)
  - [2. 生物学合理性验证](#2-生物学合理性验证)
  - [3. 性能优化深度分析](#3-性能优化深度分析)
  - [4. 数学理论基础审查](#4-数学理论基础审查)
  - [5. 代码质量和软件工程](#5-代码质量和软件工程)
  - [6. 实际应用场景适配](#6-实际应用场景适配)
  - [7. 前沿技术整合](#7-前沿技术整合)
  - [8. 基准测试和评估](#8-基准测试和评估)
  - [9. 用户体验优化](#9-用户体验优化)
  - [10. 长期维护和社区建设](#10-长期维护和社区建设)
  - [🛠️ 最佳实践](#️-最佳实践)
    - [使用这些Prompts的建议：](#使用这些prompts的建议)
  - [📌 版本信息](#-版本信息)
  - [📝 贡献指南](#-贡献指南)

## 📖 简介

本文档包含了一系列专业的prompts，用于对ScINTEG v3模型进行全面深入的审查和改进。这些prompts涵盖了技术架构、生物学合理性、性能优化、软件工程等多个维度，旨在帮助研究人员和开发者系统地评估和完善模型。

### 使用方法

1. **分阶段使用**：根据当前开发阶段选择相应的prompts
2. **迭代改进**：每个prompt的反馈都应该转化为具体的改进任务
3. **多角度审查**：邀请不同背景的专家使用不同的prompts
4. **文档化**：将每次审查的结果和改进措施记录下来

---

## 1. 架构设计审查

```
作为一名深度学习架构专家，请详细分析ScINTEG v3模型的架构设计：

1. 评估当前的编码器-投影器-解码器架构是否最优，特别是：
   - Cell Encoder和Gene Encoder的设计是否充分捕获了单细胞数据的特征？
   - Hierarchical Pathway Projector的两级设计是否真正减少了信息损失？
   - 是否存在架构瓶颈限制了模型表达能力？

2. 对比分析以下替代架构的优劣：
   - Transformer-based架构 vs 当前的GNN架构
   - VAE/GAN架构用于表达重建
   - Attention机制在不同模块中的应用

3. 提出具体的架构改进建议，包括：
   - 模块间的连接方式优化
   - Skip connections的更好利用
   - Multi-scale特征融合策略

请提供详细的技术分析和可实施的改进方案。
```

## 2. 生物学合理性验证

```
作为计算生物学专家，请从生物学角度审查ScINTEG模型：

1. 基因调控网络推断模块：
   - 当前的GRN预测器是否符合真实的基因调控机制？
   - 如何验证推断的调控关系的生物学意义？
   - 是否需要引入转录因子-靶基因的先验知识？

2. 通路活性分析：
   - Pathway mask的使用是否充分？
   - 如何处理基因在多个通路中的重叠？
   - Meta-pathway的生物学解释性如何保证？

3. 单细胞特异性：
   - 模型是否充分考虑了单细胞数据的稀疏性和噪声？
   - 批次效应处理是否影响生物学信号？
   - 细胞异质性是否被正确建模？

请提供基于文献和实验证据的改进建议。
```

## 3. 性能优化深度分析

```
作为高性能计算专家，请对ScINTEG的计算性能进行全面分析：

1. 内存瓶颈分析：
   - 使用内存分析工具（如memory_profiler）定位内存热点
   - 评估当前数据结构的内存效率
   - 提出内存优化策略（如tensor共享、原地操作等）

2. 计算瓶颈识别：
   - Profile各个模块的计算时间
   - 识别可并行化的计算
   - 评估稀疏操作的应用潜力

3. 扩展性评估：
   - 测试不同规模数据（1万、10万、100万细胞）的性能
   - 分析性能随数据规模的变化趋势
   - 提出分布式训练方案

请提供具体的优化代码示例和预期性能提升。
```

## 4. 数学理论基础审查

```
作为机器学习理论专家，请审查ScINTEG的数学基础：

1. 损失函数设计：
   - 当前的多任务损失权重是否最优？
   - ZINB损失的数学推导是否正确？
   - 是否需要自适应损失权重策略？

2. 优化理论：
   - 模型的收敛性保证
   - 梯度流分析（是否存在梯度消失/爆炸）
   - 超参数敏感性分析

3. 理论保证：
   - 模型的表达能力理论分析
   - 泛化误差界的推导
   - 与其他方法的理论比较

请提供严格的数学分析和改进建议。
```

## 5. 代码质量和软件工程

```
作为资深软件工程师，请评估ScINTEG的代码质量：

1. 设计模式应用：
   - 评估当前的模块化设计
   - 识别可应用的设计模式（如策略模式、工厂模式等）
   - 提出重构建议以提高代码可维护性

2. 错误处理和鲁棒性：
   - 审查异常处理机制的完整性
   - 识别潜在的运行时错误
   - 提出防御性编程改进

3. 测试覆盖度：
   - 分析当前的测试覆盖情况
   - 识别需要额外测试的关键路径
   - 提出集成测试和压力测试方案

4. API设计：
   - 评估API的一致性和易用性
   - 提出更符合Python惯例的接口设计
   - 文档完整性评估

请提供具体的代码改进示例。
```

## 6. 实际应用场景适配

```
作为单细胞数据分析专家，请评估ScINTEG在实际应用中的表现：

1. 数据类型适配性：
   - 测试不同类型的单细胞数据（scRNA-seq, scATAC-seq, spatial等）
   - 评估对不同测序平台数据的鲁棒性
   - 提出多模态数据整合方案

2. 真实数据挑战：
   - 处理极端稀疏数据（>99%零值）
   - 应对批次效应严重的数据
   - 处理细胞数量不平衡的数据集

3. 下游分析整合：
   - 与现有单细胞分析流程（Seurat, Scanpy）的整合
   - 结果的可解释性和可视化
   - 与实验验证的对接

请基于真实数据集提供改进建议。
```

## 7. 前沿技术整合

```
作为AI研究专家，请探讨如何将最新技术整合到ScINTEG中：

1. 最新深度学习技术：
   - Diffusion models在表达重建中的应用
   - Contrastive learning改进细胞嵌入
   - Neural ODE建模动态过程

2. 因果推断：
   - 将因果推断方法整合到GRN预测
   - 使用干预数据改进模型
   - 反事实推理在通路分析中的应用

3. 可解释AI：
   - 添加注意力可视化
   - SHAP/LIME等方法的应用
   - 生物学可解释的特征重要性

请提供具体的实现方案和预期效果。
```

## 8. 基准测试和评估

```
请设计全面的基准测试方案来评估ScINTEG：

1. 评估指标设计：
   - 重建质量的多维度评估
   - GRN预测的准确性度量
   - 通路活性的生物学相关性

2. 基准数据集：
   - 推荐用于测试的标准数据集
   - 模拟数据的生成策略
   - 金标准的获取和使用

3. 竞争方法比较：
   - 与SCENIC, GENIE3, GRNBoost2等的比较
   - 公平比较的实验设计
   - 计算资源消耗对比

请提供完整的评估pipeline代码框架。
```

## 9. 用户体验优化

```
作为产品设计专家，请优化ScINTEG的用户体验：

1. 安装和配置：
   - 简化依赖管理
   - 提供Docker/Singularity容器
   - 自动化环境配置

2. 使用流程：
   - 设计直观的CLI和GUI
   - 提供交互式教程（Jupyter notebooks）
   - 错误信息的友好化

3. 结果解释：
   - 自动生成分析报告
   - 交互式可视化工具
   - 结果导出格式标准化

请提供具体的UX改进方案。
```

## 10. 长期维护和社区建设

```
请制定ScINTEG的长期发展策略：

1. 版本管理：
   - 制定版本发布计划
   - 向后兼容性策略
   - 弃用功能的处理

2. 社区参与：
   - 贡献指南编写
   - Issue模板设计
   - 社区交流渠道建立

3. 持续改进：
   - 用户反馈收集机制
   - 性能监控和优化
   - 新功能路线图

请提供具体的实施计划。
```

---

## 🛠️ 最佳实践

### 使用这些Prompts的建议：

1. **准备工作**
   - 熟悉ScINTEG的代码结构和文档
   - 准备相关的测试数据和环境
   - 明确当前的开发目标和优先级

2. **执行审查**
   - 每次专注于1-2个prompts
   - 记录详细的发现和建议
   - 与团队成员讨论结果

3. **后续行动**
   - 将建议转化为具体的开发任务
   - 创建相应的GitHub issues
   - 定期回顾和更新改进进度

4. **持续改进**
   - 定期更新prompts以反映新的需求
   - 收集使用反馈并优化prompts
   - 分享成功的改进案例

---

## 📌 版本信息

- **文档版本**: 1.0.0
- **创建日期**: 2025-08-05
- **最后更新**: 2025-08-05
- **适用版本**: ScINTEG v3.0+

---

## 📝 贡献指南

欢迎对本文档提出改进建议：
1. 提出新的审查角度和prompts
2. 优化现有prompts的表述
3. 分享使用经验和最佳实践

请通过GitHub Issues或Pull Requests提交您的贡献。