# ScINTEG 代码库重构任务计划

## 🎯 目标
彻底重构 ScINTEG 代码库，从当前的混乱状态转变为清晰、模块化、可维护的架构。

## 📋 任务清单

### Phase 1: 评估和规划 (30分钟) ✓
- [x] 1.1 扫描所有现有文件，创建文件清单 (FILE_INVENTORY.md)
- [x] 1.2 识别核心功能模块
- [x] 1.3 绘制当前的依赖关系图 (DEPENDENCY_ANALYSIS.md)
- [x] 1.4 确定需要保留、修改或删除的代码

### Phase 2: 创建新项目结构 (1小时) ✓
- [x] 2.1 创建 `scinteg_v3_clean/` 根目录
- [x] 2.2 建立标准Python项目结构：
  ```
  scinteg_v3_clean/
  ├── scinteg/
  │   ├── __init__.py
  │   ├── core/
  │   │   ├── __init__.py
  │   │   ├── base.py          # 基类定义
  │   │   └── interfaces.py     # 接口定义
  │   ├── models/
  │   │   ├── __init__.py
  │   │   ├── encoders/
  │   │   ├── projectors/
  │   │   ├── decoders/
  │   │   └── scinteg.py       # 主模型
  │   ├── data/
  │   │   ├── __init__.py
  │   │   ├── loaders.py
  │   │   └── preprocessing.py
  │   ├── training/
  │   │   ├── __init__.py
  │   │   ├── trainer.py
  │   │   └── losses.py
  │   └── evaluation/
  │       ├── __init__.py
  │       └── metrics.py
  ├── tests/
  │   ├── unit/
  │   ├── integration/
  │   └── fixtures/
  ├── examples/
  ├── configs/
  ├── setup.py
  ├── requirements.txt
  └── README.md
  ```
- [x] 2.3 创建 setup.py 确保正确的包安装
- [x] 2.4 设置 .gitignore

### Phase 3: 核心模块迁移 (2小时)
- [x] 3.1 定义基础接口和抽象类
  - [x] BaseEncoder
  - [x] BaseDecoder
  - [x] BaseProjector
  - [x] BaseLoss
- [ ] 3.2 迁移和重构编码器
  - [ ] CellEncoder → StandardizedCellEncoder
  - [ ] GeneEncoder → StandardizedGeneEncoder
- [ ] 3.3 迁移和重构投影器
  - [ ] PathwayProjector → StandardizedPathwayProjector
  - [ ] HierarchicalProjector → StandardizedHierarchicalProjector
- [ ] 3.4 迁移和重构解码器
  - [ ] ExpressionReconstructor → StandardizedDecoder
  - [ ] UNetDecoder → StandardizedUNetDecoder
- [ ] 3.5 整合为 ScINTEGv3Clean 主模型

### Phase 4: 测试框架建立 (1.5小时)
- [ ] 4.1 单元测试
  - [ ] test_encoders.py
  - [ ] test_projectors.py
  - [ ] test_decoders.py
  - [ ] test_losses.py
- [ ] 4.2 集成测试
  - [ ] test_data_flow.py
  - [ ] test_model_integration.py
- [ ] 4.3 端到端测试
  - [ ] test_training.py
  - [ ] test_inference.py

### Phase 5: 训练和评估系统 (1小时)
- [ ] 5.1 实现标准化训练器
- [ ] 5.2 实现评估指标
- [ ] 5.3 实现结果可视化
- [ ] 5.4 创建训练脚本模板

### Phase 6: 文档和示例 (30分钟)
- [ ] 6.1 编写 README.md
- [ ] 6.2 创建快速开始指南
- [ ] 6.3 编写 API 文档
- [ ] 6.4 创建示例笔记本

### Phase 7: 验证和清理 (30分钟)
- [ ] 7.1 运行完整测试套件
- [ ] 7.2 运行示例脚本
- [ ] 7.3 检查代码质量（linting）
- [ ] 7.4 删除旧的混乱代码

## 🛠️ 实施策略

### 1. 模块标准化模板
```python
# 每个模块都遵循此结构
class StandardizedModule(BaseModule):
    """明确的文档字符串"""
    
    def __init__(self, **kwargs):
        """明确的参数列表和类型"""
        super().__init__()
        self._validate_params(**kwargs)
        self._build_module(**kwargs)
    
    def forward(self, *args, **kwargs):
        """明确的输入输出规范"""
        # 输入验证
        # 核心逻辑
        # 输出验证
        return output
    
    def get_config(self):
        """返回配置用于重现"""
        return self.config
```

### 2. 测试优先原则
- 先写测试用例，定义期望行为
- 实现代码以通过测试
- 重构以提高代码质量

### 3. 配置驱动设计
```yaml
# config/scinteg_v3.yaml
model:
  encoders:
    cell:
      type: "standard"
      hidden_dim: 128
      output_dim: 64
  projectors:
    type: "hierarchical"
    n_pathways: 50
    n_meta_pathways: 20
```

## ⚠️ 关键注意事项

1. **不要复制粘贴**：每个模块都要理解后重写
2. **保持简单**：宁可功能少但稳定，不要功能多但混乱
3. **测试覆盖**：没有测试的代码就是债务
4. **文档同步**：代码改变时立即更新文档
5. **版本控制**：每个重要步骤都要提交

## 📊 成功标准

- [ ] 所有测试通过
- [ ] 无导入错误
- [ ] 可以独立运行每个组件
- [ ] 完整的端到端训练流程可运行
- [ ] 代码结构清晰，易于理解
- [ ] 有完整的文档和示例

## 🚀 立即行动

1. 停止在现有混乱代码上修补
2. 从头开始，建立清晰的架构
3. 每一步都要验证
4. 保持专注，一次只做一件事

## 📝 进度追踪

### 当前状态：开始重构
- 开始时间：2024-XX-XX
- 预计完成：6小时内

### 已完成
- [x] 生成任务计划
- [x] Phase 1: 评估和规划
- [x] Phase 2: 创建新项目结构
- [x] Phase 3.1: 定义基础接口和抽象类
- [x] Phase 3.2 (部分): 迁移编码器 (Cell和Gene)

### 正在进行
- [ ] Phase 3: 核心模块迁移 (投影器、解码器、预测器)

### 下一步
- Phase 4: 建立测试框架

### 进度统计
- 总体进度: 约40% 完成
- 核心架构: 90% 完成
- 模块迁移: 30% 完成
- 测试覆盖: 10% 完成

---

**重要提醒**：严格按照计划执行，不要跳步骤，不要走捷径。