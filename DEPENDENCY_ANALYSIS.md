# ScINTEG 依赖关系分析

## 核心架构图

```
ScINTEG v3 主模型
    │
    ├── 编码器层
    │   ├── CellEncoder (cell_encoder.py)
    │   └── GeneEncoder (gene_encoder.py)
    │
    ├── 投影器层
    │   ├── PathwayProjector (pathway_projector.py)
    │   └── HierarchicalPathwayProjectorV2 (hierarchical_pathway_projector_v2.py)
    │
    ├── 解码器层
    │   ├── ExpressionReconstructor (expression_reconstructor.py)
    │   └── UNetDecoderV2 (unet_decoder.py)
    │
    ├── 预测器层
    │   └── RegulationPredictor (regulation_predictor.py)
    │
    ├── 损失函数
    │   ├── AdaptiveHuberLoss (robust_loss.py)
    │   ├── ContrastiveLoss (adaptive_loss.py)
    │   └── TemporalConsistencyLoss (内联定义)
    │
    └── 辅助组件
        ├── BatchCorrector (batch_correction.py)
        └── TemporalConsistencyModule (temporal_consistency.py)
```

## 数据流

```
输入数据
    ↓
批次校正 (可选)
    ↓
细胞编码 → 时序平滑 (可选) → 通路投影
    ↓                               ↓
基因编码 ←─────────────────→ 表达重建
    ↓
GRN推断
```

## 主要依赖问题

### 1. 导入混乱
- scinteg_v3.py 在 scinteg_clean/scinteg/models/
- 但它导入的模块在 scinteg/models/
- 导致路径不匹配

### 2. 接口不一致
```python
# UNetDecoderV2 期望
forward(z_p, z_meta)  # 或 forward(z_combined)

# ExpressionReconstructor 期望
forward(z_p, z_g)

# 但 ScINTEGv3 传递的参数不一致
```

### 3. 循环依赖风险
- 多个模块相互引用
- 缺少清晰的层次结构

### 4. 硬编码耦合
- 维度参数散落在各处
- 缺少中心配置管理

## 重构建议

### 1. 建立清晰的层次
```
base/
  ├── base_encoder.py
  ├── base_decoder.py
  ├── base_projector.py
  └── base_loss.py

models/
  ├── encoders/
  ├── projectors/
  ├── decoders/
  └── predictors/
```

### 2. 统一接口规范
```python
class BaseModule:
    def forward(self, inputs: Dict[str, Tensor]) -> Dict[str, Tensor]:
        pass
```

### 3. 配置驱动
```python
config = {
    'model': {
        'encoder': {'type': 'cell', 'params': {...}},
        'projector': {'type': 'hierarchical', 'params': {...}},
        'decoder': {'type': 'unet', 'params': {...}}
    }
}
```

### 4. 依赖注入
```python
class ScINTEG:
    def __init__(self, encoder, projector, decoder, predictor):
        self.encoder = encoder
        self.projector = projector
        self.decoder = decoder
        self.predictor = predictor
```

## 需要立即修复的问题

1. **路径问题**: 所有代码应该在一个统一的包结构下
2. **接口问题**: 标准化所有模块的输入输出
3. **配置问题**: 集中管理所有配置参数
4. **测试问题**: 每个模块都需要独立的单元测试