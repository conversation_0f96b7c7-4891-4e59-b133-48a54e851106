# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

ScINTEG is a deep learning framework for inferring gene regulatory networks (GRNs) from single-cell RNA-seq data. It uses graph neural networks to integrate cell-gene bipartite graph structures with biological prior knowledge.

## Key Commands

### Setup and Installation
```bash
# Create conda environment
conda create -n scinteg python=3.8
conda activate scinteg

# Install dependencies and ScINTEG
pip install -r scinteg_v3_clean/requirements.txt
pip install -e scinteg_v3_clean/

# For development (includes testing and linting tools)
pip install -e "scinteg_v3_clean/[dev]"

# For GPU support
pip install -e "scinteg_v3_clean/[gpu]"
```

### Testing
```bash
# Run all tests from the scinteg_v3_clean directory
cd scinteg_v3_clean
pytest tests/

# Run specific test files
pytest tests/test_integration.py
pytest tests/test_main_model.py

# Run with coverage
pytest --cov=scinteg tests/
```

### Code Quality
```bash
# Linting with flake8
flake8 scinteg_v3_clean/scinteg/

# Format code with black
black scinteg_v3_clean/scinteg/

# Type checking with mypy
mypy scinteg_v3_clean/scinteg/
```

## High-Level Architecture

The project is organized into multiple model implementations:

1. **scinteg_v3_clean/**: The main production-ready implementation
   - Modular architecture with clear separation of concerns
   - Comprehensive test suite in `tests/`
   - Examples in `examples/` showing different usage patterns

2. **multi_model/**: Alternative model implementations for comparison
   - `cefcon/`: CEFCON model implementation
   - `expimap/`: ExpiMap model implementation
   - `scGNN/`: scGNN baseline implementation
   - `scNET/`: scNET implementation

### Core Module Structure (scinteg_v3_clean/scinteg/)

- **core/**: Base classes and interfaces
  - `base.py`: Abstract base classes for all components
  - `interfaces.py`: Standard data structures (ModelInputs, ModelOutputs)

- **data/**: Data handling and preprocessing
  - `dataset.py`: ScINTEG dataset class
  - `preprocessing.py`: Data preprocessing utilities
  - `augmentation.py`: Data augmentation strategies
  - `dataloader.py`: Efficient data loading
  - `validation.py`: Data validation utilities

- **models/**: Model components following modular design
  - `encoders/`: Cell and gene encoders (GCN/GAT-based)
  - `projectors/`: Pathway projectors (standard and hierarchical)
  - `decoders/`: Expression decoders (MLP and U-Net)
  - `predictors/`: GRN prediction modules
  - `scinteg.py`: Main ScINTEGv3 model class

- **training/**: Training infrastructure
  - `trainer.py`: Main training loop
  - `losses.py`: Loss functions (MSE, NB, ZINB, Adaptive Huber)
  - `metrics.py`: Evaluation metrics
  - `schedulers.py`: Phased training scheduler
  - `checkpoint.py`: Model checkpointing

### Key Design Patterns

1. **Inheritance Hierarchy**: All components inherit from BaseModule
2. **Standardized I/O**: Components use ModelInputs/ModelOutputs dataclasses
3. **Configurable Architecture**: Support for different encoder types (GCN/GAT), decoder types (MLP/U-Net), and projector types (standard/hierarchical)
4. **Phased Training**: 3-phase training strategy for stable optimization

### Important Implementation Details

- The model uses a bipartite graph structure connecting cells and genes
- Pathway information is integrated through learnable soft masks
- Batch effect correction is implemented via SimpleBatchCorrector, HarmonyCorrector, or BiologyPreserver
- The hierarchical projector achieves >99% information preservation by using two-level projection
- Loss functions include reconstruction loss, GRN regularization, and pathway sparsity constraints

### Performance Considerations

- Use DataLoader with appropriate batch sizes (typically 128)
- For large datasets, use k-NN graphs instead of fully connected graphs
- Mixed precision training is supported for GPU acceleration
- The model supports multi-GPU training via DistributedDataParallel

### Testing Philosophy

- Unit tests for individual components in `tests/unit/`
- Integration tests for complete workflows in `tests/integration/`
- Performance tests for different data scenarios in `tests/performance/`
- Real data tests using PBMC3k dataset