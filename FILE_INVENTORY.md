# ScINTEG 文件清单和评估

## 目录结构分析

### 1. 主要目录
- `scinteg/` - 主代码库（混乱）
- `scinteg_clean/` - 清理尝试（几乎为空）
- `scripts/` - 测试脚本
- `tests/` - 部分测试文件
- 根目录散落的测试文件

### 2. 核心模块分析

#### 2.1 编码器 (Encoders)
- `scinteg/models/cell_encoder.py` - 细胞状态编码器
- `scinteg/models/gene_encoder.py` - 基因功能编码器

#### 2.2 投影器 (Projectors)
- `scinteg/models/pathway_projector.py` - 基础通路投影
- `scinteg/models/hierarchical_pathway_projector.py` - 层次化投影v1
- `scinteg/models/hierarchical_pathway_projector_v2.py` - 层次化投影v2

#### 2.3 解码器 (Decoders)
- `scinteg/models/expression_reconstructor.py` - 标准解码器
- `scinteg/models/unet_decoder.py` - U-Net解码器

#### 2.4 损失函数 (Loss Functions)
- `scinteg/models/loss_calculator.py` - 基础损失计算
- `scinteg/models/adaptive_loss.py` - 自适应损失
- `scinteg/models/robust_loss.py` - 鲁棒损失

#### 2.5 其他组件
- `scinteg/models/regulation_predictor.py` - GRN预测
- `scinteg/models/temporal_consistency.py` - 时序一致性
- `scinteg/models/batch_correction.py` - 批次效应校正

#### 2.6 主模型
- `scinteg/models/scinteg_model.py` - 原始ScINTEG
- `scinteg/models/scinteg_v3.py` - 尝试整合所有改进（有问题）

### 3. 支持模块

#### 3.1 数据处理
- `scinteg/data_processing.py` - 数据预处理和图构建
- `scinteg/datasets/data_handler.py` - 数据集处理

#### 3.2 训练
- `scinteg/training/monitor.py` - 训练监控
- `scinteg/training/phased_scheduler.py` - 分阶段训练调度

#### 3.3 评估
- `scinteg/evaluation/metrics.py` - 原始评估指标
- `scinteg/evaluation/basic_metrics.py` - 基础评估指标
- `scinteg/evaluation/benchmark.py` - 基准测试

#### 3.4 可视化
- `scinteg/visualization/comprehensive_viz.py` - 综合可视化
- `scinteg/utils/visualization.py` - 工具可视化

### 4. 混乱的根源

1. **重复文件**: 
   - phased_scheduler.py 在两个地方
   - comprehensive_viz.py 在两个地方

2. **导入路径问题**:
   - 混用 scinteg 和 scinteg_clean
   - sys.path 操作散落各处

3. **接口不一致**:
   - hierarchical_pathway_projector 有两个版本
   - 不同模块期望不同的输入输出格式

4. **测试文件散落**:
   - tests/ 目录
   - 根目录的测试文件
   - scripts/ 目录的测试脚本

### 5. 需要保留的核心价值

1. **算法创新**:
   - 层次化通路投影（解决信息瓶颈）
   - U-Net解码器（改善重建质量）
   - 鲁棒损失函数
   - 批次效应校正

2. **已验证的组件**:
   - 基础的编码器架构
   - GRN预测逻辑
   - 数据预处理流程

### 6. 重构策略

1. **丢弃**: scinteg_clean目录（几乎为空）
2. **重写**: scinteg_v3.py（接口混乱）
3. **标准化**: 所有编码器/解码器/投影器
4. **整合**: 分散的测试文件
5. **统一**: 导入路径和模块结构