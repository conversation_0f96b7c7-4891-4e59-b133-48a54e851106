# ScINTEG v3 - Single-cell Integrative Gene Regulatory Network Inference

A comprehensive framework for inferring gene regulatory networks from single-cell RNA-seq data with pathway-level analysis.

## 🚀 Key Features

- **Hierarchical Pathway Projection**: >99% information preservation through multi-level pathway representation
- **Advanced Decoders**: U-Net architecture for improved reconstruction quality
- **Robust Loss Functions**: Adaptive loss functions for handling outliers
- **Batch Effect Correction**: Built-in batch correction methods
- **Temporal Consistency**: Maintains biological continuity across time points
- **Modular Architecture**: Easy to extend and customize

## 📦 Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/scinteg.git
cd scinteg

# Install in development mode
pip install -e .

# For GPU support
pip install -e ".[gpu]"

# For development
pip install -e ".[dev]"
```

## 🚀 Quick Start

```python
import scinteg
from scinteg.data import load_pbmc3k
from scinteg.models import ScINTEG

# Load data
adata = load_pbmc3k()

# Create model
model = ScINTEG.from_config("configs/default.yaml")

# Train
model.fit(adata, epochs=100)

# Inference
grn = model.infer_grn()
pathway_activity = model.get_pathway_activity()
```

## 📚 Documentation

See the [docs](docs/) directory for detailed documentation.

## 🧪 Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=scinteg

# Run specific test suite
pytest tests/unit/
```

## 📊 Examples

Check the [examples](examples/) directory for:
- Basic usage tutorial
- Advanced configuration
- Custom model components
- Visualization examples

## 🛠️ Development

This project follows a modular architecture:

```
scinteg/
├── core/          # Base classes and interfaces
├── models/        # Model components
│   ├── encoders/  # Cell and gene encoders
│   ├── projectors/# Pathway projectors
│   ├── decoders/  # Expression decoders
│   └── predictors/# GRN predictors
├── data/          # Data loading and preprocessing
├── training/      # Training utilities
└── evaluation/    # Metrics and benchmarks
```

## 📄 License

This project is licensed under the MIT License.

## 📝 Citation

If you use ScINTEG in your research, please cite:

```bibtex
@article{scinteg2024,
  title={ScINTEG: Single-cell Integrative Gene Regulatory Network Inference},
  author={...},
  journal={...},
  year={2024}
}
```

## 🤝 Contributing

We welcome contributions! Please see our [contributing guidelines](CONTRIBUTING.md).

## 📧 Contact

For questions and support, please open an issue on GitHub.