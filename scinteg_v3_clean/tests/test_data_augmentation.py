"""
Tests for data augmentation module.
"""

import pytest
import torch
import numpy as np
from scinteg.data.augmentation import (
    DataAugmenter,
    BatchAwareAugmenter,
    create_augmentation_pipeline
)


class TestDataAugmenter:
    """Test basic data augmentation functionality."""
    
    def test_gaussian_noise(self):
        """Test Gaussian noise augmentation."""
        augmenter = DataAugmenter(noise_level=0.1, augment_prob=1.0)
        
        # Create test data
        expression = torch.randn(32, 100).abs()  # 32 cells, 100 genes
        batch = {'expression': expression}
        
        # Apply augmentation
        augmented = augmenter(batch, augment_type='gaussian')
        
        # Check shape preserved
        assert augmented['expression'].shape == expression.shape
        # Check values changed
        assert not torch.allclose(augmented['expression'], expression)
        # Check correlation still high
        corr = torch.corrcoef(torch.stack([
            expression.flatten(),
            augmented['expression'].flatten()
        ]))[0, 1]
        assert corr > 0.9
    
    def test_dropout(self):
        """Test dropout augmentation."""
        augmenter = DataAugmenter(dropout_rate=0.2, augment_prob=1.0)
        
        expression = torch.ones(50, 200) * 2.0
        batch = {'expression': expression}
        
        augmented = augmenter(batch, augment_type='dropout')
        
        # Check zeros were introduced
        zero_fraction = (augmented['expression'] == 0).float().mean()
        assert 0.15 < zero_fraction < 0.25
        # Non-zero values unchanged
        non_zero_mask = augmented['expression'] != 0
        assert torch.allclose(
            augmented['expression'][non_zero_mask],
            expression[non_zero_mask]
        )
    
    def test_mixup(self):
        """Test mixup augmentation."""
        augmenter = DataAugmenter(mixup_alpha=0.2, augment_prob=1.0)
        
        # Create distinct samples
        expression = torch.zeros(4, 100)
        expression[0, :50] = 1.0  # First sample
        expression[1, 50:] = 1.0  # Second sample
        batch = {'expression': expression}
        
        augmented = augmenter(batch, augment_type='mixup')
        
        # Check mixing occurred
        assert augmented['expression'].shape == expression.shape
        # Values should be between 0 and 1
        assert (augmented['expression'] >= 0).all()
        assert (augmented['expression'] <= 1).all()
    
    def test_cutmix(self):
        """Test CutMix augmentation."""
        augmenter = DataAugmenter(cutmix_alpha=1.0, augment_prob=1.0)
        
        # Create distinct samples
        expression = torch.zeros(4, 100)
        expression[0, :] = 1.0
        expression[1, :] = 2.0
        batch = {'expression': expression}
        
        augmented = augmenter(batch, augment_type='cutmix')
        
        # Check some genes were swapped
        assert augmented['expression'].shape == expression.shape
        # Each sample should have mix of values
        for i in range(4):
            unique_vals = torch.unique(augmented['expression'][i])
            assert len(unique_vals) <= 3  # At most 0, 1, 2
    
    def test_scaling(self):
        """Test scaling augmentation."""
        augmenter = DataAugmenter(scale_range=(0.8, 1.2), augment_prob=1.0)
        
        expression = torch.ones(10, 50) * 100
        batch = {'expression': expression}
        
        augmented = augmenter(batch, augment_type='scale')
        
        # Check values are scaled
        assert augmented['expression'].shape == expression.shape
        assert (augmented['expression'] >= 80).all()
        assert (augmented['expression'] <= 120).all()
    
    def test_augment_probability(self):
        """Test augmentation probability."""
        augmenter = DataAugmenter(augment_prob=0.0)
        
        expression = torch.randn(32, 100)
        batch = {'expression': expression.clone()}
        
        # Should not augment
        augmented = augmenter(batch)
        assert torch.allclose(augmented['expression'], expression)


class TestBatchAwareAugmenter:
    """Test batch-aware augmentation."""
    
    def test_batch_mixup(self):
        """Test cross-batch mixup."""
        augmenter = BatchAwareAugmenter(
            cross_batch_mixup=True,
            mixup_alpha=0.5,
            augment_prob=1.0
        )
        
        # Create data with two batches
        expression = torch.zeros(6, 100)
        expression[:3, :50] = 1.0  # Batch 1 pattern
        expression[3:, 50:] = 1.0  # Batch 2 pattern
        
        batch_labels = torch.tensor([0, 0, 0, 1, 1, 1])
        
        batch = {
            'expression': expression,
            'batch_labels': batch_labels
        }
        
        augmented = augmenter(batch, augment_type='batch_mixup')
        
        # Check mixing occurred between batches
        # Samples should have mixed patterns
        for i in range(6):
            # Should have non-zero values in both gene sets
            first_half = augmented['expression'][i, :50]
            second_half = augmented['expression'][i, 50:]
            assert first_half.sum() > 0 or second_half.sum() > 0
    
    def test_batch_specific_noise(self):
        """Test batch-specific noise."""
        augmenter = BatchAwareAugmenter(
            batch_specific_noise=True,
            noise_level=0.1,
            augment_prob=1.0
        )
        
        # Create data with two batches
        expression = torch.ones(6, 100)
        batch_labels = torch.tensor([0, 0, 0, 1, 1, 1])
        
        batch = {
            'expression': expression,
            'batch_labels': batch_labels
        }
        
        # Set seed for reproducibility
        torch.manual_seed(42)
        augmented = augmenter(batch)
        
        # Check noise was added
        assert not torch.allclose(augmented['expression'], expression)
        
        # Noise should be different between batches
        batch1_noise = augmented['expression'][:3] - expression[:3]
        batch2_noise = augmented['expression'][3:] - expression[3:]
        
        # Variance should be different
        assert abs(batch1_noise.var() - batch2_noise.var()) > 1e-6
    
    def test_fallback_without_batch_labels(self):
        """Test fallback when batch labels missing."""
        augmenter = BatchAwareAugmenter(augment_prob=1.0)
        
        expression = torch.randn(32, 100)
        batch = {'expression': expression}
        
        # Should still work without batch labels
        augmented = augmenter(batch, augment_type='gaussian')
        assert 'expression' in augmented


class TestAugmentationPipeline:
    """Test augmentation pipeline creation."""
    
    def test_create_basic_pipeline(self):
        """Test creating basic augmentation pipeline."""
        config = {
            'noise_level': 0.15,
            'dropout_rate': 0.1,
            'augment_prob': 0.7
        }
        
        augmenter = create_augmentation_pipeline(config, batch_aware=False)
        
        assert isinstance(augmenter, DataAugmenter)
        assert augmenter.noise_level == 0.15
        assert augmenter.dropout_rate == 0.1
        assert augmenter.augment_prob == 0.7
    
    def test_create_batch_aware_pipeline(self):
        """Test creating batch-aware pipeline."""
        config = {
            'cross_batch_mixup': True,
            'batch_specific_noise': True,
            'mixup_alpha': 0.3
        }
        
        augmenter = create_augmentation_pipeline(config, batch_aware=True)
        
        assert isinstance(augmenter, BatchAwareAugmenter)
        assert augmenter.cross_batch_mixup
        assert augmenter.batch_specific_noise
        assert augmenter.mixup_alpha == 0.3


if __name__ == "__main__":
    pytest.main([__file__])