"""
Unit tests for projector modules.
"""

import torch
import pytest
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from scinteg.models.projectors import (
    MaskedProjection,
    StandardPathwayProjector,
    HierarchicalPathwayProjector
)


class TestMaskedProjection:
    """Test MaskedProjection functionality."""
    
    def test_initialization(self):
        """Test masked projection initialization."""
        n_in, n_out = 100, 20
        mask = torch.randint(0, 2, (n_out, n_in)).float()
        
        # Test hard mask
        layer = MaskedProjection(n_in, n_out, mask, soft_mask=False)
        assert layer.in_features == n_in
        assert layer.out_features == n_out
        assert not layer.soft_mask
        
        # Test soft mask
        layer_soft = MaskedProjection(n_in, n_out, mask, soft_mask=True)
        assert layer_soft.soft_mask
        assert hasattr(layer_soft, 'learnable_mask')
    
    def test_forward_pass(self):
        """Test forward pass of masked projection."""
        n_in, n_out, batch_size = 50, 10, 32
        mask = torch.randint(0, 2, (n_out, n_in)).float()
        
        layer = MaskedProjection(n_in, n_out, mask)
        x = torch.randn(batch_size, n_in)
        
        output = layer(x)
        assert output.shape == (batch_size, n_out)
        assert not torch.isnan(output).any()
    
    def test_sparsity_calculation(self):
        """Test sparsity calculation."""
        n_in, n_out = 100, 20
        # Create sparse mask
        mask = torch.zeros(n_out, n_in)
        mask[:, :10] = 1  # Only 10% connections
        
        layer = MaskedProjection(n_in, n_out, mask)
        sparsity = layer.get_sparsity()
        assert 0.89 < sparsity < 0.91  # Should be ~90% sparse


class TestStandardPathwayProjector:
    """Test StandardPathwayProjector functionality."""
    
    def test_initialization(self):
        """Test projector initialization."""
        cell_dim = 64
        n_genes = 1000
        n_pathways = 50
        mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
        
        projector = StandardPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            pathway_gene_mask=mask
        )
        
        assert projector.cell_dim == cell_dim
        assert projector.n_genes == n_genes
        assert projector.n_pathways == n_pathways
    
    def test_forward_pass(self):
        """Test forward pass."""
        batch_size = 16
        cell_dim = 64
        n_genes = 500
        n_pathways = 30
        
        # Create mask with some structure
        mask = torch.zeros(n_pathways, n_genes)
        for i in range(n_pathways):
            # Each pathway connected to ~20 genes
            gene_indices = torch.randperm(n_genes)[:20]
            mask[i, gene_indices] = 1
        
        projector = StandardPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            pathway_gene_mask=mask,
            use_soft_mask=True
        )
        
        # Test input
        z_c = torch.randn(batch_size, cell_dim)
        
        # Forward pass
        z_p = projector(z_c)
        
        assert z_p.shape == (batch_size, n_pathways)
        assert not torch.isnan(z_p).any()
        assert not torch.isinf(z_p).any()
    
    def test_project_method(self):
        """Test project method with full outputs."""
        batch_size = 8
        cell_dim = 32
        n_genes = 200
        n_pathways = 20
        
        mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
        
        projector = StandardPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            pathway_gene_mask=mask
        )
        
        z_c = torch.randn(batch_size, cell_dim)
        outputs = projector.project(z_c, return_intermediate=True)
        
        # Check outputs structure
        assert hasattr(outputs, 'projections')
        assert outputs.projections.shape == (batch_size, n_pathways)
        
        # Check auxiliary outputs
        assert 'pathway_importance' in outputs.auxiliary
        assert 'temperature' in outputs.auxiliary
        assert 'gene_space' in outputs.auxiliary
        assert outputs.auxiliary['gene_space'].shape == (batch_size, n_genes)
    
    def test_pathway_associations(self):
        """Test getting pathway-gene associations."""
        cell_dim = 32
        n_genes = 100
        n_pathways = 10
        
        # Create structured mask
        mask = torch.zeros(n_pathways, n_genes)
        for i in range(n_pathways):
            mask[i, i*10:(i+1)*10] = 1
        
        projector = StandardPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            pathway_gene_mask=mask
        )
        
        associations = projector.get_pathway_gene_associations()
        
        assert len(associations) == n_pathways
        for i in range(n_pathways):
            expected_genes = torch.arange(i*10, (i+1)*10)
            assert torch.equal(associations[i], expected_genes)


class TestHierarchicalPathwayProjector:
    """Test HierarchicalPathwayProjector functionality."""
    
    def test_initialization(self):
        """Test hierarchical projector initialization."""
        cell_dim = 64
        n_genes = 1000
        n_pathways = 50
        n_meta_pathways = 20
        mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
        
        projector = HierarchicalPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            n_meta_pathways=n_meta_pathways,
            pathway_gene_mask=mask
        )
        
        assert projector.n_meta_pathways == n_meta_pathways
        assert projector.get_output_dim() == n_pathways + n_meta_pathways
    
    def test_forward_pass(self):
        """Test forward pass returning tuple."""
        batch_size = 16
        cell_dim = 64
        n_genes = 500
        n_pathways = 40
        n_meta_pathways = 15
        
        mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
        
        projector = HierarchicalPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            n_meta_pathways=n_meta_pathways,
            pathway_gene_mask=mask
        )
        
        z_c = torch.randn(batch_size, cell_dim)
        z_p, z_meta = projector(z_c)
        
        assert z_p.shape == (batch_size, n_pathways)
        assert z_meta.shape == (batch_size, n_meta_pathways)
        assert not torch.isnan(z_p).any()
        assert not torch.isnan(z_meta).any()
    
    def test_project_method(self):
        """Test project method with different configurations."""
        batch_size = 8
        cell_dim = 32
        n_genes = 200
        n_pathways = 25
        n_meta_pathways = 10
        
        mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
        
        projector = HierarchicalPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            n_meta_pathways=n_meta_pathways,
            pathway_gene_mask=mask,
            use_skip_connections=True
        )
        
        z_c = torch.randn(batch_size, cell_dim)
        
        # Test with both levels
        outputs = projector.project(z_c, return_both_levels=True)
        assert outputs.projections.shape == (batch_size, n_pathways + n_meta_pathways)
        
        # Check auxiliary outputs
        assert 'pathway_features' in outputs.auxiliary
        assert 'meta_features' in outputs.auxiliary
        assert 'fusion_weights' in outputs.auxiliary
        
        # Test without skip connections
        projector_no_skip = HierarchicalPathwayProjector(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            n_meta_pathways=n_meta_pathways,
            pathway_gene_mask=mask,
            use_skip_connections=False
        )
        
        outputs_no_skip = projector_no_skip.project(z_c)
        assert 'fusion_weights' not in outputs_no_skip.auxiliary


if __name__ == "__main__":
    # Run tests
    print("Testing MaskedProjection...")
    test_masked = TestMaskedProjection()
    test_masked.test_initialization()
    test_masked.test_forward_pass()
    test_masked.test_sparsity_calculation()
    print("✓ MaskedProjection tests passed")
    
    print("\nTesting StandardPathwayProjector...")
    test_standard = TestStandardPathwayProjector()
    test_standard.test_initialization()
    test_standard.test_forward_pass()
    test_standard.test_project_method()
    test_standard.test_pathway_associations()
    print("✓ StandardPathwayProjector tests passed")
    
    print("\nTesting HierarchicalPathwayProjector...")
    test_hierarchical = TestHierarchicalPathwayProjector()
    test_hierarchical.test_initialization()
    test_hierarchical.test_forward_pass()
    test_hierarchical.test_project_method()
    print("✓ HierarchicalPathwayProjector tests passed")
    
    print("\nAll projector tests passed!")