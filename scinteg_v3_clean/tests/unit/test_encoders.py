"""
Unit tests for encoder modules.
"""

import torch
import pytest
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from scinteg.models.encoders import StandardCellEncoder, StandardGeneEncoder


class TestStandardCellEncoder:
    """Test StandardCellEncoder functionality."""
    
    def test_initialization(self):
        """Test encoder initialization."""
        encoder = StandardCellEncoder(
            input_dim=100,
            hidden_dim=64,
            output_dim=32,
            n_layers=2
        )
        assert encoder is not None
        assert encoder.input_dim == 100
        assert encoder.output_dim == 32
    
    def test_forward_pass(self):
        """Test forward pass."""
        n_cells = 50
        n_genes = 100
        n_edges = 200
        
        # Create encoder
        encoder = StandardCellEncoder(
            input_dim=n_genes,
            hidden_dim=64,
            output_dim=32
        )
        
        # Create dummy data
        x = torch.randn(n_cells, n_genes)
        edge_index = torch.randint(0, n_cells, (2, n_edges))
        
        # Forward pass
        output = encoder(x, edge_index)
        
        # Check output shape
        assert output.shape == (n_cells, 32)
        assert not torch.isnan(output).any()
    
    def test_encode_method(self):
        """Test encode method returns proper structure."""
        encoder = StandardCellEncoder(input_dim=100, output_dim=32)
        
        x = torch.randn(10, 100)
        edge_index = torch.randint(0, 10, (2, 20))
        
        outputs = encoder.encode(x, edge_index)
        
        assert hasattr(outputs, 'embeddings')
        assert outputs.embeddings.shape == (10, 32)
        assert hasattr(outputs, 'auxiliary')
        assert 'n_edges' in outputs.auxiliary


class TestStandardGeneEncoder:
    """Test StandardGeneEncoder functionality."""
    
    def test_initialization(self):
        """Test encoder initialization."""
        encoder = StandardGeneEncoder(
            n_genes=1000,
            feature_dim=64,
            hidden_dim=128,
            output_dim=64,
            n_layers=2
        )
        assert encoder is not None
        assert encoder.n_genes == 1000
        assert encoder.output_dim == 64
    
    def test_forward_pass(self):
        """Test forward pass."""
        n_genes = 100
        n_batch_genes = 20
        n_edges = 50
        
        # Create encoder
        encoder = StandardGeneEncoder(
            n_genes=n_genes,
            feature_dim=32,
            hidden_dim=64,
            output_dim=32
        )
        
        # Create dummy data
        gene_indices = torch.randint(0, n_genes, (n_batch_genes,))
        edge_index = torch.randint(0, n_batch_genes, (2, n_edges))
        
        # Forward pass
        embeddings, attn, importance, global_feat = encoder(gene_indices, edge_index)
        
        # Check outputs
        assert embeddings.shape == (n_batch_genes, 32)
        assert importance.shape == (n_batch_genes, 1)
        assert not torch.isnan(embeddings).any()
    
    def test_biological_priors(self):
        """Test encoder with biological priors."""
        n_genes = 100
        n_pathways = 10
        
        # Create pathway mask
        pathway_mask = torch.zeros(n_pathways, n_genes)
        for i in range(n_pathways):
            pathway_mask[i, i*10:(i+1)*10] = 1
        
        # Create TF indices
        tf_indices = [0, 10, 20, 30]
        
        # Create encoder with priors
        encoder = StandardGeneEncoder(
            n_genes=n_genes,
            tf_indices=tf_indices,
            pathway_mask=pathway_mask,
            output_dim=32
        )
        
        # Test forward pass
        gene_indices = torch.arange(20)
        edge_index = torch.randint(0, 20, (2, 30))
        
        outputs = encoder.encode(gene_indices, edge_index)
        assert outputs.embeddings.shape == (20, 32)


def test_compatibility():
    """Test that encoders work together."""
    # Cell encoder
    cell_encoder = StandardCellEncoder(input_dim=100, output_dim=32)
    
    # Gene encoder  
    gene_encoder = StandardGeneEncoder(n_genes=100, output_dim=32)
    
    # Test data
    x_cell = torch.randn(50, 100)
    cell_edges = torch.randint(0, 50, (2, 100))
    gene_indices = torch.arange(100)
    gene_edges = torch.randint(0, 100, (2, 200))
    
    # Forward passes
    cell_emb = cell_encoder(x_cell, cell_edges)
    gene_emb, _, _, _ = gene_encoder(gene_indices, gene_edges)
    
    # Both should produce compatible embeddings
    assert cell_emb.shape[1] == gene_emb.shape[1]


if __name__ == "__main__":
    # Run tests
    test_cell = TestStandardCellEncoder()
    test_cell.test_initialization()
    test_cell.test_forward_pass()
    test_cell.test_encode_method()
    
    test_gene = TestStandardGeneEncoder()
    test_gene.test_initialization()
    test_gene.test_forward_pass()
    test_gene.test_biological_priors()
    
    test_compatibility()
    
    print("All encoder tests passed!")