"""
Unit tests for decoder modules.
"""

import torch
import pytest
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from scinteg.models.decoders import (
    StandardExpressionDecoder,
    StandardUNetDecoder
)


class TestStandardExpressionDecoder:
    """Test StandardExpressionDecoder functionality."""
    
    def test_initialization(self):
        """Test decoder initialization."""
        n_pathways = 50
        n_genes = 1000
        
        # Test MSE decoder
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            loss_type='mse'
        )
        assert decoder.n_pathways == n_pathways
        assert decoder.n_genes == n_genes
        assert decoder.loss_type == 'mse'
        
        # Test NB decoder
        decoder_nb = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            loss_type='nb'
        )
        assert hasattr(decoder_nb, 'mu_decoder')
        assert hasattr(decoder_nb, 'theta_decoder')
    
    def test_forward_mse(self):
        """Test forward pass with MSE loss."""
        batch_size = 16
        n_pathways = 40
        n_genes = 500
        
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            loss_type='mse'
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        reconstruction = decoder(z_p)
        
        assert reconstruction.shape == (batch_size, n_genes)
        assert not torch.isnan(reconstruction).any()
    
    def test_forward_nb(self):
        """Test forward pass with NB loss."""
        batch_size = 8
        n_pathways = 30
        n_genes = 200
        
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            loss_type='nb'
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        mu, theta = decoder(z_p)
        
        assert mu.shape == (batch_size, n_genes)
        assert theta.shape == (batch_size, n_genes)
        assert (mu >= 0).all()  # Softplus activation
        assert (theta >= 0).all()
    
    def test_gene_modulation(self):
        """Test gene modulation features."""
        batch_size = 4
        n_pathways = 20
        n_genes = 100
        gene_dim = 32
        
        # Test multiplicative modulation
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            gene_embedding_dim=gene_dim,
            use_gene_modulation=True,
            modulation_type='multiplicative'
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        z_g = torch.randn(n_genes, gene_dim)
        
        # Without gene embeddings
        out1 = decoder(z_p)
        # With gene embeddings
        out2 = decoder(z_p, z_g)
        
        assert out1.shape == out2.shape
        # Results should be different with modulation
        assert not torch.allclose(out1, out2)
    
    def test_residual_connection(self):
        """Test input residual connection."""
        batch_size = 4
        n_pathways = 20
        n_genes = 100
        
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            use_input_residual=True
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        x_input = torch.randn(batch_size, n_genes)
        
        out = decoder(z_p, x_input=x_input)
        assert out.shape == (batch_size, n_genes)
    
    def test_decode_method(self):
        """Test decode method with full outputs."""
        batch_size = 4
        n_pathways = 30
        n_genes = 150
        
        decoder = StandardExpressionDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            loss_type='zinb'
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        outputs = decoder.decode(z_p)
        
        assert hasattr(outputs, 'reconstruction')
        assert outputs.reconstruction.shape == (batch_size, n_genes)
        assert 'theta' in outputs.auxiliary
        assert 'pi_logits' in outputs.auxiliary


class TestStandardUNetDecoder:
    """Test StandardUNetDecoder functionality."""
    
    def test_initialization(self):
        """Test U-Net decoder initialization."""
        n_pathways = 50
        n_genes = 1000
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=3
        )
        
        assert decoder.n_pathways == n_pathways
        assert decoder.n_genes == n_genes
        assert decoder.n_levels == 3
        assert len(decoder.encoder_blocks) == 3
    
    def test_forward_pass(self):
        """Test basic forward pass."""
        batch_size = 8
        n_pathways = 40
        n_genes = 500
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=2,
            hidden_dims=[64, 128]
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        reconstruction = decoder(z_p)
        
        assert reconstruction.shape == (batch_size, n_genes)
        assert not torch.isnan(reconstruction).any()
    
    def test_skip_connections(self):
        """Test U-Net with skip connections."""
        batch_size = 4
        n_pathways = 30
        n_genes = 200
        
        # With skip connections
        decoder_skip = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=3,
            use_skip_connections=True
        )
        
        # Without skip connections
        decoder_no_skip = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=3,
            use_skip_connections=False
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        
        out_skip = decoder_skip(z_p)
        out_no_skip = decoder_no_skip(z_p)
        
        assert out_skip.shape == out_no_skip.shape
        # Results should be different
        assert not torch.allclose(out_skip, out_no_skip)
    
    def test_hierarchical_input(self):
        """Test hierarchical input mode."""
        batch_size = 4
        n_pathways = 40
        n_meta_pathways = 15
        n_genes = 300
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            use_hierarchical_input=True,
            n_meta_pathways=n_meta_pathways
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        z_meta = torch.randn(batch_size, n_meta_pathways)
        
        # Test with both inputs
        reconstruction = decoder(z_p, z_meta)
        assert reconstruction.shape == (batch_size, n_genes)
    
    def test_attention_blocks(self):
        """Test U-Net with attention."""
        batch_size = 4
        n_pathways = 30
        n_genes = 150
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=2,
            use_attention=True
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        reconstruction = decoder(z_p)
        
        assert reconstruction.shape == (batch_size, n_genes)
        assert hasattr(decoder, 'bottleneck_attention')
    
    def test_with_pathway_mask(self):
        """Test decoder with pathway-gene mask."""
        batch_size = 4
        n_pathways = 20
        n_genes = 100
        
        # Create structured mask
        mask = torch.zeros(n_pathways, n_genes)
        for i in range(n_pathways):
            # Each pathway connected to specific genes
            start = i * 5
            end = min(start + 10, n_genes)
            mask[i, start:end] = 1
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            pathway_gene_mask=mask,
            n_levels=2
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        reconstruction = decoder(z_p)
        
        assert reconstruction.shape == (batch_size, n_genes)
        assert hasattr(decoder, 'mask')
    
    def test_decode_method(self):
        """Test decode method with full outputs."""
        batch_size = 4
        n_pathways = 25
        n_genes = 125
        
        decoder = StandardUNetDecoder(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=2,
            use_skip_connections=True
        )
        
        z_p = torch.randn(batch_size, n_pathways)
        outputs = decoder.decode(z_p)
        
        assert hasattr(outputs, 'reconstruction')
        assert outputs.reconstruction.shape == (batch_size, n_genes)
        assert hasattr(outputs, 'latent_features')
        assert 'skip_features' in outputs.auxiliary
        assert 'final_features' in outputs.auxiliary


if __name__ == "__main__":
    # Run tests
    print("Testing StandardExpressionDecoder...")
    test_expr = TestStandardExpressionDecoder()
    test_expr.test_initialization()
    test_expr.test_forward_mse()
    test_expr.test_forward_nb()
    test_expr.test_gene_modulation()
    test_expr.test_residual_connection()
    test_expr.test_decode_method()
    print("✓ StandardExpressionDecoder tests passed")
    
    print("\nTesting StandardUNetDecoder...")
    test_unet = TestStandardUNetDecoder()
    test_unet.test_initialization()
    test_unet.test_forward_pass()
    test_unet.test_skip_connections()
    test_unet.test_hierarchical_input()
    test_unet.test_attention_blocks()
    test_unet.test_with_pathway_mask()
    test_unet.test_decode_method()
    print("✓ StandardUNetDecoder tests passed")
    
    print("\nAll decoder tests passed!")