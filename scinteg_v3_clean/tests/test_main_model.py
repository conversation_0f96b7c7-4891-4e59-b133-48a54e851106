"""
Test for the main ScINTEG v3 model.
"""

import torch
import pytest
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.models.scinteg import ScINTEGv3


def test_scinteg_v3_basic():
    """Test basic ScINTEG v3 functionality."""
    # Setup
    n_cells = 50
    n_genes = 100
    n_pathways = 20
    batch_size = 16
    
    # Create pathway mask
    pathway_mask = torch.zeros(n_pathways, n_genes)
    for i in range(n_pathways):
        # Each pathway connected to ~20 genes
        start = i * 10
        end = min(start + 20, n_genes)
        pathway_mask[i, start:end] = 1
    
    # Create model
    model = ScINTEGv3(
        n_cells=n_cells,
        n_genes=n_genes,
        n_pathways=n_pathways,
        pathway_gene_mask=pathway_mask,
        cell_embedding_dim=64,
        gene_embedding_dim=32,
        cell_hidden_dim=128,
        gene_hidden_dim=64,
        reconstruction_loss_type='mse',
        dropout=0.1,
        grn_predictor_config={
            'mode': 'embedding_similarity',  # Use embedding similarity instead of attention
            'top_k_edges_per_gene': 5,
            'use_mlp': False,  # Simple cosine similarity to save memory
            'dynamic_score_mode': None  # Disable dynamic scores for testing
        }
    )
    
    # Create dummy data
    x = torch.randn(batch_size, n_genes)
    cell_graph = torch.randint(0, batch_size, (2, 100))
    gene_graph = torch.randint(0, n_genes, (2, 200))
    
    # Forward pass
    outputs = model(x, cell_graph, gene_graph, return_intermediates=True)
    
    # Check outputs
    assert 'reconstruction' in outputs
    assert outputs['reconstruction'].shape == (batch_size, n_genes)
    
    assert 'cell_embeddings' in outputs
    assert outputs['cell_embeddings'].shape == (batch_size, 64)
    
    assert 'gene_embeddings' in outputs
    assert outputs['gene_embeddings'].shape == (n_genes, 32)
    
    assert 'pathway_features' in outputs
    assert outputs['pathway_features'].shape == (batch_size, n_pathways)
    
    assert 'grn_edge_index' in outputs
    assert 'grn_edge_weight' in outputs
    
    print("✓ Basic ScINTEG v3 test passed")


def test_scinteg_v3_hierarchical():
    """Test ScINTEG v3 with hierarchical pathways."""
    # Setup
    n_cells = 30
    n_genes = 50
    n_pathways = 15
    n_meta_pathways = 5
    batch_size = 8
    
    # Create pathway mask
    pathway_mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
    
    # Create model with hierarchical pathways
    model = ScINTEGv3(
        n_cells=n_cells,
        n_genes=n_genes,
        n_pathways=n_pathways,
        pathway_gene_mask=pathway_mask,
        n_meta_pathways=n_meta_pathways,
        use_hierarchical_pathways=True,
        cell_embedding_dim=32,
        gene_embedding_dim=32,
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None,
            'use_mlp': False
        }
    )
    
    # Create dummy data
    x = torch.randn(batch_size, n_genes)
    cell_graph = torch.randint(0, batch_size, (2, 50))
    gene_graph = torch.randint(0, n_genes, (2, 100))
    
    # Forward pass
    outputs = model(x, cell_graph, gene_graph, return_intermediates=True)
    
    # Check hierarchical outputs
    assert 'meta_pathway_features' in outputs
    assert outputs['meta_pathway_features'] is not None
    assert outputs['meta_pathway_features'].shape == (batch_size, n_meta_pathways)
    
    print("✓ Hierarchical ScINTEG v3 test passed")


def test_scinteg_v3_unet_decoder():
    """Test ScINTEG v3 with U-Net decoder."""
    # Setup
    n_cells = 30
    n_genes = 50
    n_pathways = 15
    batch_size = 8
    
    # Create pathway mask
    pathway_mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
    
    # Create model with U-Net decoder
    model = ScINTEGv3(
        n_cells=n_cells,
        n_genes=n_genes,
        n_pathways=n_pathways,
        pathway_gene_mask=pathway_mask,
        use_unet_decoder=True,
        decoder_config={'n_levels': 2},
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None,
            'use_mlp': False
        }
    )
    
    # Create dummy data
    x = torch.randn(batch_size, n_genes)
    cell_graph = torch.randint(0, batch_size, (2, 30))
    gene_graph = torch.randint(0, n_genes, (2, 80))
    
    # Forward pass
    outputs = model(x, cell_graph, gene_graph, return_intermediates=True)
    
    # Check outputs
    assert outputs['reconstruction'].shape == (batch_size, n_genes)
    
    print("✓ U-Net decoder ScINTEG v3 test passed")


def test_scinteg_v3_loss_computation():
    """Test loss computation."""
    # Setup
    n_cells = 30
    n_genes = 50
    n_pathways = 15
    batch_size = 8
    
    # Create pathway mask
    pathway_mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
    
    # Create model
    model = ScINTEGv3(
        n_cells=n_cells,
        n_genes=n_genes,
        n_pathways=n_pathways,
        pathway_gene_mask=pathway_mask,
        reconstruction_loss_type='mse',
        cell_embedding_dim=16,
        gene_embedding_dim=16,
        dropout=0.0,  # No dropout for testing
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None,
            'use_mlp': False,
            'top_k_edges_per_gene': 3
        }
    )
    
    # Create dummy data
    x = torch.randn(batch_size, n_genes) * 0.1  # Small values to avoid NaN
    cell_graph = torch.randint(0, batch_size, (2, 20))
    gene_graph = torch.randint(0, n_genes, (2, 50))
    
    # Forward pass
    outputs = model(x, cell_graph, gene_graph, return_intermediates=True)
    
    # Compute loss
    loss = model.compute_loss(outputs, x)
    
    assert isinstance(loss, torch.Tensor)
    assert loss.numel() == 1
    
    # Debug NaN issue
    if torch.isnan(loss):
        print(f"Loss is NaN!")
        print(f"Reconstruction: {outputs['reconstruction'].mean():.4f}, std: {outputs['reconstruction'].std():.4f}")
        print(f"Has NaN in reconstruction: {torch.isnan(outputs['reconstruction']).any()}")
        print(f"GRN edges: {outputs['grn_edge_weight'].shape[0] if 'grn_edge_weight' in outputs else 0}")
        
    assert not torch.isnan(loss)
    
    # Test with component return
    try:
        loss, components = model.compute_loss(outputs, x, return_components=True)
    except Exception as e:
        print(f"Error computing loss with components: {e}")
        print(f"Outputs keys: {outputs.keys()}")
        raise
    
    assert 'reconstruction' in components
    assert 'grn_regularization' in components
    assert 'pathway_sparsity' in components
    
    print("✓ Loss computation test passed")


def test_scinteg_v3_config():
    """Test configuration save/load."""
    # Setup
    n_cells = 30
    n_genes = 50
    n_pathways = 15
    
    # Create pathway mask
    pathway_mask = torch.randint(0, 2, (n_pathways, n_genes)).float()
    
    # Create model
    model1 = ScINTEGv3(
        n_cells=n_cells,
        n_genes=n_genes,
        n_pathways=n_pathways,
        pathway_gene_mask=pathway_mask,
        cell_embedding_dim=48,
        gene_embedding_dim=24
    )
    
    # Get config
    config = model1.get_model_config()
    
    # Create new model from config
    model2 = ScINTEGv3.from_config(config, pathway_mask)
    
    # Check dimensions match
    assert model2.n_cells == n_cells
    assert model2.n_genes == n_genes
    assert model2.n_pathways == n_pathways
    assert model2.cell_embedding_dim == 48
    assert model2.gene_embedding_dim == 24
    
    print("✓ Configuration test passed")


if __name__ == "__main__":
    test_scinteg_v3_basic()
    test_scinteg_v3_hierarchical()
    test_scinteg_v3_unet_decoder()
    test_scinteg_v3_loss_computation()
    test_scinteg_v3_config()
    
    print("\n✅ All ScINTEG v3 tests passed!")