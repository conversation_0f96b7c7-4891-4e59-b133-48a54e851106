"""
Integration tests for ScINTEG v3.

This module tests the complete data flow through the model,
ensuring all components work together correctly.
"""

import torch
import pytest
import sys
import os
import numpy as np

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.models.scinteg import ScINTEGv3


def create_synthetic_data(n_cells=50, n_genes=100, n_pathways=20, batch_size=16):
    """Create synthetic single-cell data for testing."""
    # Create expression matrix with some structure
    np.random.seed(42)
    
    # Create cell types (3 types)
    n_types = 3
    cell_types = np.random.randint(0, n_types, n_cells)
    
    # Create type-specific expression patterns
    type_signatures = np.random.randn(n_types, n_genes) * 2
    
    # Generate expression data
    expression = np.zeros((n_cells, n_genes))
    for i in range(n_cells):
        base_expr = type_signatures[cell_types[i]]
        noise = np.random.randn(n_genes) * 0.5
        expression[i] = np.maximum(0, base_expr + noise)
    
    # Create pathway mask with biological structure
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = n_genes // n_pathways + 5
    
    for i in range(n_pathways):
        # Each pathway has some unique and some shared genes
        start = i * (n_genes // n_pathways)
        unique_genes = list(range(start, min(start + genes_per_pathway // 2, n_genes)))
        shared_genes = np.random.choice(n_genes, genes_per_pathway // 2, replace=False)
        pathway_genes = unique_genes + list(shared_genes)
        pathway_mask[i, pathway_genes[:genes_per_pathway]] = 1
    
    # Create graphs based on similarity
    expression_tensor = torch.FloatTensor(expression)
    
    # Cell graph: k-NN based on expression similarity
    cell_similarity = torch.mm(expression_tensor, expression_tensor.t())
    k = 5
    _, cell_knn = torch.topk(cell_similarity, k+1, dim=1)
    cell_edges = []
    for i in range(n_cells):
        for j in range(1, k+1):  # Skip self
            cell_edges.append([i, cell_knn[i, j]])
    cell_graph = torch.tensor(cell_edges).t()
    
    # Gene graph: co-expression network
    gene_corr = torch.mm(expression_tensor.t(), expression_tensor)
    gene_corr = gene_corr / n_cells
    threshold = torch.quantile(gene_corr.flatten(), 0.95)
    gene_edges = torch.nonzero(gene_corr > threshold, as_tuple=False)
    gene_graph = gene_edges[gene_edges[:, 0] != gene_edges[:, 1]].t()
    
    # Sample batch
    batch_indices = np.random.choice(n_cells, batch_size, replace=False)
    batch_expression = expression_tensor[batch_indices]
    
    # Adjust cell graph for batch
    batch_cell_edges = []
    for edge in cell_graph.t():
        if edge[0] < batch_size and edge[1] < batch_size:
            batch_cell_edges.append(edge.tolist())
    
    if batch_cell_edges:
        batch_cell_graph = torch.tensor(batch_cell_edges).t()
    else:
        # Create minimal graph if no edges
        batch_cell_graph = torch.tensor([[0, 1], [1, 0]]).t()
    
    return {
        'expression': batch_expression,
        'cell_graph': batch_cell_graph,
        'gene_graph': gene_graph,
        'pathway_mask': pathway_mask,
        'cell_types': cell_types[batch_indices]
    }


def test_integration_basic_workflow():
    """Test basic end-to-end workflow."""
    # Create synthetic data
    data = create_synthetic_data(n_cells=50, n_genes=80, n_pathways=15, batch_size=16)
    
    # Create model
    model = ScINTEGv3(
        n_cells=50,
        n_genes=80,
        n_pathways=15,
        pathway_gene_mask=data['pathway_mask'],
        cell_embedding_dim=32,
        gene_embedding_dim=32,
        dropout=0.1,
        reconstruction_loss_type='mse',
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None,
            'use_mlp': False,
            'top_k_edges_per_gene': 3
        }
    )
    
    # Forward pass
    outputs = model(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    
    # Check outputs
    assert 'reconstruction' in outputs
    assert outputs['reconstruction'].shape == data['expression'].shape
    
    # Check embeddings
    assert outputs['cell_embeddings'].shape == (16, 32)
    assert outputs['gene_embeddings'].shape == (80, 32)
    
    # Check pathway features
    assert outputs['pathway_features'].shape == (16, 15)
    
    # Check GRN
    assert 'grn_edge_index' in outputs
    assert 'grn_edge_weight' in outputs
    
    # Compute loss
    loss = model.compute_loss(outputs, data['expression'])
    assert not torch.isnan(loss)
    assert loss > 0
    
    print("✓ Basic workflow test passed")


def test_integration_gradient_flow():
    """Test gradient flow through the model."""
    # Create synthetic data
    data = create_synthetic_data(n_cells=30, n_genes=50, n_pathways=10, batch_size=8)
    
    # Create model with dynamic scores enabled to test all components
    model = ScINTEGv3(
        n_cells=30,
        n_genes=50,
        n_pathways=10,
        pathway_gene_mask=data['pathway_mask'],
        cell_embedding_dim=16,
        gene_embedding_dim=16,
        dropout=0.0,  # No dropout for gradient testing
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': 'multiply_weight'  # Enable to test importance_head gradients
        }
    )
    
    # Enable gradients
    model.train()
    
    # Forward pass
    outputs = model(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    
    # Compute loss
    loss = model.compute_loss(outputs, data['expression'])
    
    # Backward pass
    loss.backward()
    
    # Check gradients exist
    params_with_grad = 0
    params_without_grad = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            if param.grad is not None:
                params_with_grad += 1
                assert not torch.isnan(param.grad).any(), f"NaN gradient for {name}"
                # Check gradient magnitude is reasonable
                grad_norm = param.grad.norm().item()
                assert grad_norm < 1000, f"Gradient explosion for {name}: {grad_norm}"
            else:
                params_without_grad.append(name)
    
    # Some components may not have gradients if their outputs aren't used
    # This is expected for importance_head modules when not used in loss
    expected_no_grad = ['importance_head']
    for param_name in params_without_grad:
        has_expected = any(expected in param_name for expected in expected_no_grad)
        assert has_expected, f"Unexpected parameter without gradient: {param_name}"
    
    # Ensure most parameters have gradients
    assert params_with_grad > len(params_without_grad), \
        f"Too few parameters with gradients: {params_with_grad} vs {len(params_without_grad)}"
    
    print("✓ Gradient flow test passed")


def test_integration_training_step():
    """Test a complete training step."""
    # Create synthetic data
    data = create_synthetic_data(n_cells=30, n_genes=50, n_pathways=10, batch_size=8)
    
    # Create model
    model = ScINTEGv3(
        n_cells=30,
        n_genes=50,
        n_pathways=10,
        pathway_gene_mask=data['pathway_mask'],
        cell_embedding_dim=16,
        gene_embedding_dim=16,
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None
        }
    )
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    # Training mode
    model.train()
    
    # Store initial loss
    with torch.no_grad():
        outputs_init = model(
            data['expression'],
            data['cell_graph'],
            data['gene_graph'],
            return_intermediates=True
        )
        loss_init = model.compute_loss(outputs_init, data['expression'])
    
    # Training step
    optimizer.zero_grad()
    outputs = model(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    loss = model.compute_loss(outputs, data['expression'])
    loss.backward()
    optimizer.step()
    
    # Check loss decreased (or at least didn't increase much)
    with torch.no_grad():
        outputs_final = model(
            data['expression'],
            data['cell_graph'],
            data['gene_graph'],
            return_intermediates=True
        )
        loss_final = model.compute_loss(outputs_final, data['expression'])
    
    # Loss should generally decrease, but allow small increase due to randomness
    assert loss_final < loss_init * 1.1, f"Loss increased: {loss_init:.4f} -> {loss_final:.4f}"
    
    print("✓ Training step test passed")


def test_integration_hierarchical_pathways():
    """Test integration with hierarchical pathways."""
    # Create synthetic data
    data = create_synthetic_data(n_cells=30, n_genes=50, n_pathways=15, batch_size=8)
    
    # Create model with hierarchical pathways
    model = ScINTEGv3(
        n_cells=30,
        n_genes=50,
        n_pathways=15,
        n_meta_pathways=5,
        pathway_gene_mask=data['pathway_mask'],
        use_hierarchical_pathways=True,
        cell_embedding_dim=16,
        gene_embedding_dim=16,
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'dynamic_score_mode': None
        }
    )
    
    # Forward pass
    outputs = model(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    
    # Check hierarchical outputs
    assert outputs['meta_pathway_features'] is not None
    assert outputs['meta_pathway_features'].shape == (8, 5)
    
    # Check reconstruction
    assert outputs['reconstruction'].shape == data['expression'].shape
    
    print("✓ Hierarchical pathways integration test passed")


def test_integration_different_losses():
    """Test integration with different loss types."""
    data = create_synthetic_data(n_cells=30, n_genes=50, n_pathways=10, batch_size=8)
    
    # Test MSE loss
    model_mse = ScINTEGv3(
        n_cells=30,
        n_genes=50,
        n_pathways=10,
        pathway_gene_mask=data['pathway_mask'],
        reconstruction_loss_type='mse',
        cell_embedding_dim=16,
        gene_embedding_dim=16
    )
    
    outputs_mse = model_mse(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    loss_mse = model_mse.compute_loss(outputs_mse, data['expression'])
    assert not torch.isnan(loss_mse)
    
    # Test NB loss
    model_nb = ScINTEGv3(
        n_cells=30,
        n_genes=50,
        n_pathways=10,
        pathway_gene_mask=data['pathway_mask'],
        reconstruction_loss_type='nb',
        cell_embedding_dim=16,
        gene_embedding_dim=16
    )
    
    outputs_nb = model_nb(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    loss_nb = model_nb.compute_loss(outputs_nb, data['expression'])
    assert not torch.isnan(loss_nb)
    
    print("✓ Different losses integration test passed")


def test_integration_edge_cases():
    """Test edge cases and robustness."""
    # Very small dataset
    data_small = create_synthetic_data(n_cells=10, n_genes=20, n_pathways=5, batch_size=4)
    
    model_small = ScINTEGv3(
        n_cells=10,
        n_genes=20,
        n_pathways=5,
        pathway_gene_mask=data_small['pathway_mask'],
        cell_embedding_dim=8,
        gene_embedding_dim=8
    )
    
    outputs_small = model_small(
        data_small['expression'],
        data_small['cell_graph'],
        data_small['gene_graph'],
        return_intermediates=True
    )
    
    loss_small = model_small.compute_loss(outputs_small, data_small['expression'])
    assert not torch.isnan(loss_small)
    
    # Sparse pathway mask
    sparse_mask = torch.zeros(5, 20)
    sparse_mask[0, [0, 1]] = 1
    sparse_mask[1, [5, 6]] = 1
    sparse_mask[2, [10, 11]] = 1
    sparse_mask[3, [15, 16]] = 1
    sparse_mask[4, [18, 19]] = 1
    
    model_sparse = ScINTEGv3(
        n_cells=10,
        n_genes=20,
        n_pathways=5,
        pathway_gene_mask=sparse_mask,
        cell_embedding_dim=8,
        gene_embedding_dim=8
    )
    
    outputs_sparse = model_sparse(
        data_small['expression'],
        data_small['cell_graph'],
        data_small['gene_graph'],
        return_intermediates=True
    )
    
    loss_sparse = model_sparse.compute_loss(outputs_sparse, data_small['expression'])
    assert not torch.isnan(loss_sparse)
    
    print("✓ Edge cases test passed")


if __name__ == "__main__":
    test_integration_basic_workflow()
    test_integration_gradient_flow()
    test_integration_training_step()
    test_integration_hierarchical_pathways()
    test_integration_different_losses()
    test_integration_edge_cases()
    
    print("\n✅ All integration tests passed!")