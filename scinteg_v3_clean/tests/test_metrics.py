"""
Tests for evaluation metrics module.
"""

import pytest
import torch
import numpy as np
from sklearn.datasets import make_blobs

from scinteg.training.metrics import IntegrationMetrics, compute_integration_metrics


class TestIntegrationMetrics:
    """Test integration metrics functionality."""
    
    def setup_method(self):
        """Setup test data."""
        # Create synthetic data with clear structure
        np.random.seed(42)
        
        # Generate 3 cell types in 2D space
        centers = [[0, 0], [5, 5], [-3, 3]]
        self.embeddings, self.cell_types = make_blobs(
            n_samples=300, centers=centers, n_features=2, 
            cluster_std=1.0, random_state=42
        )
        
        # Create batch labels (2 batches)
        self.batch_labels = np.random.choice([0, 1], size=300)
        
        # Create cluster labels (similar to cell types but with some noise)
        self.cluster_labels = self.cell_types.copy()
        # Add some noise to clustering
        noise_indices = np.random.choice(300, size=30, replace=False)
        self.cluster_labels[noise_indices] = np.random.choice(3, size=30)
        
        # Create original embeddings (slightly different)
        self.original_embeddings = self.embeddings + np.random.normal(0, 0.1, self.embeddings.shape)
        
        self.metrics_calculator = IntegrationMetrics(k_neighbors=15)
    
    def test_batch_correction_metrics(self):
        """Test batch correction metrics computation."""
        metrics = self.metrics_calculator.compute_batch_correction_metrics(
            self.embeddings, self.batch_labels
        )
        
        # Check that all expected metrics are present
        expected_metrics = [
            'batch_mixing_entropy', 'batch_silhouette', 'batch_correction_score',
            'kbet_score', 'lisi_batch'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
        
        # Check reasonable ranges
        assert 0 <= metrics['batch_mixing_entropy'] <= 5
        assert -1 <= metrics['batch_silhouette'] <= 1
        assert 0 <= metrics['batch_correction_score'] <= 2
        assert 0 <= metrics['kbet_score'] <= 1
        assert 1 <= metrics['lisi_batch'] <= 10
    
    def test_celltype_metrics(self):
        """Test cell type metrics computation."""
        metrics = self.metrics_calculator.compute_celltype_metrics(
            self.embeddings, self.cell_types, self.batch_labels
        )
        
        expected_metrics = [
            'celltype_silhouette', 'lisi_celltype', 'neighborhood_purity',
            'cross_batch_celltype_consistency'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
        
        # Cell type separation should be good for our synthetic data
        assert metrics['celltype_silhouette'] > 0.3
        assert metrics['neighborhood_purity'] > 0.5
    
    def test_clustering_metrics(self):
        """Test clustering metrics computation."""
        metrics = self.metrics_calculator.compute_clustering_metrics(
            self.embeddings, self.cluster_labels, self.cell_types
        )
        
        expected_metrics = [
            'cluster_silhouette', 'calinski_harabasz', 'davies_bouldin',
            'adjusted_rand_index', 'normalized_mutual_info',
            'homogeneity', 'completeness', 'v_measure'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
        
        # Check reasonable ranges
        assert -1 <= metrics['cluster_silhouette'] <= 1
        assert metrics['calinski_harabasz'] >= 0
        assert metrics['davies_bouldin'] >= 0
        assert 0 <= metrics['adjusted_rand_index'] <= 1
        assert 0 <= metrics['normalized_mutual_info'] <= 1
    
    def test_integration_metrics(self):
        """Test integration quality metrics computation."""
        metrics = self.metrics_calculator.compute_integration_metrics(
            self.embeddings, self.batch_labels, self.cell_types
        )
        
        expected_metrics = [
            'integration_score', 'celltype_separation',
            'graph_connectivity', 'mixing_metric'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
        
        # Integration score should be reasonable
        assert 0 <= metrics['integration_score'] <= 5
        assert 0 <= metrics['graph_connectivity'] <= 1
        assert 0 <= metrics['mixing_metric'] <= 1
    
    def test_conservation_metrics(self):
        """Test biological conservation metrics computation."""
        metrics = self.metrics_calculator.compute_conservation_metrics(
            self.original_embeddings, self.embeddings, self.cell_types
        )
        
        expected_metrics = [
            'neighborhood_preservation', 'distance_correlation',
            'procrustes_distance', 'celltype_preservation'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
        
        # Since our integrated embeddings are very similar to original,
        # conservation should be high (but be more lenient with thresholds)
        assert metrics['neighborhood_preservation'] > 0.5
        assert metrics['distance_correlation'] > 0.5
        assert metrics['procrustes_distance'] <= 1.0  # Should be between 0 and 1
    
    def test_compute_all_metrics(self):
        """Test computing all metrics together."""
        metrics = self.metrics_calculator.compute_all_metrics(
            embeddings=self.embeddings,
            batch_labels=self.batch_labels,
            cell_type_labels=self.cell_types,
            cluster_labels=self.cluster_labels,
            original_embeddings=self.original_embeddings
        )
        
        # Should have metrics from all categories
        assert len(metrics) >= 20  # Expect at least 20 different metrics
        
        # Check some key metrics are present
        key_metrics = [
            'batch_mixing_entropy', 'celltype_silhouette', 'integration_score',
            'adjusted_rand_index', 'neighborhood_preservation'
        ]
        
        for metric in key_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
            assert not np.isnan(metrics[metric])
    
    def test_with_torch_tensors(self):
        """Test metrics computation with PyTorch tensors."""
        embeddings_tensor = torch.FloatTensor(self.embeddings)
        original_tensor = torch.FloatTensor(self.original_embeddings)
        
        metrics = self.metrics_calculator.compute_all_metrics(
            embeddings=embeddings_tensor,
            batch_labels=self.batch_labels,
            cell_type_labels=self.cell_types,
            original_embeddings=original_tensor
        )
        
        assert len(metrics) > 0
        for key, value in metrics.items():
            assert isinstance(value, (int, float, np.integer, np.floating)), f"Metric {key} has type {type(value)}"
            assert not np.isnan(value), f"Metric {key} is NaN"
    
    def test_with_single_batch(self):
        """Test metrics with single batch (should handle gracefully)."""
        single_batch_labels = np.zeros(300)  # All same batch
        
        metrics = self.metrics_calculator.compute_batch_correction_metrics(
            self.embeddings, single_batch_labels
        )
        
        # Should not crash and return reasonable values
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
    
    def test_with_single_celltype(self):
        """Test metrics with single cell type (should handle gracefully)."""
        single_celltype_labels = np.zeros(300)  # All same cell type
        
        metrics = self.metrics_calculator.compute_celltype_metrics(
            self.embeddings, single_celltype_labels, self.batch_labels
        )
        
        # Should not crash and return reasonable values
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
    
    def test_error_handling(self):
        """Test error handling with problematic data."""
        # Test with very small dataset
        small_embeddings = self.embeddings[:5]
        small_batch_labels = self.batch_labels[:5]
        
        metrics = self.metrics_calculator.compute_batch_correction_metrics(
            small_embeddings, small_batch_labels
        )
        
        # Should return default values instead of crashing
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
        
        # Test with NaN values
        nan_embeddings = self.embeddings.copy()
        nan_embeddings[0, 0] = np.nan
        
        metrics = self.metrics_calculator.compute_batch_correction_metrics(
            nan_embeddings, self.batch_labels
        )
        
        # Should handle NaN gracefully
        assert isinstance(metrics, dict)
    
    def test_different_k_neighbors(self):
        """Test metrics with different k_neighbors values."""
        for k in [5, 10, 20]:
            calculator = IntegrationMetrics(k_neighbors=k)
            metrics = calculator.compute_batch_correction_metrics(
                self.embeddings, self.batch_labels
            )
            
            assert isinstance(metrics, dict)
            assert len(metrics) > 0
            
            # Values might be different but should be valid
            for value in metrics.values():
                assert isinstance(value, (int, float))
                assert not np.isnan(value)


class TestConvenienceFunction:
    """Test the convenience function."""
    
    def test_compute_integration_metrics_function(self):
        """Test the convenience function for computing integration metrics."""
        # Create simple test data
        embeddings = np.random.randn(100, 10)
        batch_labels = np.random.choice([0, 1], 100)
        cell_type_labels = np.random.choice([0, 1, 2], 100)
        
        metrics = compute_integration_metrics(
            embeddings=embeddings,
            batch_labels=batch_labels,
            cell_type_labels=cell_type_labels,
            k_neighbors=10
        )
        
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
        
        # Check some expected metrics
        expected_metrics = [
            'batch_mixing_entropy', 'celltype_silhouette', 'integration_score'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
    
    def test_with_minimal_inputs(self):
        """Test convenience function with minimal inputs."""
        embeddings = np.random.randn(50, 5)
        batch_labels = np.random.choice([0, 1], 50)
        
        metrics = compute_integration_metrics(
            embeddings=embeddings,
            batch_labels=batch_labels
        )
        
        assert isinstance(metrics, dict)
        assert len(metrics) > 0
        
        # Should have batch metrics at minimum
        assert 'batch_mixing_entropy' in metrics
        assert 'integration_score' in metrics


if __name__ == "__main__":
    pytest.main([__file__])