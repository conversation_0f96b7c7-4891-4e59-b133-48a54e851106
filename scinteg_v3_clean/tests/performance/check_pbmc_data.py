"""
Check PBMC3k data format and preprocessing state.
"""

import scanpy as sc
import numpy as np

# Load data
print("Loading PBMC3k data...")
adata = sc.read_h5ad("/mnt/f/文献汇报/gyd_developing/scinteg_0725/data/pbmc3k_processed.h5ad")

print(f"\nData shape: {adata.shape}")
print(f"Data type: {type(adata.X)}")

# Check if data is already processed
print("\nChecking data state:")
print(f"Contains negative values: {(adata.X < 0).any()}")
print(f"Value range: [{adata.X.min():.3f}, {adata.X.max():.3f}]")

# Check adata attributes
print("\n.layers available:", list(adata.layers.keys()) if adata.layers else "None")
print(".raw available:", adata.raw is not None)

# Check .uns for processing info
if 'log1p' in adata.uns:
    print("Data is marked as log1p transformed")

# Check if we can find raw counts
if adata.raw is not None:
    print(f"\nRaw data shape: {adata.raw.shape}")
    print(f"Raw data range: [{adata.raw.X.min():.3f}, {adata.raw.X.max():.3f}]")
    print(f"Raw contains negative: {(adata.raw.X < 0).any()}")

# Check for count layer
if 'counts' in adata.layers:
    counts = adata.layers['counts']
    print(f"\nCounts layer range: [{counts.min():.3f}, {counts.max():.3f}]")
    print(f"Counts contains negative: {(counts < 0).any()}")

# Analyze the distribution
print("\nValue distribution analysis:")
print(f"Values at 0: {(adata.X == 0).sum()}")
print(f"Values < 0: {(adata.X < 0).sum()}")
print(f"Values > 0 and < 1: {((adata.X > 0) & (adata.X < 1)).sum()}")
print(f"Values >= 1: {(adata.X >= 1).sum()}")

# Check if it looks like normalized + log data
unique_vals = np.unique(adata.X)[:20]
print(f"\nFirst 20 unique values: {unique_vals}")

# Summary
print("\n" + "="*50)
print("SUMMARY:")
if (adata.X < 0).any():
    print("⚠️  Data contains negative values - likely already normalized/scaled")
    print("   Do NOT apply log1p transformation")
    print("   Consider using the data as-is or applying only scaling")
else:
    print("✓ Data contains only non-negative values")
    if adata.X.max() > 100:
        print("   Data appears to be raw counts - normalization and log1p recommended")
    else:
        print("   Data might already be normalized - check before applying log1p")