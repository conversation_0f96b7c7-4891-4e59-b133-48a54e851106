"""
Debug script to identify NaN issues in ScINTEG with real data.
"""

import torch
import numpy as np
import scanpy as sc
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset
from scinteg.data.utils import create_pathway_mask_from_gmt

# Load data
print("Loading PBMC3k data...")
adata = sc.read_h5ad("/mnt/f/文献汇报/gyd_developing/scinteg_0725/data/pbmc3k_processed.h5ad")
print(f"Data shape: {adata.shape}")

# Check data properties
print("\nData properties:")
print(f"Type of X: {type(adata.X)}")
print(f"Data range: [{adata.X.min():.3f}, {adata.X.max():.3f}]")
print(f"Contains NaN: {np.isnan(adata.X).any()}")
print(f"Contains Inf: {np.isinf(adata.X).any()}")
print(f"Sparsity: {1 - np.count_nonzero(adata.X) / (adata.X.shape[0] * adata.X.shape[1]):.1%}")

# Check expression distribution
expression_sum = adata.X.sum(axis=1)
print(f"\nExpression per cell:")
print(f"  Min: {expression_sum.min():.3f}")
print(f"  Max: {expression_sum.max():.3f}")
print(f"  Mean: {expression_sum.mean():.3f}")
print(f"  Zero cells: {(expression_sum == 0).sum()}")

# Load pathways
print("\nLoading pathways...")
pathway_mask, pathway_names = create_pathway_mask_from_gmt(
    "/mnt/f/文献汇报/gyd_developing/scinteg_0725/prior_data/msigdb/human/reactome.gmt",
    adata.var_names.tolist(),
    min_genes=10,
    max_genes=500
)
print(f"Loaded {len(pathway_names)} pathways")

# Create minimal dataset
print("\nCreating dataset...")
expression = torch.FloatTensor(adata.X)
print(f"Expression tensor shape: {expression.shape}")
print(f"Contains NaN after conversion: {torch.isnan(expression).any()}")

# Test normalization
print("\nTesting normalization...")
library_sizes = expression.sum(dim=1, keepdim=True)
print(f"Library sizes range: [{library_sizes.min():.3f}, {library_sizes.max():.3f}]")
print(f"Zero library sizes: {(library_sizes == 0).sum()}")

# Normalize
non_zero_mask = (library_sizes > 0).squeeze()
if non_zero_mask.any():
    median_size = library_sizes[non_zero_mask].median()
    normalized = expression.clone()
    normalized[non_zero_mask] = expression[non_zero_mask] / library_sizes[non_zero_mask] * median_size
    print(f"After normalization - contains NaN: {torch.isnan(normalized).any()}")
    print(f"Normalized range: [{normalized.min():.3f}, {normalized.max():.3f}]")
    
    # Log transform
    log_expr = torch.log1p(normalized)
    print(f"After log transform - contains NaN: {torch.isnan(log_expr).any()}")
    print(f"Log range: [{log_expr.min():.3f}, {log_expr.max():.3f}]")

# Create minimal model and test
print("\nCreating minimal model...")
n_cells = min(100, adata.shape[0])
n_genes = adata.shape[1]
n_pathways = pathway_mask.shape[0]

# Use subset of data
subset_expr = expression[:n_cells].clone()

# Create dataset without graphs for simplicity
dataset = ScINTEGDataset(
    expression_data=subset_expr,
    pathway_mask=pathway_mask,
    normalize=True,
    log_transform=True,
    build_cell_graph=False,
    build_gene_graph=False
)

print(f"\nDataset expression stats after preprocessing:")
print(f"  Contains NaN: {torch.isnan(dataset.expression).any()}")
print(f"  Range: [{dataset.expression.min():.3f}, {dataset.expression.max():.3f}]")
print(f"  Mean: {dataset.expression.mean():.3f}")

# Create model
model = ScINTEGv3(
    n_cells=n_cells,
    n_genes=n_genes,
    n_pathways=n_pathways,
    pathway_gene_mask=pathway_mask,
    cell_embedding_dim=32,
    gene_embedding_dim=16,
    reconstruction_loss_type='mse'
)

# Test forward pass
print("\nTesting forward pass...")
batch_expr = dataset.expression[:10]
# Create dummy graphs
cell_graph = torch.tensor([[0, 1], [1, 0]], dtype=torch.long).t()
gene_graph = torch.tensor([[0, 1], [1, 0]], dtype=torch.long).t()

model.eval()
with torch.no_grad():
    outputs = model(batch_expr, cell_graph, gene_graph)
    
print(f"Output shape: {outputs.reconstruction.shape}")
print(f"Output contains NaN: {torch.isnan(outputs.reconstruction).any()}")
print(f"Output range: [{outputs.reconstruction.min():.3f}, {outputs.reconstruction.max():.3f}]")

# Test loss
loss, components = model.compute_loss(outputs, batch_expr, return_components=True)
print(f"\nLoss: {loss.item():.4f}")
print(f"Loss is NaN: {torch.isnan(loss).item()}")
print("Loss components:")
for name, value in components.items():
    print(f"  {name}: {value.item():.4f} (NaN: {torch.isnan(value).item()})")