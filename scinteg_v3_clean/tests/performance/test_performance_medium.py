"""
Performance test with medium-scale data.

Tests ScINTEG v3 with a medium dataset representing typical workloads.
"""

import torch
import numpy as np
import time
import psutil
import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import ScINTEGTrainer
from scinteg.training.config import create_default_config


def create_medium_dataset(n_cells=1000, n_genes=2000, n_pathways=50):
    """Create a medium-sized synthetic dataset for testing."""
    print(f"\nCreating medium dataset: {n_cells} cells, {n_genes} genes, {n_pathways} pathways")
    
    np.random.seed(42)
    
    # Create expression matrix with realistic structure
    n_types = 5
    cell_types = np.random.randint(0, n_types, n_cells)
    
    # Create type-specific expression patterns with varying expression levels
    type_signatures = np.random.randn(n_types, n_genes) * 2
    
    # Add batch effects
    n_batches = 3
    batch_labels = np.random.randint(0, n_batches, n_cells)
    batch_effects = np.random.randn(n_batches, n_genes) * 0.5
    
    # Generate expression data
    expression = np.zeros((n_cells, n_genes))
    for i in range(n_cells):
        base_expr = type_signatures[cell_types[i]]
        batch_effect = batch_effects[batch_labels[i]]
        noise = np.random.randn(n_genes) * 0.3
        expression[i] = np.maximum(0, base_expr + batch_effect + noise)
    
    # Add realistic sparsity (50% zeros)
    mask = np.random.random((n_cells, n_genes)) < 0.5
    expression[mask] = 0
    
    # Create pathway mask with overlapping pathways
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = n_genes // n_pathways + 20
    
    for i in range(n_pathways):
        # Each pathway has unique and shared genes
        start = i * (n_genes // n_pathways)
        unique_genes = list(range(start, min(start + genes_per_pathway // 3, n_genes)))
        
        # Add some overlapping genes with other pathways
        if i > 0:
            prev_pathway_genes = np.where(pathway_mask[i-1] > 0)[0]
            overlap_genes = np.random.choice(prev_pathway_genes, 
                                           min(10, len(prev_pathway_genes)), 
                                           replace=False)
            unique_genes.extend(overlap_genes)
        
        # Add random genes
        remaining = genes_per_pathway - len(unique_genes)
        if remaining > 0:
            random_genes = np.random.choice(n_genes, remaining, replace=False)
            unique_genes.extend(random_genes)
        
        pathway_mask[i, unique_genes[:genes_per_pathway]] = 1
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=torch.FloatTensor(expression),
        pathway_mask=pathway_mask,
        batch_labels=batch_labels,
        cell_types=cell_types,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.3  # Lower threshold for more edges
    )
    
    return dataset


def test_performance_medium():
    """Run performance test with medium dataset."""
    print("=" * 80)
    print("ScINTEG v3 Performance Test - Medium Scale")
    print("=" * 80)
    
    # Memory tracking
    process = psutil.Process(os.getpid())
    start_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Create dataset
    dataset = create_medium_dataset()
    print(f"Dataset created - Memory: {process.memory_info().rss / 1024 / 1024 - start_memory:.1f} MB")
    
    # Create data loaders with different batch sizes
    batch_sizes = [64, 128]
    results_by_batch = {}
    
    for batch_size in batch_sizes:
        print(f"\n{'='*60}")
        print(f"Testing with batch size: {batch_size}")
        print(f"{'='*60}")
        
        loaders = create_data_loaders(
            dataset,
            batch_size=batch_size,
            train_ratio=0.8,
            val_ratio=0.1,
            test_ratio=0.1
        )
        
        # Create model with standard configuration
        model = ScINTEGv3(
            n_cells=dataset.n_cells,
            n_genes=dataset.n_genes,
            n_pathways=dataset.n_pathways,
            pathway_gene_mask=dataset.pathway_mask,
            cell_embedding_dim=64,
            gene_embedding_dim=32,
            reconstruction_loss_type='mse',
            use_hierarchical_pathways=True,
            n_meta_pathways=10,
            use_unet_decoder=False
        )
        
        n_params = sum(p.numel() for p in model.parameters())
        print(f"Model created with {n_params:,} parameters")
        print(f"Memory after model: {process.memory_info().rss / 1024 / 1024 - start_memory:.1f} MB")
        
        # Create optimizer with learning rate scheduling
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=2
        )
        
        # Create trainer
        trainer = ScINTEGTrainer(
            model=model,
            optimizer=optimizer,
            scheduler=scheduler,
            device='cuda' if torch.cuda.is_available() else 'cpu',
            gradient_clip_val=1.0
        )
        
        # Training metrics
        epoch_times = []
        losses = []
        memory_usage = []
        batch_times = []
        
        print("\nStarting training...")
        print("-" * 80)
        print(f"{'Epoch':<10} {'Time (s)':<15} {'Batches/s':<15} {'Loss':<15} {'Memory (MB)':<15}")
        print("-" * 80)
        
        # Train for several epochs
        n_epochs = 3
        for epoch in range(n_epochs):
            epoch_start = time.time()
            
            # Training epoch
            epoch_loss = 0
            n_batches = 0
            batch_start = time.time()
            
            for batch in loaders['train']:
                loss = trainer.training_step(batch, batch_idx=n_batches)
                epoch_loss += loss
                n_batches += 1
                
                # Track batch processing time
                if n_batches % 10 == 0:
                    batch_time = time.time() - batch_start
                    batch_times.append(batch_time / 10)
                    batch_start = time.time()
            
            epoch_time = time.time() - epoch_start
            avg_loss = epoch_loss / n_batches
            current_memory = process.memory_info().rss / 1024 / 1024 - start_memory
            batches_per_sec = n_batches / epoch_time
            
            epoch_times.append(epoch_time)
            losses.append(avg_loss)
            memory_usage.append(current_memory)
            
            print(f"{epoch+1:<10} {epoch_time:<15.2f} {batches_per_sec:<15.2f} "
                  f"{avg_loss:<15.4f} {current_memory:<15.1f}")
            
            # Update scheduler
            scheduler.step(avg_loss)
        
        # Validation
        print("\nRunning validation...")
        val_start = time.time()
        val_metrics = trainer._validate(loaders['val'])
        val_time = time.time() - val_start
        
        # Store results
        results_by_batch[batch_size] = {
            'epoch_times': epoch_times,
            'losses': losses,
            'memory_usage': memory_usage,
            'batch_times': batch_times,
            'val_metrics': val_metrics,
            'val_time': val_time
        }
    
    # Summary statistics
    print("\n" + "=" * 80)
    print("Performance Summary - Medium Scale")
    print("=" * 80)
    print(f"Dataset size: {dataset.n_cells} cells × {dataset.n_genes} genes")
    print(f"Model parameters: {n_params:,}")
    
    for batch_size, results in results_by_batch.items():
        print(f"\nBatch size {batch_size}:")
        print(f"  Average epoch time: {np.mean(results['epoch_times']):.2f} ± "
              f"{np.std(results['epoch_times']):.2f} s")
        print(f"  Average batch time: {np.mean(results['batch_times']):.3f} s")
        print(f"  Final loss: {results['losses'][-1]:.4f}")
        print(f"  Loss reduction: {(results['losses'][0] - results['losses'][-1]) / results['losses'][0] * 100:.1f}%")
        print(f"  Peak memory: {max(results['memory_usage']):.1f} MB")
        print(f"  Validation time: {results['val_time']:.2f} s")
    
    # Scaling analysis
    print("\n" + "=" * 80)
    print("Scaling Analysis")
    print("=" * 80)
    
    if len(results_by_batch) > 1:
        batch_sizes_list = list(results_by_batch.keys())
        speedup = (np.mean(results_by_batch[batch_sizes_list[0]]['epoch_times']) / 
                  np.mean(results_by_batch[batch_sizes_list[1]]['epoch_times']))
        print(f"Speedup from batch {batch_sizes_list[0]} to {batch_sizes_list[1]}: {speedup:.2f}x")
    
    # Check for issues
    print("\n" + "=" * 80)
    print("Stability Check")
    print("=" * 80)
    
    issues = []
    for batch_size, results in results_by_batch.items():
        if any(np.isnan(results['losses']) or np.isinf(results['losses'])):
            issues.append(f"NaN/Inf in losses (batch {batch_size})")
        if max(results['memory_usage']) > 4000:  # More than 4GB
            issues.append(f"High memory: {max(results['memory_usage']):.1f} MB (batch {batch_size})")
        if np.mean(results['batch_times']) > 1.0:  # More than 1s per batch
            issues.append(f"Slow batches: {np.mean(results['batch_times']):.2f} s (batch {batch_size})")
    
    if issues:
        print("⚠️  Issues detected:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ All checks passed!")
    
    return {
        'n_cells': dataset.n_cells,
        'n_genes': dataset.n_genes,
        'n_params': n_params,
        'results_by_batch': results_by_batch,
        'issues': issues
    }


if __name__ == "__main__":
    results = test_performance_medium()