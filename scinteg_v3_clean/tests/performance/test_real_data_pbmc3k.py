"""
Comprehensive test of ScINTEG v3 with real PBMC3k data.

This script tests ScINTEG with real single-cell RNA-seq data to identify
and document any errors or performance issues.
"""

import torch
import numpy as np
import pandas as pd
import scanpy as sc
import time
import psutil
import os
import sys
import gc
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import warnings
import logging

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.data.utils import create_pathway_mask_from_gmt
from scinteg.training import ScINTEGTrainer
from scinteg.data.validation import DataValidator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealDataTester:
    """Comprehensive tester for ScINTEG with real data."""
    
    def __init__(self, output_dir: str = "test_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.errors = []
        self.warnings = []
        self.metrics = {}
        self.start_time = datetime.now()
        
    def log_error(self, phase: str, error: Exception, context: Dict[str, Any] = None):
        """Log an error with full traceback."""
        error_info = {
            'phase': phase,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'timestamp': datetime.now()
        }
        self.errors.append(error_info)
        logger.error(f"Error in {phase}: {error}")
        
    def log_warning(self, phase: str, message: str, context: Dict[str, Any] = None):
        """Log a warning."""
        warning_info = {
            'phase': phase,
            'message': message,
            'context': context or {},
            'timestamp': datetime.now()
        }
        self.warnings.append(warning_info)
        logger.warning(f"Warning in {phase}: {message}")
        
    def track_metric(self, name: str, value: Any, phase: str = None):
        """Track a metric."""
        if phase:
            key = f"{phase}_{name}"
        else:
            key = name
        self.metrics[key] = value
        
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage."""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent()
        }
        
    def load_pbmc3k_data(self) -> Tuple[Optional[sc.AnnData], Dict[str, Any]]:
        """Load and validate PBMC3k data."""
        logger.info("Loading PBMC3k data...")
        metrics = {}
        
        try:
            # Define paths
            data_path = Path("/mnt/f/文献汇报/gyd_developing/scinteg_0725/data/pbmc3k_processed.h5ad")
            
            if not data_path.exists():
                raise FileNotFoundError(f"Data file not found: {data_path}")
            
            # Load data
            start = time.time()
            adata = sc.read_h5ad(data_path)
            load_time = time.time() - start
            
            # Basic statistics
            n_cells, n_genes = adata.shape
            
            # Calculate sparsity (handle both sparse and dense matrices)
            if hasattr(adata.X, 'nnz'):  # Sparse matrix
                sparsity = 1 - (adata.X.nnz / (n_cells * n_genes))
            else:  # Dense matrix
                sparsity = 1 - (np.count_nonzero(adata.X) / (n_cells * n_genes))
            
            metrics['load_time'] = load_time
            metrics['n_cells'] = n_cells
            metrics['n_genes'] = n_genes
            metrics['sparsity'] = sparsity
            metrics['n_batches'] = len(adata.obs['batch'].unique()) if 'batch' in adata.obs else 1
            metrics['memory_after_load'] = self.get_memory_usage()
            
            # Data quality checks
            if hasattr(adata.X, 'toarray'):  # Sparse matrix
                genes_per_cell = (adata.X > 0).sum(axis=1).A1
                cells_per_gene = (adata.X > 0).sum(axis=0).A1
            else:  # Dense matrix
                genes_per_cell = (adata.X > 0).sum(axis=1)
                cells_per_gene = (adata.X > 0).sum(axis=0)
            
            metrics['mean_genes_per_cell'] = genes_per_cell.mean()
            metrics['min_genes_per_cell'] = genes_per_cell.min()
            metrics['max_genes_per_cell'] = genes_per_cell.max()
            metrics['zero_gene_cells'] = (genes_per_cell == 0).sum()
            
            metrics['mean_cells_per_gene'] = cells_per_gene.mean()
            metrics['min_cells_per_gene'] = cells_per_gene.min()
            metrics['max_cells_per_gene'] = cells_per_gene.max()
            metrics['zero_cell_genes'] = (cells_per_gene == 0).sum()
            
            logger.info(f"Loaded {n_cells} cells × {n_genes} genes")
            logger.info(f"Sparsity: {sparsity:.1%}")
            
            if metrics['zero_gene_cells'] > 0:
                self.log_warning('data_loading', 
                               f"Found {metrics['zero_gene_cells']} cells with zero expression",
                               {'cell_indices': np.where(genes_per_cell == 0)[0].tolist()[:10]})
            
            if metrics['zero_cell_genes'] > 0:
                self.log_warning('data_loading',
                               f"Found {metrics['zero_cell_genes']} genes with zero expression",
                               {'gene_indices': np.where(cells_per_gene == 0)[0].tolist()[:10]})
            
            return adata, metrics
            
        except Exception as e:
            self.log_error('data_loading', e)
            return None, metrics
            
    def load_pathway_data(self, gene_names: List[str]) -> Tuple[Optional[torch.Tensor], List[str], Dict[str, Any]]:
        """Load Reactome pathway data."""
        logger.info("Loading Reactome pathways...")
        metrics = {}
        
        try:
            gmt_path = Path("/mnt/f/文献汇报/gyd_developing/scinteg_0725/prior_data/msigdb/human/reactome.gmt")
            
            if not gmt_path.exists():
                raise FileNotFoundError(f"GMT file not found: {gmt_path}")
            
            start = time.time()
            pathway_mask, pathway_names = create_pathway_mask_from_gmt(
                str(gmt_path),
                gene_names,
                min_genes=10,
                max_genes=500
            )
            load_time = time.time() - start
            
            metrics['load_time'] = load_time
            metrics['n_pathways'] = len(pathway_names)
            metrics['pathway_mask_shape'] = pathway_mask.shape
            metrics['pathway_coverage'] = (pathway_mask.sum(dim=0) > 0).float().mean().item()
            metrics['mean_genes_per_pathway'] = pathway_mask.sum(dim=1).float().mean().item()
            metrics['memory_after_load'] = self.get_memory_usage()
            
            # Check for empty pathways
            empty_pathways = (pathway_mask.sum(dim=1) == 0).sum().item()
            if empty_pathways > 0:
                self.log_warning('pathway_loading',
                               f"Found {empty_pathways} empty pathways",
                               {'n_empty': empty_pathways})
            
            logger.info(f"Loaded {len(pathway_names)} pathways")
            logger.info(f"Pathway coverage: {metrics['pathway_coverage']:.1%} of genes")
            
            return pathway_mask, pathway_names, metrics
            
        except Exception as e:
            self.log_error('pathway_loading', e)
            return None, [], metrics
            
    def load_tf_data(self, gene_names: List[str]) -> Tuple[List[str], Dict[str, Any]]:
        """Load transcription factor list."""
        logger.info("Loading transcription factors...")
        metrics = {}
        
        try:
            tf_path = Path("/mnt/f/文献汇报/gyd_developing/scinteg_0725/prior_data/TF/hs_hgnc_tfs_lambert2018.txt")
            
            if not tf_path.exists():
                raise FileNotFoundError(f"TF file not found: {tf_path}")
            
            # Read TF list
            with open(tf_path, 'r') as f:
                all_tfs = [line.strip() for line in f if line.strip()]
            
            # Find TFs in our gene list
            gene_set = set(gene_names)
            tfs_in_data = [tf for tf in all_tfs if tf in gene_set]
            
            metrics['total_tfs'] = len(all_tfs)
            metrics['tfs_in_data'] = len(tfs_in_data)
            metrics['tf_coverage'] = len(tfs_in_data) / len(all_tfs)
            
            logger.info(f"Found {len(tfs_in_data)}/{len(all_tfs)} TFs in dataset")
            
            return tfs_in_data, metrics
            
        except Exception as e:
            self.log_error('tf_loading', e)
            return [], metrics
            
    def create_dataset(self, adata: sc.AnnData, pathway_mask: torch.Tensor) -> Tuple[Optional[ScINTEGDataset], Dict[str, Any]]:
        """Create ScINTEG dataset."""
        logger.info("Creating ScINTEG dataset...")
        metrics = {}
        
        try:
            start = time.time()
            
            # Convert sparse matrix to dense tensor
            if hasattr(adata.X, 'toarray'):
                expression = torch.FloatTensor(adata.X.toarray())
            else:
                expression = torch.FloatTensor(adata.X)
            
            # Get batch labels if available
            batch_labels = None
            if 'batch' in adata.obs:
                batch_labels = adata.obs['batch'].values
            
            # Get cell types if available
            cell_types = None
            if 'cell_type' in adata.obs:
                cell_types = adata.obs['cell_type'].values
            
            # Validate data before creating dataset
            validator = DataValidator(
                min_genes_per_cell=100,
                min_cells_per_gene=10,
                min_umi_per_cell=500
            )
            
            val_report = validator.validate(
                expression.numpy(),
                batch_labels=batch_labels,
                cell_types=cell_types
            )
            
            if not val_report.passed:
                self.log_warning('dataset_creation',
                               f"Data validation failed with {len(val_report.errors)} errors",
                               {'errors': val_report.errors[:5]})
            
            # Check if data is already processed
            already_processed = (expression.numpy() < 0).any()
            
            # Create dataset
            dataset = ScINTEGDataset(
                expression_data=expression,
                pathway_mask=pathway_mask,
                batch_labels=batch_labels,
                cell_types=cell_types,
                cell_names=adata.obs_names.tolist(),
                gene_names=adata.var_names.tolist(),
                normalize=False if already_processed else True,  # Skip if already processed
                log_transform=False if already_processed else True,  # Skip if already processed
                scale=False,
                build_cell_graph=True,
                build_gene_graph=True,
                cell_graph_k=15,
                gene_graph_threshold=0.3
            )
            
            creation_time = time.time() - start
            
            metrics['creation_time'] = creation_time
            metrics['cell_graph_edges'] = dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0
            metrics['gene_graph_edges'] = dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0
            metrics['memory_after_creation'] = self.get_memory_usage()
            
            # Check for isolated nodes
            if dataset.cell_graph is not None:
                cell_degrees = torch.bincount(dataset.cell_graph.flatten(), minlength=dataset.n_cells)
                isolated_cells = (cell_degrees == 0).sum().item()
                if isolated_cells > 0:
                    self.log_warning('dataset_creation',
                                   f"Found {isolated_cells} isolated cells in graph",
                                   {'n_isolated': isolated_cells})
            
            if dataset.gene_graph is not None:
                gene_degrees = torch.bincount(dataset.gene_graph.flatten(), minlength=dataset.n_genes)
                isolated_genes = (gene_degrees == 0).sum().item()
                if isolated_genes > 0:
                    self.log_warning('dataset_creation',
                                   f"Found {isolated_genes} isolated genes in graph",
                                   {'n_isolated': isolated_genes,
                                    'percent': isolated_genes / dataset.n_genes * 100})
            
            logger.info(f"Dataset created in {creation_time:.1f}s")
            
            return dataset, metrics
            
        except Exception as e:
            self.log_error('dataset_creation', e)
            return None, metrics
            
    def test_model_configuration(self, dataset: ScINTEGDataset, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test a specific model configuration."""
        config_name = config['name']
        logger.info(f"\nTesting {config_name} configuration...")
        metrics = {'config': config}
        
        try:
            # Create model
            start = time.time()
            model = ScINTEGv3(
                n_cells=dataset.n_cells,
                n_genes=dataset.n_genes,
                n_pathways=dataset.n_pathways,
                pathway_gene_mask=dataset.pathway_mask,
                **{k: v for k, v in config.items() if k != 'name'}
            )
            init_time = time.time() - start
            
            n_params = sum(p.numel() for p in model.parameters())
            metrics['init_time'] = init_time
            metrics['n_parameters'] = n_params
            metrics['model_memory_mb'] = n_params * 4 / 1024 / 1024  # Assuming float32
            
            # Test forward pass
            batch_size = min(32, dataset.n_cells)
            test_batch = {
                'expression': dataset.expression[:batch_size],
                'cell_graph': dataset.get_cell_graph(torch.arange(batch_size)),
                'gene_graph': dataset.get_gene_graph()
            }
            
            model.eval()
            with torch.no_grad():
                start = time.time()
                outputs = model(
                    test_batch['expression'],
                    test_batch['cell_graph'],
                    test_batch['gene_graph']
                )
                forward_time = time.time() - start
            
            metrics['forward_time'] = forward_time
            metrics['forward_successful'] = True
            
            # Test loss computation
            loss, loss_components = model.compute_loss(
                outputs, 
                test_batch['expression'],
                return_components=True
            )
            
            metrics['loss_value'] = loss.item()
            metrics['loss_components'] = {k: v.item() for k, v in loss_components.items()}
            
            # Check for NaN/Inf
            if torch.isnan(loss) or torch.isinf(loss):
                self.log_warning(f'model_test_{config_name}',
                               "Loss is NaN or Inf",
                               {'loss': loss.item()})
            
            # Test training step
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            trainer = ScINTEGTrainer(model=model, optimizer=optimizer, device='cpu')
            
            model.train()
            train_losses = []
            train_times = []
            
            # Create small data loader for testing
            loaders = create_data_loaders(
                dataset.get_subset(torch.arange(min(200, dataset.n_cells))),
                batch_size=32,
                train_ratio=0.8,
                val_ratio=0.1,
                test_ratio=0.1
            )
            
            for i, batch in enumerate(loaders['train']):
                if i >= 5:  # Test 5 steps
                    break
                
                start = time.time()
                step_loss = trainer.training_step(batch, batch_idx=i)
                train_times.append(time.time() - start)
                train_losses.append(step_loss)
            
            metrics['mean_step_time'] = np.mean(train_times)
            metrics['initial_train_loss'] = train_losses[0]
            metrics['final_train_loss'] = train_losses[-1]
            metrics['loss_decreased'] = train_losses[-1] < train_losses[0]
            
            # Memory after training
            metrics['memory_after_training'] = self.get_memory_usage()
            
            logger.info(f"  ✓ {config_name}: {n_params:,} params, loss: {loss.item():.4f}")
            
            # Cleanup
            del model
            del optimizer
            del trainer
            gc.collect()
            
            return metrics
            
        except Exception as e:
            self.log_error(f'model_test_{config_name}', e, {'config': config})
            metrics['error'] = str(e)
            metrics['forward_successful'] = False
            return metrics
            
    def test_training_convergence(self, dataset: ScINTEGDataset, config: Dict[str, Any], 
                                 n_epochs: int = 10) -> Dict[str, Any]:
        """Test training convergence for a configuration."""
        config_name = config['name']
        logger.info(f"\nTesting training convergence for {config_name}...")
        metrics = {'config': config}
        
        try:
            # Create model
            model = ScINTEGv3(
                n_cells=dataset.n_cells,
                n_genes=dataset.n_genes,
                n_pathways=dataset.n_pathways,
                pathway_gene_mask=dataset.pathway_mask,
                **{k: v for k, v in config.items() if k != 'name'}
            )
            
            # Setup training
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
            trainer = ScINTEGTrainer(model=model, optimizer=optimizer, device='cpu')
            
            # Create data loaders
            loaders = create_data_loaders(
                dataset,
                batch_size=64,
                train_ratio=0.7,
                val_ratio=0.15,
                test_ratio=0.15
            )
            
            # Training history
            train_losses = []
            val_losses = []
            epoch_times = []
            gradient_norms = []
            
            # Training loop
            for epoch in range(n_epochs):
                start = time.time()
                
                # Training
                model.train()
                epoch_train_losses = []
                epoch_grad_norms = []
                
                for batch_idx, batch in enumerate(loaders['train']):
                    optimizer.zero_grad()
                    
                    # Forward pass
                    outputs = model(
                        batch['expression'],
                        batch['cell_graph'],
                        batch['gene_graph']
                    )
                    
                    # Compute loss
                    loss, loss_components = model.compute_loss(
                        outputs,
                        batch['expression'],
                        return_components=True
                    )
                    
                    # Check for NaN
                    if torch.isnan(loss):
                        self.log_error(f'training_{config_name}',
                                     Exception("NaN loss detected"),
                                     {'epoch': epoch, 'batch': batch_idx})
                        metrics['nan_at_epoch'] = epoch
                        metrics['nan_at_batch'] = batch_idx
                        return metrics
                    
                    # Backward pass
                    loss.backward()
                    
                    # Compute gradient norm
                    total_norm = 0
                    for p in model.parameters():
                        if p.grad is not None:
                            param_norm = p.grad.data.norm(2)
                            total_norm += param_norm.item() ** 2
                    total_norm = total_norm ** 0.5
                    epoch_grad_norms.append(total_norm)
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    epoch_train_losses.append(loss.item())
                
                # Validation
                model.eval()
                val_loss = trainer.validation_epoch(loaders['val'], epoch)
                
                # Record metrics
                train_losses.append(np.mean(epoch_train_losses))
                val_losses.append(val_loss)
                gradient_norms.append(np.mean(epoch_grad_norms))
                epoch_times.append(time.time() - start)
                
                logger.info(f"  Epoch {epoch}: train_loss={train_losses[-1]:.4f}, "
                          f"val_loss={val_loss:.4f}, grad_norm={gradient_norms[-1]:.2f}")
            
            # Compute convergence metrics
            metrics['train_losses'] = train_losses
            metrics['val_losses'] = val_losses
            metrics['gradient_norms'] = gradient_norms
            metrics['epoch_times'] = epoch_times
            metrics['mean_epoch_time'] = np.mean(epoch_times)
            metrics['total_training_time'] = sum(epoch_times)
            
            # Check convergence
            if len(train_losses) > 3:
                recent_improvement = (train_losses[-3] - train_losses[-1]) / train_losses[-3]
                metrics['recent_improvement'] = recent_improvement
                metrics['converged'] = recent_improvement < 0.01
            
            # Check overfitting
            if val_losses[-1] > val_losses[0]:
                metrics['overfitting'] = True
                metrics['overfitting_ratio'] = val_losses[-1] / val_losses[0]
            else:
                metrics['overfitting'] = False
            
            # Final memory usage
            metrics['final_memory'] = self.get_memory_usage()
            
            # Cleanup
            del model
            del optimizer
            del trainer
            gc.collect()
            
            return metrics
            
        except Exception as e:
            self.log_error(f'training_{config_name}', e, {'config': config})
            metrics['error'] = str(e)
            return metrics
            
    def generate_report(self):
        """Generate comprehensive test report."""
        logger.info("\nGenerating test report...")
        
        report_path = self.output_dir / f"REAL_DATA_TEST_REPORT_{self.start_time.strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w') as f:
            f.write("# ScINTEG v3 Real Data (PBMC3k) Test Report\n\n")
            f.write(f"**Test Date**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Duration**: {datetime.now() - self.start_time}\n\n")
            
            # Executive Summary
            f.write("## Executive Summary\n\n")
            f.write(f"- **Total Errors**: {len(self.errors)}\n")
            f.write(f"- **Total Warnings**: {len(self.warnings)}\n")
            f.write(f"- **Configurations Tested**: {len([k for k in self.metrics.keys() if k.startswith('config_')])}\n\n")
            
            # Data Loading Summary
            f.write("## Data Loading Summary\n\n")
            if 'data_n_cells' in self.metrics:
                f.write(f"- **Cells**: {self.metrics['data_n_cells']:,}\n")
                f.write(f"- **Genes**: {self.metrics['data_n_genes']:,}\n")
                f.write(f"- **Sparsity**: {self.metrics['data_sparsity']:.1%}\n")
                f.write(f"- **Pathways**: {self.metrics.get('pathways_n_pathways', 'N/A')}\n")
                f.write(f"- **TFs in Data**: {self.metrics.get('tf_tfs_in_data', 'N/A')}\n\n")
            
            # Errors Section
            if self.errors:
                f.write("## Errors Encountered\n\n")
                for i, error in enumerate(self.errors, 1):
                    f.write(f"### Error {i}: {error['error_type']} in {error['phase']}\n\n")
                    f.write(f"**Message**: {error['error_message']}\n\n")
                    f.write("**Traceback**:\n```python\n")
                    f.write(error['traceback'])
                    f.write("```\n\n")
                    if error['context']:
                        f.write("**Context**:\n```json\n")
                        import json
                        f.write(json.dumps(error['context'], indent=2))
                        f.write("\n```\n\n")
            
            # Warnings Section
            if self.warnings:
                f.write("## Warnings\n\n")
                for warning in self.warnings:
                    f.write(f"- **{warning['phase']}**: {warning['message']}\n")
            
            # Model Configuration Results
            f.write("\n## Model Configuration Results\n\n")
            config_metrics = {k: v for k, v in self.metrics.items() if k.startswith('config_')}
            
            if config_metrics:
                f.write("| Configuration | Parameters | Init Time | Forward Time | Loss | Status |\n")
                f.write("|--------------|------------|-----------|--------------|------|--------|\n")
                
                for key, metrics in config_metrics.items():
                    config_name = metrics['config']['name']
                    n_params = metrics.get('n_parameters', 'N/A')
                    init_time = metrics.get('init_time', 'N/A')
                    forward_time = metrics.get('forward_time', 'N/A')
                    loss = metrics.get('loss_value', 'N/A')
                    status = "✓" if metrics.get('forward_successful', False) else "✗"
                    
                    f.write(f"| {config_name} | {n_params:,} | {init_time:.2f}s | {forward_time:.3f}s | {loss:.4f} | {status} |\n")
            
            # Training Convergence Results
            f.write("\n## Training Convergence Results\n\n")
            convergence_metrics = {k: v for k, v in self.metrics.items() if k.startswith('convergence_')}
            
            if convergence_metrics:
                for key, metrics in convergence_metrics.items():
                    config_name = metrics['config']['name']
                    f.write(f"### {config_name}\n\n")
                    
                    if 'error' in metrics:
                        f.write(f"**Error**: {metrics['error']}\n\n")
                    else:
                        f.write(f"- **Converged**: {metrics.get('converged', 'N/A')}\n")
                        f.write(f"- **Overfitting**: {metrics.get('overfitting', 'N/A')}\n")
                        f.write(f"- **Mean Epoch Time**: {metrics.get('mean_epoch_time', 'N/A'):.2f}s\n")
                        f.write(f"- **Final Train Loss**: {metrics.get('train_losses', ['N/A'])[-1]:.4f}\n")
                        f.write(f"- **Final Val Loss**: {metrics.get('val_losses', ['N/A'])[-1]:.4f}\n\n")
            
            # Memory Usage
            f.write("## Memory Usage\n\n")
            memory_metrics = {k: v for k, v in self.metrics.items() if 'memory' in k}
            
            for key, value in memory_metrics.items():
                if isinstance(value, dict):
                    f.write(f"**{key}**:\n")
                    f.write(f"- RSS: {value.get('rss_mb', 'N/A'):.1f} MB\n")
                    f.write(f"- VMS: {value.get('vms_mb', 'N/A'):.1f} MB\n")
                    f.write(f"- Percent: {value.get('percent', 'N/A'):.1f}%\n\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            
            # Based on errors and warnings
            if any('zero expression' in str(e) for e in self.errors):
                f.write("1. **Zero Expression Handling**: Implement better handling for cells/genes with zero expression\n")
            
            if any('isolated' in w['message'] for w in self.warnings):
                f.write("2. **Graph Construction**: Consider alternative methods for handling isolated nodes in sparse data\n")
            
            if any('NaN' in str(e) for e in self.errors):
                f.write("3. **Numerical Stability**: Add more checks for numerical stability during training\n")
            
            if any(m.get('overfitting', False) for m in convergence_metrics.values()):
                f.write("4. **Regularization**: Consider stronger regularization to prevent overfitting\n")
            
            f.write("\n## Conclusion\n\n")
            if len(self.errors) == 0:
                f.write("All tests completed successfully without critical errors.\n")
            else:
                f.write(f"Testing revealed {len(self.errors)} critical errors that need to be addressed.\n")
            
        logger.info(f"Report saved to: {report_path}")
        return report_path
        
    def run_comprehensive_test(self):
        """Run the complete test suite."""
        logger.info("=" * 80)
        logger.info("Starting ScINTEG v3 Real Data Test")
        logger.info("=" * 80)
        
        # Phase 1: Load Data
        logger.info("\nPhase 1: Data Loading")
        logger.info("-" * 40)
        
        adata, data_metrics = self.load_pbmc3k_data()
        if adata is None:
            logger.error("Failed to load PBMC3k data. Aborting test.")
            return
        
        for key, value in data_metrics.items():
            self.track_metric(key, value, 'data')
        
        # Phase 2: Load Pathway Data
        logger.info("\nPhase 2: Pathway Loading")
        logger.info("-" * 40)
        
        gene_names = adata.var_names.tolist()
        pathway_mask, pathway_names, pathway_metrics = self.load_pathway_data(gene_names)
        
        if pathway_mask is None:
            logger.error("Failed to load pathway data. Aborting test.")
            return
        
        for key, value in pathway_metrics.items():
            self.track_metric(key, value, 'pathways')
        
        # Phase 3: Load TF Data
        logger.info("\nPhase 3: TF Loading")
        logger.info("-" * 40)
        
        tfs, tf_metrics = self.load_tf_data(gene_names)
        for key, value in tf_metrics.items():
            self.track_metric(key, value, 'tf')
        
        # Phase 4: Create Dataset
        logger.info("\nPhase 4: Dataset Creation")
        logger.info("-" * 40)
        
        dataset, dataset_metrics = self.create_dataset(adata, pathway_mask)
        if dataset is None:
            logger.error("Failed to create dataset. Aborting test.")
            return
        
        for key, value in dataset_metrics.items():
            self.track_metric(key, value, 'dataset')
        
        # Phase 5: Test Model Configurations
        logger.info("\nPhase 5: Model Configuration Testing")
        logger.info("-" * 40)
        
        configurations = [
            {
                'name': 'MSE_Base',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'reconstruction_loss_type': 'mse',
                'use_hierarchical_pathways': False,
                'use_unet_decoder': False
            },
            {
                'name': 'NB_Base',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'reconstruction_loss_type': 'nb',
                'use_hierarchical_pathways': False,
                'use_unet_decoder': False
            },
            {
                'name': 'ZINB_Base',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'reconstruction_loss_type': 'zinb',
                'use_hierarchical_pathways': False,
                'use_unet_decoder': False
            },
            {
                'name': 'ZINB_Hierarchical',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'reconstruction_loss_type': 'zinb',
                'use_hierarchical_pathways': True,
                'n_meta_pathways': min(20, len(pathway_names) // 5),
                'use_unet_decoder': False
            }
        ]
        
        for config in configurations:
            config_metrics = self.test_model_configuration(dataset, config)
            self.track_metric(f"config_{config['name']}", config_metrics)
        
        # Phase 6: Test Training Convergence (only for successful configs)
        logger.info("\nPhase 6: Training Convergence Testing")
        logger.info("-" * 40)
        
        # Test convergence for ZINB model (most realistic for scRNA-seq)
        zinb_config = configurations[2]  # ZINB_Base
        convergence_metrics = self.test_training_convergence(dataset, zinb_config, n_epochs=5)
        self.track_metric(f"convergence_{zinb_config['name']}", convergence_metrics)
        
        # Phase 7: Generate Report
        logger.info("\nPhase 7: Report Generation")
        logger.info("-" * 40)
        
        report_path = self.generate_report()
        
        logger.info("\n" + "=" * 80)
        logger.info("Testing Complete")
        logger.info("=" * 80)
        logger.info(f"Errors: {len(self.errors)}")
        logger.info(f"Warnings: {len(self.warnings)}")
        logger.info(f"Report: {report_path}")
        
        return report_path


def main():
    """Main test execution."""
    tester = RealDataTester()
    report_path = tester.run_comprehensive_test()
    
    # Print summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    if tester.errors:
        print(f"\n❌ Found {len(tester.errors)} errors:")
        for error in tester.errors[:5]:  # Show first 5 errors
            print(f"  - {error['phase']}: {error['error_type']} - {error['error_message']}")
        if len(tester.errors) > 5:
            print(f"  ... and {len(tester.errors) - 5} more")
    else:
        print("\n✅ No critical errors found!")
    
    if tester.warnings:
        print(f"\n⚠️  Found {len(tester.warnings)} warnings:")
        for warning in tester.warnings[:5]:  # Show first 5 warnings
            print(f"  - {warning['phase']}: {warning['message']}")
        if len(tester.warnings) > 5:
            print(f"  ... and {len(tester.warnings) - 5} more")
    
    print(f"\n📄 Full report: {report_path}")


if __name__ == "__main__":
    main()