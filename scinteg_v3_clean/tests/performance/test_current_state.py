"""
Quick test of ScINTEG v3 current state and performance.

This script runs basic tests to verify the system is working correctly
and establishes baseline performance metrics.
"""

import torch
import numpy as np
import time
import psutil
import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import ScIN<PERSON>GTrainer
from scinteg.data.validation import DataValidator


def create_test_data(n_cells=200, n_genes=500, n_pathways=20, sparsity=0.7):
    """Create test dataset with known properties."""
    np.random.seed(42)
    
    # Create expression matrix
    expression = np.random.lognormal(0, 1, (n_cells, n_genes))
    
    # Add sparsity
    mask = np.random.random((n_cells, n_genes)) < sparsity
    expression[mask] = 0
    
    # Create pathway mask
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = 30
    
    for i in range(n_pathways):
        start = i * 20
        gene_indices = list(range(start, min(start + 15, n_genes)))
        # Add some random genes
        random_genes = np.random.choice(n_genes, 15, replace=False)
        gene_indices.extend(random_genes)
        pathway_mask[i, gene_indices[:genes_per_pathway]] = 1
    
    # Create batch labels
    batch_labels = np.array([0] * (n_cells // 2) + [1] * (n_cells - n_cells // 2))
    
    # Create cell types
    cell_types = np.random.randint(0, 3, n_cells)
    
    return expression, pathway_mask, batch_labels, cell_types


def test_basic_functionality():
    """Test basic ScINTEG functionality."""
    print("=" * 80)
    print("ScINTEG v3 Current State Test")
    print("=" * 80)
    
    # Test 1: Data creation and validation
    print("\n1. Testing data creation and validation...")
    expression, pathway_mask, batch_labels, cell_types = create_test_data()
    
    validator = DataValidator()
    report = validator.validate(expression, batch_labels=batch_labels, cell_types=cell_types)
    
    print(f"   Data validation: {'PASSED' if report.passed else 'FAILED'}")
    print(f"   Sparsity: {(expression == 0).mean():.1%}")
    print(f"   Warnings: {len(report.warnings)}")
    
    # Test 2: Dataset creation
    print("\n2. Testing dataset creation...")
    try:
        dataset = ScINTEGDataset(
            expression_data=torch.FloatTensor(expression),
            pathway_mask=pathway_mask,
            batch_labels=batch_labels,
            cell_types=cell_types,
            normalize=True,
            log_transform=True,
            build_cell_graph=True,
            build_gene_graph=True,
            cell_graph_k=15,
            gene_graph_threshold=0.3
        )
        print(f"   Dataset created: {dataset.n_cells} cells × {dataset.n_genes} genes")
        print(f"   Cell graph edges: {dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0}")
        print(f"   Gene graph edges: {dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0}")
    except Exception as e:
        print(f"   ❌ Dataset creation failed: {e}")
        return
    
    # Test 3: Model creation
    print("\n3. Testing model creation...")
    try:
        model = ScINTEGv3(
            n_cells=dataset.n_cells,
            n_genes=dataset.n_genes,
            n_pathways=dataset.n_pathways,
            pathway_gene_mask=dataset.pathway_mask,
            cell_embedding_dim=32,
            gene_embedding_dim=16,
            reconstruction_loss_type='mse'
        )
        n_params = sum(p.numel() for p in model.parameters())
        print(f"   Model created with {n_params:,} parameters")
    except Exception as e:
        print(f"   ❌ Model creation failed: {e}")
        return
    
    # Test 4: Data loading
    print("\n4. Testing data loading...")
    try:
        loaders = create_data_loaders(
            dataset,
            batch_size=32,
            train_ratio=0.7,
            val_ratio=0.15,
            test_ratio=0.15
        )
        print(f"   Train batches: {len(loaders['train'])}")
        print(f"   Val batches: {len(loaders['val'])}")
        print(f"   Test batches: {len(loaders['test'])}")
    except Exception as e:
        print(f"   ❌ Data loading failed: {e}")
        return
    
    # Test 5: Forward pass
    print("\n5. Testing forward pass...")
    try:
        batch = next(iter(loaders['train']))
        print(f"   Batch keys: {list(batch.keys())}")
        
        model.eval()
        with torch.no_grad():
            outputs = model(
                batch['expression'],
                batch['cell_graph'],
                batch['gene_graph']
            )
        print(f"   Forward pass successful")
        print(f"   Output shape: {outputs.reconstruction.shape}")
    except Exception as e:
        print(f"   ❌ Forward pass failed: {e}")
        return
    
    # Test 6: Loss computation
    print("\n6. Testing loss computation...")
    try:
        loss, components = model.compute_loss(outputs, batch['expression'], return_components=True)
        print(f"   Total loss: {loss.item():.4f}")
        for name, value in components.items():
            print(f"   - {name}: {value.item():.4f}")
    except Exception as e:
        print(f"   ❌ Loss computation failed: {e}")
        return
    
    # Test 7: Training step
    print("\n7. Testing training...")
    try:
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        trainer = ScINTEGTrainer(
            model=model,
            optimizer=optimizer,
            device='cpu'
        )
        
        # Train for a few steps
        model.train()
        losses = []
        times = []
        
        for i, batch in enumerate(loaders['train']):
            if i >= 5:
                break
            
            start = time.time()
            loss = trainer.training_step(batch, batch_idx=i)
            times.append(time.time() - start)
            losses.append(loss)
        
        print(f"   Training successful")
        print(f"   Average step time: {np.mean(times):.3f}s")
        print(f"   Initial loss: {losses[0]:.4f}")
        print(f"   Final loss: {losses[-1]:.4f}")
    except Exception as e:
        print(f"   ❌ Training failed: {e}")
        return
    
    # Test 8: Memory usage
    print("\n8. Memory usage...")
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"   Current memory: {memory_mb:.1f} MB")
    
    print("\n" + "=" * 80)
    print("Summary")
    print("=" * 80)
    print("✅ All basic tests passed!")
    print("\nSystem is ready for more comprehensive testing.")
    
    return True


def test_different_configs():
    """Test different model configurations."""
    print("\n" + "=" * 80)
    print("Testing Different Configurations")
    print("=" * 80)
    
    # Create dataset
    expression, pathway_mask, batch_labels, cell_types = create_test_data()
    dataset = ScINTEGDataset(
        expression_data=torch.FloatTensor(expression),
        pathway_mask=pathway_mask,
        batch_labels=batch_labels,
        cell_types=cell_types,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True
    )
    
    configs = [
        {
            'name': 'Minimal',
            'cell_embedding_dim': 16,
            'gene_embedding_dim': 8,
            'use_hierarchical_pathways': False,
            'use_unet_decoder': False
        },
        {
            'name': 'Standard',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32,
            'use_hierarchical_pathways': False,
            'use_unet_decoder': False
        },
        {
            'name': 'Hierarchical',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32,
            'use_hierarchical_pathways': True,
            'n_meta_pathways': 5,
            'use_unet_decoder': False
        },
        {
            'name': 'UNet',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32,
            'use_hierarchical_pathways': False,
            'use_unet_decoder': True
        }
    ]
    
    for config in configs:
        print(f"\nTesting {config['name']} configuration...")
        try:
            model = ScINTEGv3(
                n_cells=dataset.n_cells,
                n_genes=dataset.n_genes,
                n_pathways=dataset.n_pathways,
                pathway_gene_mask=dataset.pathway_mask,
                reconstruction_loss_type='mse',
                **{k: v for k, v in config.items() if k != 'name'}
            )
            n_params = sum(p.numel() for p in model.parameters())
            
            # Test forward pass
            batch_data = torch.randn(10, dataset.n_genes)
            cell_graph = torch.stack([torch.arange(10), torch.arange(10)], dim=0).long()
            gene_graph = torch.stack([torch.arange(100), torch.arange(100)], dim=0).long()
            
            with torch.no_grad():
                outputs = model(batch_data, cell_graph, gene_graph)
            
            print(f"   ✅ {config['name']}: {n_params:,} parameters")
        except Exception as e:
            print(f"   ❌ {config['name']} failed: {e}")


if __name__ == "__main__":
    # Run basic functionality test
    success = test_basic_functionality()
    
    if success:
        # Test different configurations
        test_different_configs()
    
    print("\n" + "=" * 80)
    print("Testing Complete")
    print("=" * 80)