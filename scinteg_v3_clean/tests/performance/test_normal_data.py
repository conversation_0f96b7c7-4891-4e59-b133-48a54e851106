"""
Performance test with normal, well-behaved data.

Tests ScINTEG v3 with data that has typical single-cell RNA-seq characteristics.
"""

import torch
import numpy as np
import time
import psutil
import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import ScINTEGTrainer
from scinteg.data.validation import DataValidator


def create_normal_dataset(n_cells=500, n_genes=1000, n_pathways=30):
    """Create a dataset with normal single-cell characteristics."""
    print(f"\nCreating normal dataset: {n_cells} cells, {n_genes} genes, {n_pathways} pathways")
    
    np.random.seed(42)
    
    # Simulate realistic cell type distribution
    cell_type_props = [0.3, 0.25, 0.2, 0.15, 0.1]  # 5 cell types with varying proportions
    n_types = len(cell_type_props)
    cell_types = []
    for i, prop in enumerate(cell_type_props):
        cell_types.extend([i] * int(n_cells * prop))
    cell_types = np.array(cell_types[:n_cells])
    np.random.shuffle(cell_types)
    
    # Create realistic gene expression patterns
    # Housekeeping genes (expressed in all cells)
    n_housekeeping = int(n_genes * 0.1)
    # Cell-type specific genes
    n_specific = int(n_genes * 0.3)
    # Variable genes
    n_variable = n_genes - n_housekeeping - n_specific
    
    expression = np.zeros((n_cells, n_genes))
    
    # Housekeeping genes - consistently expressed
    housekeeping_expr = np.random.lognormal(2, 0.5, (n_cells, n_housekeeping))
    expression[:, :n_housekeeping] = housekeeping_expr
    
    # Cell-type specific genes
    for cell_type in range(n_types):
        mask = cell_types == cell_type
        n_cells_type = mask.sum()
        
        # Each cell type expresses different specific genes
        start_idx = n_housekeeping + cell_type * (n_specific // n_types)
        end_idx = n_housekeeping + (cell_type + 1) * (n_specific // n_types)
        
        if n_cells_type > 0:
            specific_expr = np.random.lognormal(1.5, 0.8, (n_cells_type, end_idx - start_idx))
            expression[mask, start_idx:end_idx] = specific_expr
    
    # Variable genes - sporadic expression
    variable_expr = np.random.lognormal(0, 1.5, (n_cells, n_variable))
    variable_mask = np.random.random((n_cells, n_variable)) < 0.3  # 30% chance of expression
    variable_expr[~variable_mask] = 0
    expression[:, -n_variable:] = variable_expr
    
    # Add realistic dropout (zero-inflation)
    dropout_rate = 0.7  # 70% zeros is typical
    dropout_mask = np.random.random(expression.shape) < dropout_rate
    # Don't dropout highly expressed genes
    dropout_mask[expression > np.percentile(expression[expression > 0], 80)] = False
    expression[dropout_mask] = 0
    
    # Add mild batch effects (2 batches)
    batch_labels = np.array([0] * (n_cells // 2) + [1] * (n_cells - n_cells // 2))
    batch_effect = np.random.normal(0, 0.1, (2, n_genes))
    for i in range(n_cells):
        expression[i] *= (1 + batch_effect[batch_labels[i]])
    
    # Create biologically meaningful pathway mask
    pathway_mask = torch.zeros(n_pathways, n_genes)
    
    # Some pathways for housekeeping genes
    for i in range(3):
        pathway_mask[i, i*20:(i+1)*20] = 1  # Housekeeping pathways
    
    # Cell-type specific pathways
    for i in range(n_types):
        pathway_idx = 3 + i
        start_idx = n_housekeeping + i * (n_specific // n_types)
        end_idx = n_housekeeping + (i + 1) * (n_specific // n_types)
        pathway_mask[pathway_idx, start_idx:end_idx] = 1
    
    # General signaling pathways (overlapping genes)
    for i in range(8, n_pathways):
        # Random selection with some overlap
        n_genes_pathway = np.random.randint(20, 50)
        gene_indices = np.random.choice(n_genes, n_genes_pathway, replace=False)
        pathway_mask[i, gene_indices] = 1
    
    # Ensure minimum sparsity
    expression = torch.FloatTensor(expression)
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        pathway_mask=pathway_mask,
        batch_labels=batch_labels,
        cell_types=cell_types,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.3
    )
    
    return dataset


def test_normal_data():
    """Test ScINTEG with normal, well-behaved data."""
    print("=" * 80)
    print("ScINTEG v3 Performance Test - Normal Data")
    print("=" * 80)
    
    # Memory tracking
    process = psutil.Process(os.getpid())
    start_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Create dataset
    dataset = create_normal_dataset()
    print(f"Dataset created - Memory: {process.memory_info().rss / 1024 / 1024 - start_memory:.1f} MB")
    
    # Validate data quality
    print("\nValidating data quality...")
    validator = DataValidator()
    validation_report = validator.validate(
        dataset.expression.numpy(),
        batch_labels=dataset.batch_labels.numpy() if dataset.batch_labels is not None else None,
        cell_types=dataset.cell_types.numpy() if dataset.cell_types is not None else None
    )
    
    print(f"Validation passed: {validation_report.passed}")
    print(f"Warnings: {len(validation_report.warnings)}")
    for warning in validation_report.warnings[:3]:  # Show first 3 warnings
        print(f"  - {warning}")
    
    print("\nData statistics:")
    print(f"  Zero expression: {validation_report.metrics.get('zero_expression_pct', 0):.1f}%")
    print(f"  Cells per gene (median): {validation_report.metrics.get('cells_per_gene_median', 0):.0f}")
    print(f"  Genes per cell (median): {validation_report.metrics.get('genes_per_cell_median', 0):.0f}")
    
    # Create data loaders
    loaders = create_data_loaders(
        dataset,
        batch_size=64,
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15
    )
    
    # Test different model configurations
    configs = [
        {
            'name': 'Standard',
            'use_hierarchical_pathways': False,
            'use_unet_decoder': False,
            'reconstruction_loss_type': 'mse'
        },
        {
            'name': 'Hierarchical',
            'use_hierarchical_pathways': True,
            'n_meta_pathways': 10,
            'use_unet_decoder': False,
            'reconstruction_loss_type': 'mse'
        },
        {
            'name': 'NB Loss',
            'use_hierarchical_pathways': False,
            'use_unet_decoder': False,
            'reconstruction_loss_type': 'nb'
        }
    ]
    
    results = {}
    
    for config in configs:
        print(f"\n{'='*60}")
        print(f"Testing configuration: {config['name']}")
        print(f"{'='*60}")
        
        # Create model
        model = ScINTEGv3(
            n_cells=dataset.n_cells,
            n_genes=dataset.n_genes,
            n_pathways=dataset.n_pathways,
            pathway_gene_mask=dataset.pathway_mask,
            cell_embedding_dim=64,
            gene_embedding_dim=32,
            **{k: v for k, v in config.items() if k != 'name'}
        )
        
        n_params = sum(p.numel() for p in model.parameters())
        print(f"Model parameters: {n_params:,}")
        
        # Create optimizer
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        
        # Create trainer
        trainer = ScINTEGTrainer(
            model=model,
            optimizer=optimizer,
            device='cuda' if torch.cuda.is_available() else 'cpu',
            gradient_clip_val=1.0
        )
        
        # Quick training test
        print("\nTraining for 3 epochs...")
        epoch_times = []
        losses = []
        
        for epoch in range(3):
            epoch_start = time.time()
            epoch_loss = 0
            n_batches = 0
            
            for batch in loaders['train']:
                loss = trainer.training_step(batch, batch_idx=n_batches)
                epoch_loss += loss
                n_batches += 1
            
            epoch_time = time.time() - epoch_start
            avg_loss = epoch_loss / n_batches
            
            epoch_times.append(epoch_time)
            losses.append(avg_loss)
            
            print(f"  Epoch {epoch+1}: {epoch_time:.2f}s, Loss: {avg_loss:.4f}")
        
        # Validation
        val_metrics = trainer._validate(loaders['val'])
        
        # Test inference speed
        print("\nTesting inference speed...")
        model.eval()
        inference_times = []
        
        with torch.no_grad():
            for _ in range(5):
                batch = next(iter(loaders['test']))
                start = time.time()
                _ = trainer.model(batch['inputs'])
                inference_times.append(time.time() - start)
        
        avg_inference_time = np.mean(inference_times[1:])  # Skip first
        
        results[config['name']] = {
            'epoch_times': epoch_times,
            'losses': losses,
            'val_metrics': val_metrics,
            'inference_time': avg_inference_time,
            'memory': process.memory_info().rss / 1024 / 1024 - start_memory
        }
    
    # Summary
    print("\n" + "=" * 80)
    print("Performance Summary - Normal Data")
    print("=" * 80)
    
    print(f"\nDataset: {dataset.n_cells} cells × {dataset.n_genes} genes")
    print(f"Sparsity: {(dataset.expression == 0).float().mean().item():.1%}")
    
    print("\nConfiguration Comparison:")
    print("-" * 80)
    print(f"{'Config':<15} {'Epoch Time':<15} {'Final Loss':<15} {'Val Corr':<15} {'Inference':<15}")
    print("-" * 80)
    
    for name, res in results.items():
        val_corr = res['val_metrics'].get('reconstruction_corr', 0)
        print(f"{name:<15} {np.mean(res['epoch_times']):<15.2f} "
              f"{res['losses'][-1]:<15.4f} {val_corr:<15.4f} "
              f"{res['inference_time']*1000:<15.1f}ms")
    
    # Stability check
    print("\n" + "=" * 80)
    print("Stability Analysis")
    print("=" * 80)
    
    all_stable = True
    for name, res in results.items():
        issues = []
        
        # Check for NaN/Inf
        if any(np.isnan(res['losses']) or np.isinf(res['losses'])):
            issues.append("NaN/Inf in losses")
            all_stable = False
        
        # Check for convergence
        if len(res['losses']) > 1:
            improvement = (res['losses'][0] - res['losses'][-1]) / res['losses'][0]
            if improvement < 0.05:  # Less than 5% improvement
                issues.append(f"Poor convergence ({improvement:.1%})")
        
        # Check validation metrics
        if res['val_metrics'].get('reconstruction_corr', 0) < 0.5:
            issues.append("Low reconstruction correlation")
        
        if issues:
            print(f"\n{name}:")
            for issue in issues:
                print(f"  ⚠️  {issue}")
        else:
            print(f"\n{name}: ✅ All checks passed")
    
    if all_stable:
        print("\n✅ Overall: System is stable with normal data!")
    else:
        print("\n⚠️  Overall: Some stability issues detected")
    
    return results


if __name__ == "__main__":
    results = test_normal_data()