"""
Clean test of ScINTEG v3 with PBMC3k raw data.
This version properly handles the data format issues.
"""

import torch
import numpy as np
import scanpy as sc
import time
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional

sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.data.utils import create_pathway_mask_from_gmt
from scinteg.training import ScINTEGTrainer

import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_pbmc3k_raw_data():
    """Load PBMC3k data and use raw counts if available."""
    logger.info("Loading PBMC3k data...")
    
    # Load the h5ad file
    adata = sc.read_h5ad("/mnt/f/文献汇报/gyd_developing/scinteg_0725/data/pbmc3k_processed.h5ad")
    
    # Check data format
    logger.info(f"Main data shape: {adata.shape}")
    logger.info(f"Main data range: [{adata.X.min():.3f}, {adata.X.max():.3f}]")
    
    # Use raw data if available (which should be counts)
    if adata.raw is not None:
        logger.info("Using raw count data from adata.raw")
        # Convert raw to full AnnData object
        adata_raw = adata.raw.to_adata()
        
        # Filter to highly variable genes if they were selected
        if 'highly_variable' in adata.var:
            hvg_genes = adata.var_names[adata.var['highly_variable']]
            # Keep only HVGs that exist in raw data
            hvg_genes = [g for g in hvg_genes if g in adata_raw.var_names]
            adata_raw = adata_raw[:, hvg_genes]
            logger.info(f"Filtered to {len(hvg_genes)} highly variable genes")
        
        return adata_raw
    else:
        # If no raw data, use main data but warn about preprocessing
        logger.warning("No raw data found, using preprocessed data")
        logger.warning("This may cause issues if data is already normalized/scaled")
        return adata


def test_scinteg_with_real_data():
    """Main test function with proper data handling."""
    
    # Phase 1: Load Data
    logger.info("\n" + "="*60)
    logger.info("Phase 1: Data Loading")
    logger.info("="*60)
    
    # Load raw data
    adata = load_pbmc3k_raw_data()
    n_cells, n_genes = adata.shape
    
    # Data statistics
    is_sparse = hasattr(adata.X, 'toarray')
    if is_sparse:
        expression_matrix = adata.X.toarray()
    else:
        expression_matrix = adata.X
    
    data_stats = {
        'n_cells': n_cells,
        'n_genes': n_genes,
        'sparsity': 1 - np.count_nonzero(expression_matrix) / (n_cells * n_genes),
        'value_range': (expression_matrix.min(), expression_matrix.max()),
        'has_negatives': (expression_matrix < 0).any()
    }
    
    logger.info(f"Cells: {data_stats['n_cells']}")
    logger.info(f"Genes: {data_stats['n_genes']}")
    logger.info(f"Sparsity: {data_stats['sparsity']:.1%}")
    logger.info(f"Value range: [{data_stats['value_range'][0]:.3f}, {data_stats['value_range'][1]:.3f}]")
    logger.info(f"Contains negative values: {data_stats['has_negatives']}")
    
    # Phase 2: Load Pathways
    logger.info("\n" + "="*60)
    logger.info("Phase 2: Pathway Loading")
    logger.info("="*60)
    
    gene_names = adata.var_names.tolist()
    pathway_mask, pathway_names = create_pathway_mask_from_gmt(
        "/mnt/f/文献汇报/gyd_developing/scinteg_0725/prior_data/msigdb/human/reactome.gmt",
        gene_names,
        min_genes=10,
        max_genes=500
    )
    
    logger.info(f"Loaded {len(pathway_names)} pathways")
    pathway_coverage = (pathway_mask.sum(dim=0) > 0).float().mean().item()
    logger.info(f"Pathway coverage: {pathway_coverage:.1%} of genes")
    
    # Phase 3: Create Dataset
    logger.info("\n" + "="*60)
    logger.info("Phase 3: Dataset Creation")
    logger.info("="*60)
    
    # Convert to tensor
    expression = torch.FloatTensor(expression_matrix)
    
    # Determine preprocessing based on data characteristics
    should_normalize = not data_stats['has_negatives'] and data_stats['value_range'][1] > 10
    should_log_transform = not data_stats['has_negatives'] and data_stats['value_range'][1] > 10
    
    logger.info(f"Will normalize: {should_normalize}")
    logger.info(f"Will log-transform: {should_log_transform}")
    
    # Adjust graph parameters based on sparsity
    if data_stats['sparsity'] < 0.5:  # Dense data
        gene_graph_threshold = 0.1
        logger.info("Using lower correlation threshold (0.1) for dense data")
    else:
        gene_graph_threshold = 0.3
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        pathway_mask=pathway_mask,
        cell_names=adata.obs_names.tolist(),
        gene_names=gene_names,
        normalize=should_normalize,
        log_transform=should_log_transform,
        scale=False,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=gene_graph_threshold
    )
    
    logger.info(f"Dataset created successfully")
    logger.info(f"Cell graph edges: {dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0}")
    logger.info(f"Gene graph edges: {dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0}")
    
    # Check for isolated nodes
    if dataset.gene_graph is not None and dataset.gene_graph.shape[1] > 0:
        gene_degrees = torch.bincount(dataset.gene_graph.flatten(), minlength=n_genes)
        isolated_genes = (gene_degrees == 0).sum().item()
        logger.info(f"Isolated genes: {isolated_genes} ({isolated_genes/n_genes*100:.1f}%)")
    
    # Phase 4: Test Models
    logger.info("\n" + "="*60)
    logger.info("Phase 4: Model Testing")
    logger.info("="*60)
    
    results = {}
    
    # Test configurations
    configs = [
        {
            'name': 'MSE',
            'reconstruction_loss_type': 'mse',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32
        },
        {
            'name': 'NB',
            'reconstruction_loss_type': 'nb',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32
        },
        {
            'name': 'ZINB',
            'reconstruction_loss_type': 'zinb',
            'cell_embedding_dim': 64,
            'gene_embedding_dim': 32
        }
    ]
    
    for config in configs:
        logger.info(f"\nTesting {config['name']} configuration...")
        
        try:
            # Create model
            model = ScINTEGv3(
                n_cells=dataset.n_cells,
                n_genes=dataset.n_genes,
                n_pathways=dataset.n_pathways,
                pathway_gene_mask=dataset.pathway_mask,
                **{k: v for k, v in config.items() if k != 'name'}
            )
            
            n_params = sum(p.numel() for p in model.parameters())
            logger.info(f"  Parameters: {n_params:,}")
            
            # Test forward pass
            batch_size = 32
            test_expr = dataset.expression[:batch_size]
            test_cell_graph = dataset.get_cell_graph(torch.arange(batch_size))
            test_gene_graph = dataset.get_gene_graph()
            
            model.eval()
            with torch.no_grad():
                outputs = model(test_expr, test_cell_graph, test_gene_graph)
                loss, components = model.compute_loss(outputs, test_expr, return_components=True)
            
            # Check results
            has_nan = torch.isnan(loss).item()
            logger.info(f"  Loss: {loss.item():.4f} (NaN: {has_nan})")
            
            if not has_nan:
                # Quick training test
                optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
                model.train()
                
                # One training step
                optimizer.zero_grad()
                outputs = model(test_expr, test_cell_graph, test_gene_graph)
                loss = model.compute_loss(outputs, test_expr)
                loss.backward()
                optimizer.step()
                
                logger.info(f"  ✓ {config['name']} working correctly")
                results[config['name']] = 'Success'
            else:
                logger.warning(f"  ✗ {config['name']} has NaN loss")
                results[config['name']] = 'NaN Loss'
                
        except Exception as e:
            logger.error(f"  ✗ {config['name']} failed: {str(e)}")
            results[config['name']] = f'Error: {str(e)}'
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("Summary")
    logger.info("="*60)
    
    for name, status in results.items():
        logger.info(f"{name}: {status}")
    
    return results


if __name__ == "__main__":
    results = test_scinteg_with_real_data()
    
    # Check if all tests passed
    all_passed = all('Success' in str(v) for v in results.values())
    if all_passed:
        logger.info("\n✅ All tests passed!")
    else:
        logger.info("\n❌ Some tests failed - see details above")