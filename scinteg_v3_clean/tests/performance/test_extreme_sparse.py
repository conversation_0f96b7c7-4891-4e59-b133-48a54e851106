"""
Extreme sparsity test for ScINTEG v3.

Tests the model with ultra-sparse data (95%+ zeros) to find fundamental errors
related to zero-handling, graph construction, and numerical stability.
"""

import torch
import numpy as np
import time
import psutil
import os
import sys
import traceback
from pathlib import Path
import logging

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import ScINTEGTrainer
from scinteg.data.validation import DataValidator

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_extreme_sparse_data(n_cells=200, n_genes=1000, n_pathways=20, sparsity_levels=[0.95, 0.98, 0.99, 0.999]):
    """Create datasets with extreme sparsity levels."""
    datasets = {}
    
    for sparsity in sparsity_levels:
        logger.info(f"\nCreating dataset with {sparsity*100:.1f}% sparsity")
        np.random.seed(42)
        
        # Start with a base expression pattern
        # Create cell type signatures with very few expressing genes
        n_types = 3
        cell_types = np.random.randint(0, n_types, n_cells)
        
        # Each cell type expresses only 20-50 genes strongly
        type_signatures = np.zeros((n_types, n_genes))
        for t in range(n_types):
            expressing_genes = np.random.choice(n_genes, size=np.random.randint(20, 50), replace=False)
            type_signatures[t, expressing_genes] = np.random.lognormal(2, 1, len(expressing_genes))
        
        # Generate expression data
        expression = np.zeros((n_cells, n_genes))
        for i in range(n_cells):
            # Add type-specific expression
            base_expr = type_signatures[cell_types[i]]
            # Add very sparse noise
            noise_mask = np.random.random(n_genes) > 0.99  # Only 1% of genes get noise
            noise = np.random.lognormal(0, 0.5, n_genes) * noise_mask
            expression[i] = base_expr + noise
        
        # Apply extreme sparsity
        mask = np.random.random((n_cells, n_genes)) < sparsity
        expression[mask] = 0
        
        # Ensure some cells are completely zero (dropout cells)
        dropout_cells = np.random.choice(n_cells, size=int(n_cells * 0.05), replace=False)
        expression[dropout_cells, :] = 0
        
        # Ensure some genes are completely zero (non-expressed genes)
        dropout_genes = np.random.choice(n_genes, size=int(n_genes * 0.1), replace=False)
        expression[:, dropout_genes] = 0
        
        # Create pathway mask with some empty pathways
        pathway_mask = torch.zeros(n_pathways, n_genes)
        genes_per_pathway = 30
        
        for i in range(n_pathways):
            if i < n_pathways - 2:  # Leave 2 pathways empty
                # Sparse pathway membership
                pathway_genes = np.random.choice(n_genes, size=genes_per_pathway, replace=False)
                pathway_mask[i, pathway_genes] = 1
        
        # Create batch labels with imbalanced batches
        batch_labels = np.concatenate([
            np.zeros(int(n_cells * 0.8)),  # 80% batch 0
            np.ones(int(n_cells * 0.2))    # 20% batch 1
        ])[:n_cells]
        np.random.shuffle(batch_labels)
        
        # Log statistics
        actual_sparsity = (expression == 0).mean()
        cells_all_zero = (expression.sum(axis=1) == 0).sum()
        genes_all_zero = (expression.sum(axis=0) == 0).sum()
        mean_genes_per_cell = (expression > 0).sum(axis=1).mean()
        mean_cells_per_gene = (expression > 0).sum(axis=0).mean()
        
        logger.info(f"  Actual sparsity: {actual_sparsity*100:.2f}%")
        logger.info(f"  Cells with all zeros: {cells_all_zero}/{n_cells}")
        logger.info(f"  Genes with all zeros: {genes_all_zero}/{n_genes}")
        logger.info(f"  Mean genes per cell: {mean_genes_per_cell:.1f}")
        logger.info(f"  Mean cells per gene: {mean_cells_per_gene:.1f}")
        
        datasets[sparsity] = {
            'expression': expression,
            'pathway_mask': pathway_mask,
            'batch_labels': batch_labels,
            'cell_types': cell_types,
            'stats': {
                'actual_sparsity': actual_sparsity,
                'cells_all_zero': cells_all_zero,
                'genes_all_zero': genes_all_zero,
                'mean_genes_per_cell': mean_genes_per_cell,
                'mean_cells_per_gene': mean_cells_per_gene
            }
        }
    
    return datasets


def test_extreme_sparse_dataset(data_dict, sparsity_level, device='cpu'):
    """Test ScINTEG with an extreme sparse dataset."""
    logger.info(f"\n{'='*80}")
    logger.info(f"Testing with {sparsity_level*100:.1f}% sparsity")
    logger.info(f"{'='*80}")
    
    errors = []
    warnings = []
    
    try:
        # 1. Data validation
        logger.info("\n1. Testing data validation...")
        validator = DataValidator(
            min_genes_per_cell=5,  # Very low threshold
            min_cells_per_gene=5,  # Very low threshold
            min_umi_per_cell=10,   # Very low threshold for sparse data
            max_genes_per_cell=10000  # High threshold
        )
        
        report = validator.validate(
            data_dict['expression'],
            batch_labels=data_dict['batch_labels'],
            cell_types=data_dict['cell_types']
        )
        
        logger.info(f"   Validation passed: {report.passed}")
        logger.info(f"   Errors: {len(report.errors)}")
        logger.info(f"   Warnings: {len(report.warnings)}")
        
        if report.errors:
            for error in report.errors:
                logger.error(f"   - {error}")
                errors.append(f"Validation: {error}")
        
        # 2. Dataset creation
        logger.info("\n2. Testing dataset creation...")
        try:
            dataset = ScINTEGDataset(
                expression_data=torch.FloatTensor(data_dict['expression']),
                pathway_mask=data_dict['pathway_mask'],
                batch_labels=data_dict['batch_labels'],
                cell_types=data_dict['cell_types'],
                normalize=True,
                log_transform=True,
                build_cell_graph=True,
                build_gene_graph=True,
                cell_graph_k=min(15, max(1, int(data_dict['stats']['mean_cells_per_gene']))),
                gene_graph_threshold=0.1  # Lower threshold for sparse data
            )
            logger.info(f"   Dataset created successfully")
            logger.info(f"   Cell graph edges: {dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0}")
            logger.info(f"   Gene graph edges: {dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0}")
            
            # Check for isolated nodes
            if dataset.cell_graph is not None and dataset.cell_graph.shape[1] > 0:
                cell_degrees = torch.zeros(dataset.n_cells)
                cell_degrees.scatter_add_(0, dataset.cell_graph[0], torch.ones(dataset.cell_graph.shape[1]))
                isolated_cells = (cell_degrees == 0).sum().item()
                if isolated_cells > 0:
                    warnings.append(f"Found {isolated_cells} isolated cells in graph")
                    logger.warning(f"   Isolated cells: {isolated_cells}/{dataset.n_cells}")
            
            if dataset.gene_graph is not None and dataset.gene_graph.shape[1] > 0:
                gene_degrees = torch.zeros(dataset.n_genes)
                gene_degrees.scatter_add_(0, dataset.gene_graph[0], torch.ones(dataset.gene_graph.shape[1]))
                isolated_genes = (gene_degrees == 0).sum().item()
                if isolated_genes > 0:
                    warnings.append(f"Found {isolated_genes} isolated genes in graph")
                    logger.warning(f"   Isolated genes: {isolated_genes}/{dataset.n_genes}")
            
        except Exception as e:
            error_msg = f"Dataset creation failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 3. Model creation
        logger.info("\n3. Testing model creation...")
        try:
            model = ScINTEGv3(
                n_cells=dataset.n_cells,
                n_genes=dataset.n_genes,
                n_pathways=dataset.n_pathways,
                pathway_gene_mask=dataset.pathway_mask,
                cell_embedding_dim=32,
                gene_embedding_dim=16,
                reconstruction_loss_type='zinb',  # Use ZINB for zero-inflated data
                use_batch_norm=True,
                dropout_rate=0.1
            ).to(device)
            
            n_params = sum(p.numel() for p in model.parameters())
            logger.info(f"   Model created with {n_params:,} parameters")
            
        except Exception as e:
            error_msg = f"Model creation failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 4. Data loading
        logger.info("\n4. Testing data loading...")
        try:
            loaders = create_data_loaders(
                dataset,
                batch_size=32,
                train_ratio=0.8,
                val_ratio=0.1,
                test_ratio=0.1
            )
            logger.info(f"   Data loaders created successfully")
            
        except Exception as e:
            error_msg = f"Data loading failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 5. Forward pass
        logger.info("\n5. Testing forward pass...")
        try:
            batch = next(iter(loaders['train']))
            # Move batch to device
            for key in batch:
                if torch.is_tensor(batch[key]):
                    batch[key] = batch[key].to(device)
            
            model.eval()
            with torch.no_grad():
                outputs = model(
                    batch['expression'],
                    batch['cell_graph'],
                    batch['gene_graph']
                )
            
            logger.info(f"   Forward pass successful")
            logger.info(f"   Reconstruction shape: {outputs.reconstruction.shape}")
            
            # Check for NaN/Inf in outputs
            if torch.isnan(outputs.reconstruction).any():
                errors.append("NaN values in reconstruction")
                logger.error("   ❌ NaN values detected in reconstruction")
            if torch.isinf(outputs.reconstruction).any():
                errors.append("Inf values in reconstruction")
                logger.error("   ❌ Inf values detected in reconstruction")
            
            # Check reconstruction statistics
            recon_zeros = (outputs.reconstruction == 0).float().mean().item()
            logger.info(f"   Reconstruction sparsity: {recon_zeros*100:.1f}%")
            if recon_zeros > 0.99:
                warnings.append(f"Reconstruction is {recon_zeros*100:.1f}% zeros")
            
        except Exception as e:
            error_msg = f"Forward pass failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 6. Loss computation
        logger.info("\n6. Testing loss computation...")
        try:
            loss, components = model.compute_loss(
                outputs,
                batch['expression'],
                return_components=True
            )
            
            logger.info(f"   Total loss: {loss.item():.4f}")
            for name, value in components.items():
                logger.info(f"   - {name}: {value.item():.4f}")
                if torch.isnan(value):
                    errors.append(f"NaN in {name} loss")
                    logger.error(f"   ❌ NaN detected in {name}")
                if torch.isinf(value):
                    errors.append(f"Inf in {name} loss")
                    logger.error(f"   ❌ Inf detected in {name}")
            
        except Exception as e:
            error_msg = f"Loss computation failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 7. Training step
        logger.info("\n7. Testing training step...")
        try:
            optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)  # Lower learning rate
            trainer = ScINTEGTrainer(
                model=model,
                optimizer=optimizer,
                device=device,
                gradient_clip_val=1.0  # Add gradient clipping
            )
            
            model.train()
            initial_loss = None
            losses = []
            
            for i, batch in enumerate(loaders['train']):
                if i >= 5:
                    break
                
                # Move batch to device
                for key in batch:
                    if torch.is_tensor(batch[key]):
                        batch[key] = batch[key].to(device)
                
                loss = trainer.training_step(batch, batch_idx=i)
                losses.append(loss)
                
                if initial_loss is None:
                    initial_loss = loss
                
                # Check gradient norms
                total_norm = 0
                for p in model.parameters():
                    if p.grad is not None:
                        param_norm = p.grad.data.norm(2)
                        total_norm += param_norm.item() ** 2
                total_norm = total_norm ** 0.5
                
                if total_norm > 100:
                    warnings.append(f"Large gradient norm: {total_norm:.1f}")
                    logger.warning(f"   Step {i}: Large gradient norm {total_norm:.1f}")
            
            logger.info(f"   Training successful")
            logger.info(f"   Initial loss: {initial_loss:.4f}")
            logger.info(f"   Final loss: {losses[-1]:.4f}")
            
            # Check if model is learning
            if losses[-1] >= initial_loss:
                warnings.append("Model not learning (loss not decreasing)")
                logger.warning("   ⚠️ Loss is not decreasing")
            
        except Exception as e:
            error_msg = f"Training failed: {str(e)}"
            logger.error(f"   ❌ {error_msg}")
            errors.append(error_msg)
            logger.debug(traceback.format_exc())
            return {'errors': errors, 'warnings': warnings, 'completed': False}
        
        # 8. Edge case tests
        logger.info("\n8. Testing edge cases...")
        
        # Test with all-zero batch
        logger.info("   Testing all-zero input...")
        try:
            zero_input = torch.zeros_like(batch['expression'])
            with torch.no_grad():
                zero_outputs = model(zero_input, batch['cell_graph'], batch['gene_graph'])
            
            if torch.isnan(zero_outputs.reconstruction).any():
                errors.append("NaN with all-zero input")
                logger.error("   ❌ NaN values with all-zero input")
            else:
                logger.info("   ✓ All-zero input handled correctly")
            
        except Exception as e:
            error_msg = f"All-zero test failed: {str(e)}"
            errors.append(error_msg)
            logger.error(f"   ❌ {error_msg}")
        
        # Test with single non-zero value
        logger.info("   Testing single non-zero value...")
        try:
            single_value = torch.zeros_like(batch['expression'])
            single_value[0, 0] = 1.0
            with torch.no_grad():
                single_outputs = model(single_value, batch['cell_graph'], batch['gene_graph'])
            
            if torch.isnan(single_outputs.reconstruction).any():
                errors.append("NaN with single non-zero value")
                logger.error("   ❌ NaN values with single non-zero value")
            else:
                logger.info("   ✓ Single value input handled correctly")
            
        except Exception as e:
            error_msg = f"Single value test failed: {str(e)}"
            errors.append(error_msg)
            logger.error(f"   ❌ {error_msg}")
        
        return {
            'sparsity': sparsity_level,
            'errors': errors,
            'warnings': warnings,
            'completed': True,
            'stats': data_dict['stats']
        }
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"\n❌ {error_msg}")
        logger.debug(traceback.format_exc())
        errors.append(error_msg)
        return {
            'sparsity': sparsity_level,
            'errors': errors,
            'warnings': warnings,
            'completed': False,
            'stats': data_dict['stats']
        }


def main():
    """Run extreme sparsity tests."""
    print("\n" + "="*80)
    print("ScINTEG v3 Extreme Sparsity Test")
    print("="*80)
    
    # Create test datasets
    sparsity_levels = [0.95, 0.98, 0.99, 0.995]
    datasets = create_extreme_sparse_data(sparsity_levels=sparsity_levels)
    
    # Run tests
    results = []
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"\nUsing device: {device}")
    
    for sparsity, data_dict in datasets.items():
        result = test_extreme_sparse_dataset(data_dict, sparsity, device)
        results.append(result)
    
    # Generate report
    print("\n" + "="*80)
    print("Extreme Sparsity Test Report")
    print("="*80)
    
    all_errors = []
    all_warnings = []
    
    for result in results:
        sparsity = result['sparsity']
        print(f"\n### {sparsity*100:.1f}% Sparsity")
        print(f"Completed: {'✅' if result['completed'] else '❌'}")
        
        if result.get('stats'):
            stats = result['stats']
            print(f"Mean genes/cell: {stats['mean_genes_per_cell']:.1f}")
            print(f"Mean cells/gene: {stats['mean_cells_per_gene']:.1f}")
        
        if result['errors']:
            print(f"\nErrors ({len(result['errors'])}):")
            for error in result['errors']:
                print(f"  - {error}")
                all_errors.append((sparsity, error))
        
        if result['warnings']:
            print(f"\nWarnings ({len(result['warnings'])}):")
            for warning in result['warnings']:
                print(f"  - {warning}")
                all_warnings.append((sparsity, warning))
    
    # Summary of root causes
    print("\n" + "="*80)
    print("Root Cause Analysis")
    print("="*80)
    
    if all_errors:
        print("\n🔴 Critical Issues Found:")
        
        # Group errors by type
        error_types = {}
        for sparsity, error in all_errors:
            error_type = error.split(':')[0]
            if error_type not in error_types:
                error_types[error_type] = []
            error_types[error_type].append((sparsity, error))
        
        for error_type, instances in error_types.items():
            print(f"\n{error_type}:")
            for sparsity, error in instances:
                print(f"  - At {sparsity*100:.1f}% sparsity: {error}")
        
        print("\n🔧 Suggested Fixes:")
        if 'NaN' in str(all_errors):
            print("  1. Add numerical stability checks in loss computation")
            print("  2. Use log1p instead of log for sparse data")
            print("  3. Add epsilon values to prevent division by zero")
        
        if 'correlation' in str(all_errors) or 'corrcoef' in str(all_errors):
            print("  1. Handle constant genes in correlation computation")
            print("  2. Use sparse correlation methods")
            print("  3. Filter out zero-variance features")
        
        if 'graph' in str(all_errors).lower():
            print("  1. Handle isolated nodes in GNN layers")
            print("  2. Add self-loops for isolated nodes")
            print("  3. Use minimum degree constraints")
    
    else:
        print("\n✅ No critical errors found!")
    
    if all_warnings:
        print("\n⚠️ Performance Concerns:")
        for sparsity, warning in all_warnings[:5]:  # Show first 5
            print(f"  - At {sparsity*100:.1f}%: {warning}")
    
    # Save detailed report
    report_path = Path(__file__).parent / "extreme_sparse_test_report.txt"
    with open(report_path, 'w') as f:
        f.write("ScINTEG v3 Extreme Sparsity Test Report\n")
        f.write("="*80 + "\n\n")
        
        for result in results:
            f.write(f"\nSparsity: {result['sparsity']*100:.1f}%\n")
            f.write(f"Completed: {result['completed']}\n")
            f.write(f"Errors: {len(result['errors'])}\n")
            f.write(f"Warnings: {len(result['warnings'])}\n")
            
            if result['errors']:
                f.write("\nErrors:\n")
                for error in result['errors']:
                    f.write(f"  - {error}\n")
            
            if result['warnings']:
                f.write("\nWarnings:\n")
                for warning in result['warnings']:
                    f.write(f"  - {warning}\n")
            
            f.write("\n" + "-"*40 + "\n")
    
    print(f"\n📄 Detailed report saved to: {report_path}")


if __name__ == "__main__":
    main()