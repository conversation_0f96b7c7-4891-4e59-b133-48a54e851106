"""
Clean test of ScINTEG v3 with PBMC3k raw data.

This script properly handles the data and focuses on identifying actual model issues.
"""

import torch
import numpy as np
import pandas as pd
import scanpy as sc
import time
import psutil
import os
import sys
import gc
import traceback
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import warnings
import logging

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.data.utils import create_pathway_mask_from_gmt
from scinteg.training import ScINTEGTrainer

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_and_prepare_data():
    """Load PBMC3k data and prepare it properly."""
    logger.info("Loading PBMC3k data...")
    
    # Load data
    adata = sc.read_h5ad("/mnt/f/文献汇报/gyd_developing/scinteg_0725/data/pbmc3k_processed.h5ad")
    logger.info(f"Loaded data shape: {adata.shape}")
    
    # Check data state
    logger.info(f"X data range: [{adata.X.min():.3f}, {adata.X.max():.3f}]")
    logger.info(f"X contains negatives: {(adata.X < 0).any()}")
    
    # Use raw data if available
    if adata.raw is not None:
        logger.info("Using raw count data from adata.raw")
        # Get raw data
        adata_raw = adata.raw.to_adata()
        
        # Filter to highly variable genes if needed
        if adata_raw.shape[1] > 3000:
            logger.info(f"Filtering from {adata_raw.shape[1]} to top 2000 highly variable genes")
            sc.pp.highly_variable_genes(adata_raw, n_top_genes=2000)
            adata_raw = adata_raw[:, adata_raw.var.highly_variable]
        
        # Use raw data
        expression = adata_raw.X
        gene_names = adata_raw.var_names.tolist()
        
        # Check if sparse
        if hasattr(expression, 'toarray'):
            expression = expression.toarray()
            
        logger.info(f"Raw data shape: {expression.shape}")
        logger.info(f"Raw data range: [{expression.min():.3f}, {expression.max():.3f}]")
    else:
        # Use X but skip normalization
        logger.info("No raw data available, using X without normalization")
        expression = adata.X
        gene_names = adata.var_names.tolist()
    
    return expression, gene_names, adata


def test_scinteg_with_real_data():
    """Main test function."""
    errors = []
    results = {}
    
    try:
        # Load data
        expression, gene_names, adata = load_and_prepare_data()
        results['data_shape'] = expression.shape
        results['data_range'] = (expression.min(), expression.max())
        
        # Load pathways
        logger.info("\nLoading pathway data...")
        pathway_mask, pathway_names = create_pathway_mask_from_gmt(
            "/mnt/f/文献汇报/gyd_developing/scinteg_0725/prior_data/msigdb/human/reactome.gmt",
            gene_names,
            min_genes=10,
            max_genes=500
        )
        logger.info(f"Loaded {len(pathway_names)} pathways")
        results['n_pathways'] = len(pathway_names)
        
        # Create dataset
        logger.info("\nCreating dataset...")
        expression_tensor = torch.FloatTensor(expression)
        
        # Determine if data needs preprocessing
        needs_normalization = expression.min() >= 0 and expression.max() > 100  # Likely raw counts
        needs_log = expression.max() > 100  # High values suggest non-log data
        
        logger.info(f"Normalization needed: {needs_normalization}")
        logger.info(f"Log transform needed: {needs_log}")
        
        dataset = ScINTEGDataset(
            expression_data=expression_tensor,
            pathway_mask=pathway_mask,
            normalize=needs_normalization,
            log_transform=needs_log,
            scale=False,
            build_cell_graph=True,
            build_gene_graph=True,
            cell_graph_k=15,
            gene_graph_threshold=0.1  # Lower threshold for dense data
        )
        
        logger.info(f"Dataset created: {dataset.n_cells} cells × {dataset.n_genes} genes")
        logger.info(f"Cell graph edges: {dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0}")
        logger.info(f"Gene graph edges: {dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0}")
        
        # Check for isolated nodes
        if dataset.gene_graph is not None:
            gene_degrees = torch.bincount(dataset.gene_graph.flatten(), minlength=dataset.n_genes)
            isolated_genes = (gene_degrees == 0).sum().item()
            logger.info(f"Isolated genes: {isolated_genes}/{dataset.n_genes} ({isolated_genes/dataset.n_genes*100:.1f}%)")
            results['isolated_genes_pct'] = isolated_genes / dataset.n_genes * 100
        
        # Test different model configurations
        configurations = [
            {
                'name': 'MSE_Simple',
                'reconstruction_loss_type': 'mse',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'use_hierarchical_pathways': False
            },
            {
                'name': 'NB_Simple',
                'reconstruction_loss_type': 'nb',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'use_hierarchical_pathways': False
            },
            {
                'name': 'ZINB_Simple',
                'reconstruction_loss_type': 'zinb',
                'cell_embedding_dim': 64,
                'gene_embedding_dim': 32,
                'use_hierarchical_pathways': False
            }
        ]
        
        for config in configurations:
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing {config['name']} configuration")
            logger.info(f"{'='*60}")
            
            try:
                # Create model
                model = ScINTEGv3(
                    n_cells=dataset.n_cells,
                    n_genes=dataset.n_genes,
                    n_pathways=dataset.n_pathways,
                    pathway_gene_mask=dataset.pathway_mask,
                    **{k: v for k, v in config.items() if k != 'name'}
                )
                
                n_params = sum(p.numel() for p in model.parameters())
                logger.info(f"Model parameters: {n_params:,}")
                
                # Test forward pass
                batch_size = 32
                test_expr = dataset.expression[:batch_size]
                test_cell_graph = dataset.get_cell_graph(torch.arange(batch_size))
                test_gene_graph = dataset.get_gene_graph()
                
                model.eval()
                with torch.no_grad():
                    outputs = model(test_expr, test_cell_graph, test_gene_graph)
                
                # Check outputs
                logger.info(f"Output shape: {outputs.reconstruction.shape}")
                logger.info(f"Output range: [{outputs.reconstruction.min():.3f}, {outputs.reconstruction.max():.3f}]")
                
                # Compute loss
                loss, loss_components = model.compute_loss(
                    outputs, test_expr, return_components=True
                )
                
                logger.info(f"Total loss: {loss.item():.4f}")
                for name, value in loss_components.items():
                    logger.info(f"  {name}: {value.item():.4f}")
                
                # Check for NaN
                if torch.isnan(loss):
                    logger.error(f"NaN loss detected for {config['name']}")
                    errors.append(f"{config['name']}: NaN loss")
                    
                    # Debug NaN source
                    logger.info("Debugging NaN source:")
                    logger.info(f"  Input contains NaN: {torch.isnan(test_expr).any()}")
                    logger.info(f"  Output contains NaN: {torch.isnan(outputs.reconstruction).any()}")
                    logger.info(f"  Cell embeddings NaN: {torch.isnan(outputs.cell_embeddings).any()}")
                    logger.info(f"  Gene embeddings NaN: {torch.isnan(outputs.gene_embeddings).any()}")
                    if hasattr(outputs, 'theta') and outputs.theta is not None:
                        logger.info(f"  Theta contains NaN: {torch.isnan(outputs.theta).any()}")
                    if hasattr(outputs, 'pi_logits') and outputs.pi_logits is not None:
                        logger.info(f"  Pi logits NaN: {torch.isnan(outputs.pi_logits).any()}")
                else:
                    logger.info(f"✓ {config['name']} forward pass successful")
                
                # Test training step
                logger.info(f"\nTesting training for {config['name']}...")
                optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
                trainer = ScINTEGTrainer(model=model, optimizer=optimizer, device='cpu')
                
                # Create small dataloader
                small_dataset = dataset.get_subset(torch.arange(min(200, dataset.n_cells)))
                loaders = create_data_loaders(
                    small_dataset,
                    batch_size=32,
                    train_ratio=0.7,
                    val_ratio=0.15,
                    test_ratio=0.15
                )
                
                # Train for a few steps
                model.train()
                train_losses = []
                for i, batch in enumerate(loaders['train']):
                    if i >= 5:
                        break
                    
                    loss = trainer.training_step(batch, batch_idx=i)
                    train_losses.append(loss)
                    logger.info(f"  Step {i}: loss = {loss:.4f}")
                
                if train_losses:
                    logger.info(f"  Training loss trend: {train_losses[0]:.4f} → {train_losses[-1]:.4f}")
                    if train_losses[-1] < train_losses[0]:
                        logger.info(f"  ✓ Loss decreasing")
                    else:
                        logger.warning(f"  ⚠ Loss not decreasing")
                
                results[config['name']] = {
                    'success': not torch.isnan(loss),
                    'loss': loss.item() if not torch.isnan(loss) else float('nan'),
                    'n_params': n_params
                }
                
            except Exception as e:
                logger.error(f"Error in {config['name']}: {str(e)}")
                logger.error(traceback.format_exc())
                errors.append(f"{config['name']}: {str(e)}")
                results[config['name']] = {'success': False, 'error': str(e)}
            
            # Cleanup
            del model
            gc.collect()
            
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        logger.error(traceback.format_exc())
        errors.append(f"Fatal: {str(e)}")
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    if errors:
        logger.info(f"\n❌ Found {len(errors)} errors:")
        for error in errors:
            logger.info(f"  - {error}")
    else:
        logger.info("\n✅ All tests passed!")
    
    logger.info(f"\nResults:")
    for key, value in results.items():
        logger.info(f"  {key}: {value}")
    
    return results, errors


if __name__ == "__main__":
    # Run test
    results, errors = test_scinteg_with_real_data()
    
    # Save results
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)
    
    # Save to file
    with open(output_dir / f"clean_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w') as f:
        f.write("ScINTEG v3 Clean Test Results\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("Results:\n")
        for key, value in results.items():
            f.write(f"  {key}: {value}\n")
        
        f.write(f"\nErrors ({len(errors)}):\n")
        for error in errors:
            f.write(f"  - {error}\n")