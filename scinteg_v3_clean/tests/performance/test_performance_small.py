"""
Performance test with small-scale data.

Tests ScINTEG v3 with a small dataset to verify basic functionality
and establish baseline performance metrics.
"""

import torch
import numpy as np
import time
import psutil
import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import ScIN<PERSON>GTrainer
from scinteg.training.config import create_default_config


def create_small_dataset(n_cells=100, n_genes=500, n_pathways=10):
    """Create a small synthetic dataset for testing."""
    print(f"\nCreating small dataset: {n_cells} cells, {n_genes} genes, {n_pathways} pathways")
    
    np.random.seed(42)
    
    # Create expression matrix with realistic structure
    n_types = 3
    cell_types = np.random.randint(0, n_types, n_cells)
    
    # Create type-specific expression patterns
    type_signatures = np.random.randn(n_types, n_genes) * 2
    
    # Generate expression data
    expression = np.zeros((n_cells, n_genes))
    for i in range(n_cells):
        base_expr = type_signatures[cell_types[i]]
        noise = np.random.randn(n_genes) * 0.5
        expression[i] = np.maximum(0, base_expr + noise)
    
    # Add some sparsity (30% zeros)
    mask = np.random.random((n_cells, n_genes)) < 0.3
    expression[mask] = 0
    
    # Create pathway mask
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = n_genes // n_pathways + 10
    
    for i in range(n_pathways):
        start = i * (n_genes // n_pathways)
        unique_genes = list(range(start, min(start + genes_per_pathway // 2, n_genes)))
        shared_genes = np.random.choice(n_genes, genes_per_pathway // 2, replace=False)
        pathway_genes = unique_genes + list(shared_genes)
        pathway_mask[i, pathway_genes[:genes_per_pathway]] = 1
    
    # Create batch labels (2 batches)
    batch_labels = np.array([0] * (n_cells // 2) + [1] * (n_cells - n_cells // 2))
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=torch.FloatTensor(expression),
        pathway_mask=pathway_mask,
        batch_labels=batch_labels,
        cell_types=cell_types,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=min(15, n_cells - 1),
        gene_graph_threshold=0.5
    )
    
    return dataset


def test_performance_small():
    """Run performance test with small dataset."""
    print("=" * 80)
    print("ScINTEG v3 Performance Test - Small Scale")
    print("=" * 80)
    
    # Memory tracking
    process = psutil.Process(os.getpid())
    start_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Create dataset
    dataset = create_small_dataset()
    print(f"Dataset created - Memory: {process.memory_info().rss / 1024 / 1024 - start_memory:.1f} MB")
    
    # Create data loaders
    loaders = create_data_loaders(
        dataset,
        batch_size=32,
        train_ratio=0.8,
        val_ratio=0.1,
        test_ratio=0.1
    )
    
    # Create model
    print("\nCreating model...")
    model = ScINTEGv3(
        n_cells=dataset.n_cells,
        n_genes=dataset.n_genes,
        n_pathways=dataset.n_pathways,
        pathway_gene_mask=dataset.pathway_mask,
        cell_embedding_dim=32,
        gene_embedding_dim=16,
        reconstruction_loss_type='mse',
        use_hierarchical_pathways=False,
        use_unet_decoder=False
    )
    
    n_params = sum(p.numel() for p in model.parameters())
    print(f"Model created with {n_params:,} parameters")
    print(f"Memory after model: {process.memory_info().rss / 1024 / 1024 - start_memory:.1f} MB")
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    # Create trainer
    trainer = ScINTEGTrainer(
        model=model,
        optimizer=optimizer,
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # Training metrics
    epoch_times = []
    losses = []
    memory_usage = []
    
    print("\nStarting training...")
    print("-" * 60)
    print(f"{'Epoch':<10} {'Time (s)':<15} {'Loss':<15} {'Memory (MB)':<15}")
    print("-" * 60)
    
    # Train for a few epochs
    n_epochs = 5
    for epoch in range(n_epochs):
        epoch_start = time.time()
        
        # Training epoch
        epoch_loss = 0
        n_batches = 0
        
        for batch in loaders['train']:
            loss = trainer.training_step(batch, batch_idx=n_batches)
            epoch_loss += loss
            n_batches += 1
        
        epoch_time = time.time() - epoch_start
        avg_loss = epoch_loss / n_batches
        current_memory = process.memory_info().rss / 1024 / 1024 - start_memory
        
        epoch_times.append(epoch_time)
        losses.append(avg_loss)
        memory_usage.append(current_memory)
        
        print(f"{epoch+1:<10} {epoch_time:<15.2f} {avg_loss:<15.4f} {current_memory:<15.1f}")
    
    # Validation
    print("\nRunning validation...")
    val_start = time.time()
    val_metrics = trainer._validate(loaders['val'])
    val_time = time.time() - val_start
    
    # Summary statistics
    print("\n" + "=" * 60)
    print("Performance Summary - Small Scale")
    print("=" * 60)
    print(f"Dataset size: {dataset.n_cells} cells × {dataset.n_genes} genes")
    print(f"Model parameters: {n_params:,}")
    print(f"Average epoch time: {np.mean(epoch_times):.2f} ± {np.std(epoch_times):.2f} s")
    print(f"Final loss: {losses[-1]:.4f}")
    print(f"Loss reduction: {(losses[0] - losses[-1]) / losses[0] * 100:.1f}%")
    print(f"Peak memory usage: {max(memory_usage):.1f} MB")
    print(f"Validation time: {val_time:.2f} s")
    print("\nValidation metrics:")
    for key, value in val_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # Check for issues
    print("\n" + "=" * 60)
    print("Stability Check")
    print("=" * 60)
    
    issues = []
    losses_np = [loss.detach().cpu().numpy() if torch.is_tensor(loss) else loss for loss in losses]
    if any(np.isnan(losses_np) or np.isinf(losses_np)):
        issues.append("NaN or Inf detected in losses")
    if max(memory_usage) > 1000:  # More than 1GB for small data
        issues.append(f"High memory usage: {max(memory_usage):.1f} MB")
    if np.mean(epoch_times) > 10:  # More than 10s per epoch
        issues.append(f"Slow training: {np.mean(epoch_times):.1f} s/epoch")
    
    if issues:
        print("⚠️  Issues detected:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ All checks passed!")
    
    return {
        'n_cells': dataset.n_cells,
        'n_genes': dataset.n_genes,
        'n_params': n_params,
        'epoch_times': epoch_times,
        'losses': losses,
        'memory_usage': memory_usage,
        'val_metrics': val_metrics,
        'issues': issues
    }


if __name__ == "__main__":
    results = test_performance_small()