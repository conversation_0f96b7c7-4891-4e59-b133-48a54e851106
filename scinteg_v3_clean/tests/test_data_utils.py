"""
Tests for data utility functions.

Following TDD principles - write tests first, then implement.
"""

import pytest
import torch
import numpy as np
from scinteg.data.utils import (
    propagate_features,
    add_noise_to_expression,
    load_prior_network_from_url
)


class TestFeaturePropagation:
    """Test network propagation functionality."""
    
    def test_propagate_features_basic(self):
        """Test basic feature propagation."""
        # Simple 3-node graph: 0-1-2
        adjacency = torch.tensor([
            [0., 1., 0.],
            [1., 0., 1.],
            [0., 1., 0.]
        ])
        
        # Initial features: only node 0 has signal
        features = torch.tensor([1., 0., 0.])
        
        # After propagation, signal should spread
        result = propagate_features(features, adjacency, alpha=0.5)
        
        # Node 1 should receive signal from node 0
        assert result[1] > 0
        # Signal should decay with distance
        assert result[0] > result[1] > result[2]
    
    def test_propagate_features_convergence(self):
        """Test that propagation converges."""
        # Complete graph
        adjacency = torch.ones(5, 5) - torch.eye(5)
        features = torch.randn(5)
        
        result = propagate_features(features, adjacency, alpha=0.9, max_iter=1000)
        
        # Should converge without NaN or Inf
        assert torch.isfinite(result).all()
    
    def test_propagate_features_isolated_nodes(self):
        """Test propagation with isolated nodes."""
        # Graph with isolated node
        adjacency = torch.tensor([
            [0., 1., 0.],
            [1., 0., 0.],
            [0., 0., 0.]  # Isolated
        ])
        
        features = torch.tensor([1., 1., 1.])
        result = propagate_features(features, adjacency, alpha=0.5)
        
        # Isolated node should retain its original value
        assert result[2] == features[2]


class TestDataAugmentation:
    """Test data augmentation functions."""
    
    def test_add_gaussian_noise(self):
        """Test adding Gaussian noise."""
        expression = torch.randn(100, 200)  # 100 cells, 200 genes
        
        # Use fixed seed for reproducibility
        noisy = add_noise_to_expression(expression, noise_level=0.1, noise_type='gaussian', random_seed=42)
        
        # Should have same shape
        assert noisy.shape == expression.shape
        # Should be different from original
        assert not torch.allclose(noisy, expression)
        # Correlation should still be high (adjust threshold for noise level)
        corr = torch.corrcoef(torch.stack([expression.flatten(), noisy.flatten()]))[0, 1]
        assert corr > 0.8  # More realistic for 0.1 noise level
    
    def test_add_dropout_noise(self):
        """Test adding dropout noise."""
        expression = torch.ones(50, 100) * 2.0
        
        noisy = add_noise_to_expression(expression, noise_level=0.2, noise_type='dropout')
        
        # About 20% should be zero
        zero_fraction = (noisy == 0).float().mean()
        assert 0.15 < zero_fraction < 0.25
        # Non-zero values should be unchanged
        assert torch.allclose(noisy[noisy != 0], expression[noisy != 0])


class TestNetworkLoading:
    """Test network loading functionality."""
    
    def test_load_prior_network_caching(self, tmp_path):
        """Test that network loading uses cache."""
        # Mock URL that returns a simple network
        mock_data = "from,to\nGENE1,GENE2\nGENE2,GENE3\n"
        
        # Use a real-looking URL for testing
        test_url = "http://example.com/test_network.csv"
        
        # First load should "download" (use test data)
        network1 = load_prior_network_from_url(
            url=test_url,
            cache_dir=tmp_path,
            _test_data=mock_data
        )
        
        # Second load should use cache (no test data needed)
        network2 = load_prior_network_from_url(
            url=test_url,
            cache_dir=tmp_path
        )
        
        # Should be same data
        assert network1.equals(network2)
        # Should have correct shape
        assert len(network1) == 2
        assert list(network1.columns) == ['from', 'to']
    
    def test_load_prior_network_format(self):
        """Test network format handling."""
        mock_data = "source\ttarget\nA\tB\nB\tC\n"
        
        network = load_prior_network_from_url(
            url="http://example.com/test.tsv",
            _test_data=mock_data,
            sep='\t'
        )
        
        # Should have correct columns
        assert 'from' in network.columns
        assert 'to' in network.columns
        assert len(network) == 2


if __name__ == "__main__":
    pytest.main([__file__])