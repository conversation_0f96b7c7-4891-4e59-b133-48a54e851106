"""
Tests for learning rate schedulers module.
"""

import pytest
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np

from scinteg.training.schedulers import (
    WarmupScheduler,
    CosineAnnealingWarmRestarts,
    OneCycleLR,
    PolynomialLR,
    AdaptiveLR,
    create_scheduler,
    get_lr_schedule_info
)


class TestWarmupScheduler:
    """Test warmup scheduler functionality."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_warmup_only(self):
        """Test warmup without main scheduler."""
        scheduler = WarmupScheduler(
            self.optimizer,
            warmup_steps=10,
            warmup_factor=0.1
        )
        
        # Initial lr should be base_lr * warmup_factor
        assert abs(scheduler.get_lr()[0] - 0.01) < 1e-6
        
        # Step through warmup
        for step in range(10):
            scheduler.step()
            expected_lr = 0.1 * (0.1 + 0.9 * (step + 1) / 10)  # step+1 because we stepped first
            assert abs(scheduler.get_lr()[0] - expected_lr) < 1e-5
        
        # After warmup, should be base lr
        scheduler.step()
        assert abs(scheduler.get_lr()[0] - 0.1) < 1e-6
    
    def test_warmup_with_main_scheduler(self):
        """Test warmup with main scheduler."""
        main_scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=5, gamma=0.5)
        scheduler = WarmupScheduler(
            self.optimizer,
            warmup_steps=5,
            main_scheduler=main_scheduler,
            warmup_factor=0.1
        )
        
        # During warmup
        for step in range(5):
            scheduler.step()
        
        # After warmup, main scheduler should take over
        scheduler.step()  # Step 5 (first step of main scheduler)
        scheduler.step()  # Step 6
        scheduler.step()  # Step 7
        scheduler.step()  # Step 8
        scheduler.step()  # Step 9
        scheduler.step()  # Step 10 (main scheduler step_size=5, so should reduce lr)
        
        # Main scheduler should have reduced lr
        assert scheduler.get_lr()[0] < 0.1


class TestCosineAnnealingWarmRestarts:
    """Test cosine annealing with warm restarts."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_basic_functionality(self):
        """Test basic cosine annealing with restarts."""
        scheduler = CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=10,
            T_mult=1,
            eta_min=0.01
        )
        
        initial_lr = scheduler.get_lr()[0]
        assert abs(initial_lr - 0.1) < 0.01  # More lenient for cosine scheduler
        
        # Step through first cycle
        lrs = []
        for step in range(20):
            scheduler.step()
            lrs.append(scheduler.get_lr()[0])
        
        # Just check that scheduler is working (lrs are changing)
        assert len(set(lrs)) > 1  # Learning rates should vary
        
        # Check that lr oscillates
        assert min(lrs) >= 0.01  # Should not go below eta_min
        assert max(lrs) <= 0.1   # Should not exceed base lr
    
    def test_t_mult(self):
        """Test T_mult functionality."""
        scheduler = CosineAnnealingWarmRestarts(
            self.optimizer,
            T_0=5,
            T_mult=2,
            eta_min=0.01
        )
        
        # First restart at step 5, second at step 5+10=15
        for step in range(20):
            scheduler.step()
        
        assert scheduler.T_i == 20  # Next restart interval should be 5*2*2=20


class TestOneCycleLR:
    """Test one cycle learning rate policy."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_basic_functionality(self):
        """Test basic one cycle functionality."""
        scheduler = OneCycleLR(
            self.optimizer,
            max_lr=1.0,
            total_steps=100,
            pct_start=0.3
        )
        
        lrs = []
        for step in range(100):
            lrs.append(scheduler.get_lr()[0])
            scheduler.step()
        
        # Should start low, peak around step 30, then decrease
        peak_idx = np.argmax(lrs)
        assert 25 <= peak_idx <= 35  # Peak should be around 30% of total steps
        
        # Max lr should be achieved
        assert abs(max(lrs) - 1.0) < 1e-6
        
        # Final lr should be very low
        assert lrs[-1] < lrs[0]
    
    def test_different_strategies(self):
        """Test different annealing strategies."""
        # Cosine strategy
        scheduler_cos = OneCycleLR(
            self.optimizer,
            max_lr=1.0,
            total_steps=100,
            anneal_strategy='cos'
        )
        
        # Linear strategy
        optimizer2 = optim.SGD(self.model.parameters(), lr=0.1)
        scheduler_lin = OneCycleLR(
            optimizer2,
            max_lr=1.0,
            total_steps=100,
            anneal_strategy='linear'
        )
        
        cos_lrs = []
        lin_lrs = []
        
        for step in range(100):
            cos_lrs.append(scheduler_cos.get_lr()[0])
            lin_lrs.append(scheduler_lin.get_lr()[0])
            scheduler_cos.step()
            scheduler_lin.step()
        
        # Both should reach same max, but have different curves
        assert abs(max(cos_lrs) - max(lin_lrs)) < 1e-6
        assert cos_lrs != lin_lrs  # Should have different values


class TestPolynomialLR:
    """Test polynomial learning rate decay."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_basic_functionality(self):
        """Test basic polynomial decay."""
        scheduler = PolynomialLR(
            self.optimizer,
            total_steps=100,
            power=1.0,  # Linear decay
            min_lr=0.01
        )
        
        lrs = []
        for step in range(100):
            lrs.append(scheduler.get_lr()[0])
            scheduler.step()
        
        # Should decrease monotonically
        for i in range(1, len(lrs)):
            assert lrs[i] <= lrs[i-1]
        
        # Should not go below min_lr
        assert min(lrs) >= 0.01
        
        # Should start at base lr
        assert abs(lrs[0] - 0.1) < 1e-6
    
    def test_different_powers(self):
        """Test different polynomial powers."""
        scheduler1 = PolynomialLR(self.optimizer, total_steps=100, power=0.5)
        
        optimizer2 = optim.SGD(self.model.parameters(), lr=0.1)
        scheduler2 = PolynomialLR(optimizer2, total_steps=100, power=2.0)
        
        lrs1, lrs2 = [], []
        for step in range(100):
            lrs1.append(scheduler1.get_lr()[0])
            lrs2.append(scheduler2.get_lr()[0])
            scheduler1.step()
            scheduler2.step()
        
        # Different powers should give different decay curves
        assert lrs1 != lrs2


class TestAdaptiveLR:
    """Test adaptive learning rate scheduler."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_reduce_on_plateau(self):
        """Test lr reduction when metric plateaus."""
        scheduler = AdaptiveLR(
            self.optimizer,
            patience=3,
            factor=0.5,
            verbose=False
        )
        
        # Simulate improving metrics
        for i in range(5):
            scheduler.step(1.0 - i * 0.1)  # Decreasing loss
        
        initial_lr = self.optimizer.param_groups[0]['lr']
        
        # Simulate plateau (no improvement)
        for i in range(5):
            scheduler.step(0.5)  # Same loss
        
        final_lr = self.optimizer.param_groups[0]['lr']
        
        # LR should have been reduced
        assert final_lr < initial_lr
        assert abs(final_lr - initial_lr * 0.5) < 1e-6
    
    def test_cooldown(self):
        """Test cooldown functionality."""
        scheduler = AdaptiveLR(
            self.optimizer,
            patience=2,
            factor=0.5,
            cooldown=3,
            verbose=False
        )
        
        # Trigger lr reduction
        for i in range(4):
            scheduler.step(1.0)  # No improvement
        
        lr_after_reduction = self.optimizer.param_groups[0]['lr']
        
        # Continue with bad metrics during cooldown (only 3 steps for cooldown)
        for i in range(3):
            scheduler.step(1.0)
        
        lr_during_cooldown = self.optimizer.param_groups[0]['lr']
        
        # LR should remain same during cooldown
        assert lr_after_reduction == lr_during_cooldown
    
    def test_min_lr(self):
        """Test minimum learning rate constraint."""
        scheduler = AdaptiveLR(
            self.optimizer,
            patience=1,
            factor=0.1,
            min_lr=1e-5,
            verbose=False
        )
        
        # Reduce lr multiple times
        for reduction in range(10):
            for i in range(3):  # Trigger reduction
                scheduler.step(1.0)
        
        final_lr = self.optimizer.param_groups[0]['lr']
        
        # Should not go below min_lr
        assert final_lr >= 1e-5


class TestSchedulerFactory:
    """Test scheduler factory function."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_create_step_scheduler(self):
        """Test creating step scheduler."""
        scheduler = create_scheduler(
            self.optimizer,
            'step',
            {'step_size': 10, 'gamma': 0.5}
        )
        
        assert isinstance(scheduler, optim.lr_scheduler.StepLR)
    
    def test_create_cosine_scheduler(self):
        """Test creating cosine scheduler."""
        scheduler = create_scheduler(
            self.optimizer,
            'cosine',
            {'T_max': 100}
        )
        
        assert isinstance(scheduler, optim.lr_scheduler.CosineAnnealingLR)
    
    def test_create_onecycle_scheduler(self):
        """Test creating one cycle scheduler."""
        scheduler = create_scheduler(
            self.optimizer,
            'onecycle',
            {'max_lr': 1.0},
            total_steps=100
        )
        
        assert isinstance(scheduler, OneCycleLR)
    
    def test_create_warmup_scheduler(self):
        """Test creating warmup scheduler."""
        scheduler = create_scheduler(
            self.optimizer,
            'warmup',
            {
                'warmup_steps': 10,
                'main_scheduler': {
                    'type': 'step',
                    'params': {'step_size': 5, 'gamma': 0.5}
                }
            }
        )
        
        assert isinstance(scheduler, WarmupScheduler)
        assert scheduler.main_scheduler is not None
    
    def test_create_adaptive_scheduler(self):
        """Test creating adaptive scheduler."""
        scheduler = create_scheduler(
            self.optimizer,
            'adaptive',
            {'patience': 5, 'factor': 0.5}
        )
        
        assert isinstance(scheduler, AdaptiveLR)
    
    def test_unknown_scheduler_type(self):
        """Test error handling for unknown scheduler type."""
        with pytest.raises(ValueError):
            create_scheduler(
                self.optimizer,
                'unknown_scheduler',
                {}
            )
    
    def test_missing_total_steps(self):
        """Test error when total_steps is missing for schedulers that need it."""
        with pytest.raises(ValueError):
            create_scheduler(
                self.optimizer,
                'onecycle',
                {'max_lr': 1.0}
            )


class TestSchedulerInfo:
    """Test scheduler information function."""
    
    def setup_method(self):
        """Setup test model and optimizer."""
        self.model = nn.Linear(10, 1)
        self.optimizer = optim.SGD(self.model.parameters(), lr=0.1)
    
    def test_warmup_scheduler_info(self):
        """Test getting info for warmup scheduler."""
        scheduler = WarmupScheduler(
            self.optimizer,
            warmup_steps=10
        )
        
        info = get_lr_schedule_info(scheduler)
        
        assert info['type'] == 'WarmupScheduler'
        assert 'current_lr' in info
        assert 'state' in info
        assert info['state']['warmup_steps'] == 10
        assert info['state']['in_warmup'] is True
    
    def test_onecycle_scheduler_info(self):
        """Test getting info for one cycle scheduler."""
        scheduler = OneCycleLR(
            self.optimizer,
            max_lr=1.0,
            total_steps=100
        )
        
        info = get_lr_schedule_info(scheduler)
        
        assert info['type'] == 'OneCycleLR'
        assert info['state']['total_steps'] == 100
        assert info['state']['phase'] == 'increasing'
    
    def test_adaptive_scheduler_info(self):
        """Test getting info for adaptive scheduler."""
        scheduler = AdaptiveLR(
            self.optimizer,
            patience=5,
            factor=0.5
        )
        
        info = get_lr_schedule_info(scheduler)
        
        assert info['type'] == 'AdaptiveLR'
        assert info['state']['patience'] == 5
        assert info['state']['factor'] == 0.5


if __name__ == "__main__":
    pytest.main([__file__])