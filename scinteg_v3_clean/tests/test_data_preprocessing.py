"""
Tests for data preprocessing and validation modules.
"""

import pytest
import torch
import numpy as np
from scinteg.data.validation import DataValidator, validate_dataset, ValidationReport
from scinteg.data.preprocessing import (
    PreprocessingPipeline,
    PreprocessingConfig,
    preprocess_data
)


class TestDataValidator:
    """Test data validation functionality."""
    
    def test_basic_validation(self):
        """Test basic data validation."""
        validator = DataValidator()
        
        # Create test data
        expression = np.random.lognormal(0, 1, (100, 500))
        gene_names = [f"Gene_{i}" for i in range(500)]
        cell_names = [f"Cell_{i}" for i in range(100)]
        batch_labels = [0] * 50 + [1] * 50
        
        report = validator.validate(
            expression, gene_names, cell_names, batch_labels
        )
        
        # Check report structure
        assert isinstance(report, ValidationReport)
        assert isinstance(report.passed, bool)
        assert isinstance(report.warnings, list)
        assert isinstance(report.errors, list)
        assert isinstance(report.metrics, dict)
        assert isinstance(report.recommendations, list)
        
        # Check basic metrics
        assert report.metrics['n_cells'] == 100
        assert report.metrics['n_genes'] == 500
        assert report.metrics['has_gene_names'] is True
        assert report.metrics['has_batch_labels'] is True
    
    def test_validation_thresholds(self):
        """Test validation with quality thresholds."""
        validator = DataValidator(
            min_cells_per_gene=5,
            min_genes_per_cell=50,
            min_umi_per_cell=500
        )
        
        # Create low-quality data
        expression = np.random.poisson(0.1, (50, 100))  # Very sparse
        
        report = validator.validate(expression)
        
        # Should detect quality issues
        assert report.metrics['zero_expression_pct'] > 50
        assert len(report.warnings) > 0 or len(report.errors) > 0
    
    def test_batch_effect_detection(self):
        """Test batch effect detection."""
        validator = DataValidator()
        
        # Create data with batch effects
        n_cells_per_batch = 50
        n_genes = 200
        
        batch1 = np.random.lognormal(0, 1, (n_cells_per_batch, n_genes))
        batch2 = np.random.lognormal(1, 1, (n_cells_per_batch, n_genes))  # Different mean
        
        expression = np.vstack([batch1, batch2])
        batch_labels = [0] * n_cells_per_batch + [1] * n_cells_per_batch
        
        report = validator.validate(expression, batch_labels=batch_labels)
        
        # Should detect batch effects
        assert 'batch_effect_strength' in report.metrics
        assert report.metrics['n_batches'] == 2
    
    def test_outlier_detection(self):
        """Test outlier detection."""
        validator = DataValidator(outlier_threshold=2.0)
        
        # Create normal data with outliers
        normal_expr = np.random.lognormal(0, 1, (95, 100))
        outlier_expr = np.random.lognormal(3, 1, (5, 100))  # Much higher expression
        
        expression = np.vstack([normal_expr, outlier_expr])
        
        report = validator.validate(expression)
        
        # Should detect outliers
        assert 'outlier_cells_pct' in report.metrics
        assert report.metrics['outlier_cells_pct'] > 0
    
    def test_validation_with_missing_data(self):
        """Test validation with missing metadata."""
        validator = DataValidator()
        
        expression = np.random.lognormal(0, 1, (100, 200))
        
        # No metadata
        report = validator.validate(expression)
        
        assert report.metrics['has_gene_names'] is False
        assert report.metrics['has_batch_labels'] is False
        assert report.metrics['has_cell_types'] is False


class TestPreprocessingPipeline:
    """Test preprocessing pipeline functionality."""
    
    def test_basic_preprocessing(self):
        """Test basic preprocessing pipeline."""
        config = PreprocessingConfig(
            validate_data=True,
            filter_cells=True,
            filter_genes=True,
            normalize=True,
            build_cell_graph=False,  # Skip for speed
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create test data
        expression = np.random.lognormal(0, 1, (200, 1000))
        gene_names = [f"Gene_{i}" for i in range(1000)]
        cell_names = [f"Cell_{i}" for i in range(200)]
        
        result = pipeline.fit_transform(
            expression, gene_names, cell_names
        )
        
        # Check output structure
        assert 'expression' in result
        assert 'gene_names' in result
        assert 'cell_names' in result
        assert 'processing_log' in result
        assert 'validation_report' in result
        
        # Check data shapes
        assert isinstance(result['expression'], torch.Tensor)
        assert result['expression'].ndim == 2
        assert result['final_shape'][0] <= 200  # Some cells may be filtered
        assert result['final_shape'][1] <= 1000  # Some genes may be filtered
    
    def test_gene_filtering(self):
        """Test gene filtering functionality."""
        config = PreprocessingConfig(
            filter_genes=True,
            min_cells_per_gene=10,
            filter_cells=False,
            normalize=False,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create data with some lowly expressed genes
        expression = np.random.lognormal(0, 1, (100, 200))
        # Make some genes very lowly expressed
        expression[:, :50] = np.random.poisson(0.01, (100, 50))
        
        gene_names = [f"Gene_{i}" for i in range(200)]
        
        result = pipeline.fit_transform(expression, gene_names)
        
        # Should filter out lowly expressed genes
        assert result['final_shape'][1] < 200
        assert len(result['gene_names']) == result['final_shape'][1]
    
    def test_cell_filtering(self):
        """Test cell filtering functionality."""
        config = PreprocessingConfig(
            filter_cells=True,
            min_umi_per_cell=1000,
            max_umi_per_cell=50000,
            min_genes_per_cell=100,
            filter_genes=False,
            normalize=False,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create data with some low-quality cells
        good_cells = np.random.lognormal(0, 1, (80, 500))
        bad_cells = np.random.poisson(0.1, (20, 500))  # Very low UMI
        
        expression = np.vstack([good_cells, bad_cells])
        cell_names = [f"Cell_{i}" for i in range(100)]
        
        result = pipeline.fit_transform(expression, cell_names=cell_names)
        
        # Should filter out low-quality cells
        assert result['final_shape'][0] < 100
        assert len(result['cell_names']) == result['final_shape'][0]
    
    def test_normalization(self):
        """Test normalization functionality."""
        config = PreprocessingConfig(
            filter_cells=False,
            filter_genes=False,
            normalize=True,
            normalization_method='library_size',
            log_transform=True,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create test data
        expression = np.random.lognormal(0, 1, (50, 100))
        
        result = pipeline.fit_transform(expression)
        
        # Check normalization was applied
        normalized_expr = result['expression'].numpy()
        
        # After log transformation, should not have extreme values
        assert normalized_expr.min() >= 0
        assert normalized_expr.max() < 10  # Reasonable after log transform
        
        # Library sizes should be more uniform after normalization
        original_lib_sizes = expression.sum(axis=1)
        normalized_lib_sizes = np.exp(normalized_expr).sum(axis=1)
        
        assert normalized_lib_sizes.std() < original_lib_sizes.std()
    
    def test_outlier_removal(self):
        """Test outlier removal functionality."""
        config = PreprocessingConfig(
            filter_cells=False,
            filter_genes=False,
            remove_outliers=True,
            outlier_threshold=2.0,
            normalize=False,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create normal data with outliers
        normal_expr = np.random.lognormal(0, 1, (95, 100))
        outlier_expr = np.random.lognormal(4, 1, (5, 100))  # Extreme outliers
        
        expression = np.vstack([normal_expr, outlier_expr])
        
        result = pipeline.fit_transform(expression)
        
        # Should remove some outliers
        assert result['final_shape'][0] < 100
    
    def test_highly_variable_genes(self):
        """Test highly variable gene selection."""
        config = PreprocessingConfig(
            filter_cells=False,
            filter_genes=False,
            highly_variable_genes=True,
            n_top_genes=100,
            normalize=False,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        pipeline = PreprocessingPipeline(config)
        
        # Create data with varying gene variability
        low_var_genes = np.random.normal(1, 0.1, (50, 400))  # Low variance
        high_var_genes = np.random.normal(1, 2, (50, 100))   # High variance
        
        expression = np.hstack([low_var_genes, high_var_genes])
        gene_names = [f"Gene_{i}" for i in range(500)]
        
        result = pipeline.fit_transform(expression, gene_names)
        
        # Should select top variable genes
        assert result['final_shape'][1] == 100
        assert len(result['gene_names']) == 100
    
    def test_graph_construction(self):
        """Test graph construction."""
        config = PreprocessingConfig(
            filter_cells=False,
            filter_genes=False,
            normalize=False,
            build_cell_graph=True,
            build_gene_graph=True,
            cell_graph_k=10,
            gene_graph_threshold=0.1
        )
        
        pipeline = PreprocessingPipeline(config)
        
        expression = np.random.lognormal(0, 1, (100, 200))
        
        result = pipeline.fit_transform(expression)
        
        # Check graphs were constructed
        assert result['cell_graph'] is not None
        assert result['gene_graph'] is not None
        assert isinstance(result['cell_graph'], torch.Tensor)
        assert isinstance(result['gene_graph'], torch.Tensor)
        assert result['cell_graph'].shape[0] == 2  # Edge list format
        assert result['gene_graph'].shape[0] == 2


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_validate_dataset_function(self):
        """Test validate_dataset convenience function."""
        expression = np.random.lognormal(0, 1, (100, 200))
        
        report = validate_dataset(
            expression,
            create_plots=False  # Skip plots in test
        )
        
        assert isinstance(report, ValidationReport)
        assert report.metrics['n_cells'] == 100
        assert report.metrics['n_genes'] == 200
    
    def test_preprocess_data_function(self):
        """Test preprocess_data convenience function."""
        expression = np.random.lognormal(0, 1, (100, 200))
        gene_names = [f"Gene_{i}" for i in range(200)]
        
        result = preprocess_data(
            expression,
            gene_names=gene_names,
            normalize=True,
            build_cell_graph=False,
            build_gene_graph=False
        )
        
        assert 'expression' in result
        assert 'gene_names' in result
        assert isinstance(result['expression'], torch.Tensor)
    
    def test_preprocessing_config(self):
        """Test preprocessing configuration."""
        config = PreprocessingConfig(
            min_cells_per_gene=5,
            normalize=True,
            normalization_method='cpm',
            build_cell_graph=True,
            cell_graph_k=20
        )
        
        assert config.min_cells_per_gene == 5
        assert config.normalize is True
        assert config.normalization_method == 'cpm'
        assert config.build_cell_graph is True
        assert config.cell_graph_k == 20


if __name__ == "__main__":
    pytest.main([__file__])