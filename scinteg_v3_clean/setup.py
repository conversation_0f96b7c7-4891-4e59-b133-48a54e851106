"""
ScINTEG v3 - Single-cell Integrative Gene Regulatory Network Inference
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="scinteg",
    version="3.0.0",
    author="ScINTEG Team",
    author_email="",
    description="Single-cell Integrative Gene Regulatory Network Inference",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/scinteg",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
    install_requires=[
        "torch>=1.10.0",
        "numpy>=1.20.0",
        "scipy>=1.7.0",
        "scikit-learn>=0.24.0",
        "pandas>=1.3.0",
        "scanpy>=1.8.0",
        "anndata>=0.8.0",
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "tqdm>=4.62.0",
        "pyyaml>=5.4.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "pytest-cov>=2.12.0",
            "black>=21.6b0",
            "flake8>=3.9.0",
            "mypy>=0.910",
            "sphinx>=4.0.0",
        ],
        "gpu": [
            "torch-geometric>=2.0.0",
            "torch-sparse>=0.6.0",
            "torch-scatter>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "scinteg=scinteg.cli:main",
        ],
    },
)