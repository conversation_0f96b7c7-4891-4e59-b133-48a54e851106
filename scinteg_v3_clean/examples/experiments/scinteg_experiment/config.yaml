data:
  balance_by: batch
  balanced_sampling: false
  batch_size: 32
  build_cell_graph: true
  build_gene_graph: true
  cell_graph_k: 15
  data_path: null
  dropout_rate: 0.0
  expression_noise: 0.0
  gene_graph_threshold: 0.5
  log_transform: true
  normalize: true
  num_workers: 4
  pin_memory: true
  rebuild_graph_per_batch: true
  scale: false
  test_data: null
  test_ratio: 0.1
  train_data: null
  train_ratio: 0.8
  val_data: null
  val_ratio: 0.1
description: ''
model:
  cell_embedding_dim: 128
  cell_encoder_config: {}
  cell_encoder_type: standard
  cell_hidden_dim: 256
  decoder_config: {}
  dropout: 0.1
  gene_embedding_dim: 64
  gene_encoder_config: {}
  gene_encoder_type: standard
  gene_hidden_dim: 128
  grn_predictor_config: {}
  grn_weight: 0.1
  n_genes: 2000
  n_meta_pathways: null
  n_pathways: 50
  pathway_sparsity_weight: 0.01
  projector_config: {}
  reconstruction_loss_type: mse
  reconstruction_weight: 1.0
  use_adaptive_weighting: false
  use_hierarchical_pathways: false
  use_unet_decoder: false
name: scinteg_experiment
output_dir: ./experiments
training:
  accumulate_grad_batches: 1
  batch_size: 32
  checkpoint_dir: experiments/scinteg_experiment/checkpoints
  checkpoint_mode: min
  checkpoint_monitor: val_loss
  deterministic: false
  device: cuda
  early_stopping: true
  early_stopping_min_delta: 0.0001
  early_stopping_mode: min
  early_stopping_patience: 20
  epochs: 1
  gradient_clip_val: 1.0
  log_dir: experiments/scinteg_experiment/logs
  log_interval: 10
  mixed_precision: false
  num_workers: 4
  optimizer:
    betas: !!python/tuple
    - 0.9
    - 0.999
    eps: 1.0e-08
    lr: 0.001
    momentum: 0.9
    name: adam
    nesterov: false
    weight_decay: 0.0
  pin_memory: true
  save_last: true
  save_top_k: 3
  scheduler:
    T_max: null
    cooldown: 0
    eta_min: 0.0
    factor: 0.1
    gamma: 0.1
    min_lr: 0.0
    mode: min
    name: null
    patience: 10
    step_size: 30
    threshold: 0.0001
  seed: 42
  use_tensorboard: true
  use_wandb: false
  val_batch_size: null
  val_check_interval: 1
  wandb_project: null
