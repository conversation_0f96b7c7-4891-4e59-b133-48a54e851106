"""
Example of batch-aware data augmentation.

This example demonstrates:
1. Creating data with batch effects
2. Using batch-aware augmentation strategies
3. Visualizing the effects of augmentation
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.data import (
    ScINTEGDataset,
    ScINTEGDataLoader,
    DataAugmenter,
    BatchAwareAugmenter,
    create_augmentation_pipeline
)


def create_batch_effect_data(n_cells_per_batch=100, n_genes=500, n_batches=3):
    """Create synthetic data with batch effects."""
    all_expression = []
    all_batch_labels = []
    
    np.random.seed(42)
    
    # Base expression pattern
    base_expression = np.random.lognormal(0, 1, (n_cells_per_batch, n_genes))
    
    for batch_id in range(n_batches):
        # Add batch-specific effects
        batch_effect = 1.0 + (batch_id - n_batches/2) * 0.2  # Scale effect
        batch_shift = np.random.randn(n_genes) * 0.5  # Gene-specific shifts
        
        batch_expr = base_expression * batch_effect + batch_shift
        batch_expr = np.maximum(batch_expr, 0)  # Ensure non-negative
        
        all_expression.append(batch_expr)
        all_batch_labels.extend([batch_id] * n_cells_per_batch)
    
    expression = np.vstack(all_expression)
    batch_labels = np.array(all_batch_labels)
    
    return expression, batch_labels


def visualize_augmentation_effects(
    original_expr,
    augmented_expr,
    batch_labels,
    title="Augmentation Effects"
):
    """Visualize the effects of augmentation on expression data."""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. Expression distribution by batch (original)
    ax = axes[0, 0]
    for batch_id in np.unique(batch_labels):
        batch_mask = batch_labels == batch_id
        ax.hist(
            original_expr[batch_mask].flatten(),
            bins=50,
            alpha=0.5,
            label=f'Batch {batch_id}',
            density=True
        )
    ax.set_xlabel('Expression Level')
    ax.set_ylabel('Density')
    ax.set_title('Original Expression Distribution')
    ax.legend()
    
    # 2. Expression distribution by batch (augmented)
    ax = axes[0, 1]
    for batch_id in np.unique(batch_labels):
        batch_mask = batch_labels == batch_id
        ax.hist(
            augmented_expr[batch_mask].flatten(),
            bins=50,
            alpha=0.5,
            label=f'Batch {batch_id}',
            density=True
        )
    ax.set_xlabel('Expression Level')
    ax.set_ylabel('Density')
    ax.set_title('Augmented Expression Distribution')
    ax.legend()
    
    # 3. PCA visualization (original)
    from sklearn.decomposition import PCA
    pca = PCA(n_components=2)
    
    ax = axes[1, 0]
    pca_orig = pca.fit_transform(original_expr)
    for batch_id in np.unique(batch_labels):
        batch_mask = batch_labels == batch_id
        ax.scatter(
            pca_orig[batch_mask, 0],
            pca_orig[batch_mask, 1],
            alpha=0.5,
            label=f'Batch {batch_id}'
        )
    ax.set_xlabel('PC1')
    ax.set_ylabel('PC2')
    ax.set_title('PCA - Original Data')
    ax.legend()
    
    # 4. PCA visualization (augmented)
    ax = axes[1, 1]
    pca_aug = pca.fit_transform(augmented_expr)
    for batch_id in np.unique(batch_labels):
        batch_mask = batch_labels == batch_id
        ax.scatter(
            pca_aug[batch_mask, 0],
            pca_aug[batch_mask, 1],
            alpha=0.5,
            label=f'Batch {batch_id}'
        )
    ax.set_xlabel('PC1')
    ax.set_ylabel('PC2')
    ax.set_title('PCA - Augmented Data')
    ax.legend()
    
    plt.suptitle(title)
    plt.tight_layout()
    plt.savefig('batch_augmentation_effects.png', dpi=150)
    plt.close()


def main():
    """Main example function."""
    print("Batch-Aware Augmentation Example")
    print("=" * 50)
    
    # 1. Create synthetic data with batch effects
    print("\n1. Creating synthetic data with batch effects...")
    expression, batch_labels = create_batch_effect_data(
        n_cells_per_batch=100,
        n_genes=500,
        n_batches=3
    )
    
    print(f"   Created dataset: {expression.shape[0]} cells, {expression.shape[1]} genes")
    print(f"   Batches: {np.unique(batch_labels)}")
    
    # 2. Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        batch_labels=batch_labels,
        normalize=True,
        log_transform=True
    )
    
    # 3. Standard augmentation
    print("\n2. Applying standard augmentation...")
    standard_augmenter = DataAugmenter(
        noise_level=0.1,
        dropout_rate=0.1,
        mixup_alpha=0.2,
        augment_prob=1.0
    )
    
    batch = {
        'expression': torch.FloatTensor(dataset.expression),
        'batch_labels': torch.LongTensor(batch_labels)
    }
    
    standard_augmented = standard_augmenter(batch.copy(), augment_type='mixup')
    
    # 4. Batch-aware augmentation
    print("\n3. Applying batch-aware augmentation...")
    batch_aware_augmenter = BatchAwareAugmenter(
        cross_batch_mixup=True,
        batch_specific_noise=True,
        mixup_alpha=0.3,
        noise_level=0.1,
        augment_prob=1.0
    )
    
    batch_augmented = batch_aware_augmenter(batch.copy(), augment_type='batch_mixup')
    
    # 5. Create data loader with augmentation
    print("\n4. Creating data loader with augmentation...")
    
    # Configure augmentation pipeline
    augmentation_config = {
        'cross_batch_mixup': True,
        'batch_specific_noise': True,
        'mixup_alpha': 0.3,
        'noise_level': 0.1,
        'augment_prob': 0.5
    }
    
    augmenter = create_augmentation_pipeline(
        augmentation_config,
        batch_aware=True
    )
    
    # Create balanced data loader
    dataloader = ScINTEGDataLoader(
        dataset,
        batch_size=32,
        balanced_sampling=True,
        balance_by='batch',
        shuffle=True
    )
    
    # 6. Demonstrate augmentation in training loop
    print("\n5. Simulating training with augmentation...")
    for i, batch_data in enumerate(dataloader):
        if i >= 3:  # Just show first 3 batches
            break
            
        # Apply augmentation
        augmented_batch = augmenter(batch_data)
        
        print(f"   Batch {i+1}:")
        print(f"     Original shape: {batch_data['expression'].shape}")
        print(f"     Batch labels: {batch_data['batch_labels'].unique().tolist()}")
        print(f"     Expression range: [{batch_data['expression'].min():.3f}, "
              f"{batch_data['expression'].max():.3f}]")
        print(f"     Augmented range: [{augmented_batch['expression'].min():.3f}, "
              f"{augmented_batch['expression'].max():.3f}]")
    
    # 7. Visualize augmentation effects
    print("\n6. Visualizing augmentation effects...")
    
    # Get full dataset for visualization
    full_expr = dataset.expression.numpy()
    
    # Apply different augmentations
    visualize_augmentation_effects(
        full_expr,
        batch_augmented['expression'].numpy(),
        batch_labels,
        title="Batch-Aware Mixup Effects"
    )
    
    print("\n   Visualization saved to 'batch_augmentation_effects.png'")
    
    # 8. Compare batch statistics
    print("\n7. Comparing batch statistics...")
    
    original_stats = {}
    augmented_stats = {}
    
    for batch_id in np.unique(batch_labels):
        batch_mask = batch_labels == batch_id
        
        original_stats[batch_id] = {
            'mean': full_expr[batch_mask].mean(),
            'std': full_expr[batch_mask].std()
        }
        
        augmented_stats[batch_id] = {
            'mean': batch_augmented['expression'][batch_mask].mean().item(),
            'std': batch_augmented['expression'][batch_mask].std().item()
        }
    
    print("\n   Original batch statistics:")
    for batch_id, stats in original_stats.items():
        print(f"     Batch {batch_id}: mean={stats['mean']:.3f}, std={stats['std']:.3f}")
    
    print("\n   Augmented batch statistics:")
    for batch_id, stats in augmented_stats.items():
        print(f"     Batch {batch_id}: mean={stats['mean']:.3f}, std={stats['std']:.3f}")
    
    print("\n✅ Example completed successfully!")


if __name__ == "__main__":
    main()