"""
Basic usage example for ScINTEG v3.

This script demonstrates how to:
1. Load and prepare data
2. Create a ScINTEG v3 model
3. Run forward pass
4. Compute loss
5. Extract useful outputs
"""

import torch
import numpy as np
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.models.scinteg import ScINTEGv3


def create_example_data():
    """Create example single-cell data."""
    # Parameters
    n_cells = 100
    n_genes = 500
    n_pathways = 50
    batch_size = 32
    
    # Create synthetic expression matrix
    np.random.seed(42)
    expression = np.random.lognormal(0, 1, (n_cells, n_genes))
    expression = torch.FloatTensor(expression)
    
    # Create pathway-gene mask
    # Each pathway is associated with ~20 genes
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = 20
    
    for i in range(n_pathways):
        # Randomly select genes for this pathway
        gene_indices = np.random.choice(n_genes, genes_per_pathway, replace=False)
        pathway_mask[i, gene_indices] = 1
    
    # Create cell similarity graph (k-NN)
    cell_similarity = torch.mm(expression, expression.t())
    k = 5
    _, indices = torch.topk(cell_similarity, k+1, dim=1)
    
    cell_edges = []
    for i in range(n_cells):
        for j in range(1, k+1):
            neighbor = indices[i, j].item()
            cell_edges.append([i, neighbor])
    
    cell_graph = torch.tensor(cell_edges).t()
    print(f"Created cell graph with {cell_graph.shape[1]} edges")
    
    # Create gene co-expression graph
    gene_corr = torch.corrcoef(expression.t())
    threshold = 0.5
    gene_edges = torch.nonzero(gene_corr > threshold, as_tuple=False)
    # Remove self-loops
    gene_graph = gene_edges[gene_edges[:, 0] != gene_edges[:, 1]].t()
    
    # Sample a batch
    batch_indices = torch.randperm(n_cells)[:batch_size]
    batch_expression = expression[batch_indices]
    
    # Adjust cell graph for batch
    batch_cell_edges = []
    for i, j in cell_edges:
        # Map to batch indices
        if i in batch_indices and j in batch_indices:
            new_i = (batch_indices == i).nonzero()[0].item()
            new_j = (batch_indices == j).nonzero()[0].item()
            batch_cell_edges.append([new_i, new_j])
    
    batch_cell_graph = torch.tensor(batch_cell_edges).t() if batch_cell_edges else torch.zeros(2, 0, dtype=torch.long)
    
    return {
        'expression': batch_expression,
        'cell_graph': batch_cell_graph,
        'gene_graph': gene_graph,
        'pathway_mask': pathway_mask,
        'n_cells': n_cells,
        'n_genes': n_genes,
        'n_pathways': n_pathways
    }


def main():
    """Main example function."""
    print("ScINTEG v3 Basic Usage Example")
    print("=" * 50)
    
    # 1. Create example data
    print("\n1. Creating example data...")
    data = create_example_data()
    print(f"   - Expression matrix: {data['expression'].shape}")
    print(f"   - Cell graph edges: {data['cell_graph'].shape[1]}")
    print(f"   - Gene graph edges: {data['gene_graph'].shape[1]}")
    print(f"   - Pathway mask: {data['pathway_mask'].shape}")
    
    # 2. Create model
    print("\n2. Creating ScINTEG v3 model...")
    model = ScINTEGv3(
        n_cells=data['n_cells'],
        n_genes=data['n_genes'],
        n_pathways=data['n_pathways'],
        pathway_gene_mask=data['pathway_mask'],
        # Model architecture
        cell_embedding_dim=64,
        gene_embedding_dim=32,
        cell_hidden_dim=128,
        gene_hidden_dim=64,
        # Options
        use_hierarchical_pathways=False,
        reconstruction_loss_type='mse',
        dropout=0.1,
        # GRN predictor config
        grn_predictor_config={
            'mode': 'embedding_similarity',
            'top_k_edges_per_gene': 10,
            'use_mlp': False
        }
    )
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"   - Total parameters: {total_params:,}")
    print(f"   - Trainable parameters: {trainable_params:,}")
    
    # 3. Forward pass
    print("\n3. Running forward pass...")
    model.eval()  # Set to evaluation mode
    
    with torch.no_grad():
        outputs = model(
            data['expression'],
            data['cell_graph'],
            data['gene_graph'],
            return_intermediates=True
        )
    
    print(f"   - Reconstruction shape: {outputs['reconstruction'].shape}")
    print(f"   - Cell embeddings shape: {outputs['cell_embeddings'].shape}")
    print(f"   - Gene embeddings shape: {outputs['gene_embeddings'].shape}")
    print(f"   - Pathway features shape: {outputs['pathway_features'].shape}")
    print(f"   - GRN edges: {outputs['grn_edge_index'].shape[1]}")
    
    # 4. Compute loss
    print("\n4. Computing loss...")
    loss = model.compute_loss(outputs, data['expression'])
    print(f"   - Total loss: {loss.item():.4f}")
    
    # Get loss components
    _, loss_components = model.compute_loss(
        outputs, 
        data['expression'], 
        return_components=True
    )
    
    print("   - Loss components:")
    for name, value in loss_components.items():
        print(f"     - {name}: {value.item():.4f}")
    
    # 5. Extract useful information
    print("\n5. Extracting useful outputs...")
    
    # Reconstruction quality
    mse = torch.nn.functional.mse_loss(outputs['reconstruction'], data['expression'])
    print(f"   - Reconstruction MSE: {mse.item():.4f}")
    
    # Top pathway activities
    pathway_activities = outputs['pathway_features'].mean(dim=0)
    top_pathways = torch.topk(pathway_activities, 5)
    print("   - Top 5 active pathways:")
    for value, idx in zip(top_pathways.values, top_pathways.indices):
        print(f"     - Pathway {idx.item()}: {value.item():.4f}")
    
    # GRN statistics
    if outputs['grn_edge_weight'].numel() > 0:
        mean_weight = outputs['grn_edge_weight'].mean().item()
        max_weight = outputs['grn_edge_weight'].max().item()
        print(f"   - GRN mean edge weight: {mean_weight:.4f}")
        print(f"   - GRN max edge weight: {max_weight:.4f}")
    
    # 6. Training example (optional)
    print("\n6. Training example (1 step)...")
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    # Forward pass
    outputs = model(
        data['expression'],
        data['cell_graph'],
        data['gene_graph'],
        return_intermediates=True
    )
    
    # Compute loss
    loss = model.compute_loss(outputs, data['expression'])
    print(f"   - Loss before update: {loss.item():.4f}")
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    
    # Check loss after update
    with torch.no_grad():
        outputs = model(
            data['expression'],
            data['cell_graph'],
            data['gene_graph'],
            return_intermediates=True
        )
        loss_after = model.compute_loss(outputs, data['expression'])
    
    print(f"   - Loss after update: {loss_after.item():.4f}")
    print(f"   - Loss change: {(loss_after - loss).item():.4f}")
    
    print("\n✅ Example completed successfully!")


if __name__ == "__main__":
    main()