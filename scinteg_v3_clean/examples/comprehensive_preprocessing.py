"""
Comprehensive preprocessing pipeline example.

This example demonstrates:
1. Data validation and quality control
2. Complete preprocessing pipeline
3. Integration with ScINTEG dataset
4. Visualization of preprocessing effects
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.data import (
    ScINTEGDataset,
    DataValidator,
    ValidationReport,
    PreprocessingPipeline,
    PreprocessingConfig,
    validate_dataset,
    preprocess_data
)


def create_realistic_dataset(n_cells=2000, n_genes=5000, n_batches=3):
    """Create a realistic synthetic dataset with various quality issues."""
    np.random.seed(42)
    
    all_expression = []
    all_batch_labels = []
    all_cell_types = []
    
    # Cell types with different expression patterns
    cell_type_patterns = {
        0: (1.0, 1.0),    # Type A: moderate expression
        1: (1.5, 0.8),    # Type B: higher expression, lower variance  
        2: (0.8, 1.2),    # Type C: lower expression, higher variance
    }
    
    for batch_id in range(n_batches):
        batch_size = n_cells // n_batches
        
        # Batch effect parameters
        batch_scale = 1.0 + (batch_id - 1) * 0.3  # Batch effect
        batch_shift = np.random.randn(n_genes) * 0.2
        
        batch_expr = []
        batch_types = []
        
        for cell_type in range(3):
            n_cells_type = batch_size // 3
            
            # Cell type specific parameters
            type_mean, type_std = cell_type_patterns[cell_type]
            
            # Generate base expression
            base_expr = np.random.lognormal(
                mean=np.log(type_mean), 
                sigma=type_std, 
                size=(n_cells_type, n_genes)
            )
            
            # Apply batch effects
            cell_expr = base_expr * batch_scale + batch_shift
            cell_expr = np.maximum(cell_expr, 0)  # Ensure non-negative
            
            batch_expr.append(cell_expr)
            batch_types.extend([cell_type] * n_cells_type)
        
        batch_expression = np.vstack(batch_expr)
        all_expression.append(batch_expression)
        all_batch_labels.extend([batch_id] * batch_size)
        all_cell_types.extend(batch_types)
    
    expression = np.vstack(all_expression)
    
    # Add some quality issues
    # 1. Add some very lowly expressed genes
    n_low_genes = n_genes // 10
    expression[:, :n_low_genes] = np.random.poisson(0.1, (expression.shape[0], n_low_genes))
    
    # 2. Add some low-quality cells
    n_low_cells = n_cells // 20
    low_quality_indices = np.random.choice(n_cells, n_low_cells, replace=False)
    expression[low_quality_indices] = np.random.poisson(0.5, (n_low_cells, n_genes))
    
    # 3. Add some extreme outlier cells
    n_outliers = n_cells // 50
    outlier_indices = np.random.choice(n_cells, n_outliers, replace=False)
    expression[outlier_indices] = np.random.lognormal(3, 1, (n_outliers, n_genes))
    
    # Create gene names with some mitochondrial genes
    gene_names = []
    for i in range(n_genes):
        if i < 50:  # First 50 are mitochondrial
            gene_names.append(f"MT-CO{i+1}")
        else:
            gene_names.append(f"Gene_{i}")
    
    cell_names = [f"Cell_{i}" for i in range(n_cells)]
    
    return expression, gene_names, cell_names, all_batch_labels, all_cell_types


def plot_preprocessing_comparison(original_data, processed_data, save_path="preprocessing_comparison.png"):
    """Plot comparison of original vs processed data."""
    fig, axes = plt.subplots(3, 3, figsize=(15, 12))
    
    orig_expr = original_data['expression'].numpy() if isinstance(original_data['expression'], torch.Tensor) else original_data['expression']
    proc_expr = processed_data['expression'].numpy()
    
    # 1. Cell UMI distribution
    ax = axes[0, 0]
    orig_umi = orig_expr.sum(axis=1)
    proc_umi = proc_expr.sum(axis=1)
    ax.hist(orig_umi, bins=50, alpha=0.5, label='Original', density=True)
    ax.hist(proc_umi, bins=50, alpha=0.5, label='Processed', density=True)
    ax.set_xlabel('UMI counts per cell')
    ax.set_ylabel('Density')
    ax.set_title('UMI Distribution')
    ax.legend()
    ax.set_yscale('log')
    
    # 2. Genes per cell distribution
    ax = axes[0, 1]
    orig_genes = (orig_expr > 0).sum(axis=1)
    proc_genes = (proc_expr > 0).sum(axis=1)
    ax.hist(orig_genes, bins=50, alpha=0.5, label='Original', density=True)
    ax.hist(proc_genes, bins=50, alpha=0.5, label='Processed', density=True)
    ax.set_xlabel('Genes per cell')
    ax.set_ylabel('Density')
    ax.set_title('Genes per Cell Distribution')
    ax.legend()
    
    # 3. Cells per gene distribution
    ax = axes[0, 2]
    orig_cells_per_gene = (orig_expr > 0).sum(axis=0)
    proc_cells_per_gene = (proc_expr > 0).sum(axis=0)
    ax.hist(orig_cells_per_gene, bins=50, alpha=0.5, label='Original', density=True)
    ax.hist(proc_cells_per_gene, bins=50, alpha=0.5, label='Processed', density=True)
    ax.set_xlabel('Cells per gene')
    ax.set_ylabel('Density')
    ax.set_title('Cells per Gene Distribution')
    ax.legend()
    ax.set_yscale('log')
    
    # 4. Expression distribution
    ax = axes[1, 0]
    orig_nonzero = orig_expr[orig_expr > 0].flatten()
    proc_nonzero = proc_expr[proc_expr > 0].flatten()
    ax.hist(orig_nonzero, bins=50, alpha=0.5, label='Original', density=True)
    ax.hist(proc_nonzero, bins=50, alpha=0.5, label='Processed', density=True)
    ax.set_xlabel('Expression level')
    ax.set_ylabel('Density')
    ax.set_title('Non-zero Expression Distribution')
    ax.legend()
    ax.set_yscale('log')
    
    # 5. Sparsity comparison
    ax = axes[1, 1]
    orig_sparsity = (orig_expr == 0).mean() * 100
    proc_sparsity = (proc_expr == 0).mean() * 100
    ax.bar(['Original', 'Processed'], [orig_sparsity, proc_sparsity])
    ax.set_ylabel('Sparsity (%)')
    ax.set_title('Data Sparsity')
    
    # 6. Shape comparison
    ax = axes[1, 2]
    ax.bar(['Original\nCells', 'Processed\nCells'], [orig_expr.shape[0], proc_expr.shape[0]], alpha=0.7)
    ax.set_ylabel('Number of cells')
    ax.set_title('Data Size Comparison')
    
    # 7. PCA visualization (if data not too large)
    if orig_expr.shape[0] <= 1000 and orig_expr.shape[1] <= 1000:
        from sklearn.decomposition import PCA
        
        # Original data PCA
        ax = axes[2, 0]
        pca_orig = PCA(n_components=2)
        orig_pca = pca_orig.fit_transform(orig_expr)
        batch_labels = original_data.get('batch_labels', [0] * orig_expr.shape[0])
        scatter = ax.scatter(orig_pca[:, 0], orig_pca[:, 1], c=batch_labels, alpha=0.6, s=1)
        ax.set_xlabel(f'PC1 ({pca_orig.explained_variance_ratio_[0]:.2%})')
        ax.set_ylabel(f'PC2 ({pca_orig.explained_variance_ratio_[1]:.2%})')
        ax.set_title('Original Data PCA')
        
        # Processed data PCA
        ax = axes[2, 1]
        pca_proc = PCA(n_components=2)
        proc_pca = pca_proc.fit_transform(proc_expr)
        proc_batch_labels = processed_data.get('batch_labels', [0] * proc_expr.shape[0])
        ax.scatter(proc_pca[:, 0], proc_pca[:, 1], c=proc_batch_labels, alpha=0.6, s=1)
        ax.set_xlabel(f'PC1 ({pca_proc.explained_variance_ratio_[0]:.2%})')
        ax.set_ylabel(f'PC2 ({pca_proc.explained_variance_ratio_[1]:.2%})')
        ax.set_title('Processed Data PCA')
    else:
        axes[2, 0].text(0.5, 0.5, 'Data too large\nfor PCA plot', ha='center', va='center')
        axes[2, 1].text(0.5, 0.5, 'Data too large\nfor PCA plot', ha='center', va='center')
    
    # 8. Processing summary
    ax = axes[2, 2]
    ax.axis('off')
    
    summary_text = f"""Processing Summary:
    
Original: {original_data['expression'].shape if isinstance(original_data['expression'], torch.Tensor) else orig_expr.shape}
Processed: {proc_expr.shape}

Cells removed: {orig_expr.shape[0] - proc_expr.shape[0]}
Genes removed: {orig_expr.shape[1] - proc_expr.shape[1]}

Sparsity reduction:
{orig_sparsity:.1f}% → {proc_sparsity:.1f}%
    """
    
    ax.text(0.1, 0.9, summary_text, fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()


def main():
    """Main preprocessing example."""
    print("Comprehensive Preprocessing Pipeline Example")
    print("=" * 50)
    
    # 1. Create realistic synthetic dataset
    print("\n1. Creating realistic synthetic dataset...")
    expression, gene_names, cell_names, batch_labels, cell_types = create_realistic_dataset(
        n_cells=2000, n_genes=5000, n_batches=3
    )
    
    print(f"   Dataset: {expression.shape[0]} cells, {expression.shape[1]} genes")
    print(f"   Batches: {len(set(batch_labels))}")
    print(f"   Cell types: {len(set(cell_types))}")
    print(f"   Sparsity: {(expression == 0).mean() * 100:.1f}%")
    
    # 2. Data validation
    print("\n2. Performing data validation...")
    
    validator = DataValidator(
        min_cells_per_gene=10,
        min_genes_per_cell=200,
        min_umi_per_cell=1000,
        max_mitochondrial_pct=15.0
    )
    
    validation_report = validator.validate(
        expression, gene_names, cell_names, batch_labels, cell_types
    )
    
    print(f"   Validation status: {'✓ PASSED' if validation_report.passed else '✗ FAILED'}")
    print(f"   Warnings: {len(validation_report.warnings)}")
    print(f"   Errors: {len(validation_report.errors)}")
    
    if validation_report.warnings:
        print("\n   Warnings:")
        for warning in validation_report.warnings[:3]:  # Show first 3
            print(f"     - {warning}")
    
    if validation_report.recommendations:
        print("\n   Recommendations:")
        for rec in validation_report.recommendations[:3]:  # Show first 3
            print(f"     - {rec}")
    
    # 3. Configure preprocessing pipeline
    print("\n3. Configuring preprocessing pipeline...")
    
    config = PreprocessingConfig(
        # Quality control
        validate_data=True,
        min_cells_per_gene=20,
        min_genes_per_cell=300,
        max_genes_per_cell=8000,
        min_umi_per_cell=1500,
        max_umi_per_cell=80000,
        max_mitochondrial_pct=15.0,
        
        # Filtering
        filter_cells=True,
        filter_genes=True,
        remove_outliers=True,
        outlier_threshold=2.5,
        
        # Feature selection
        highly_variable_genes=True,
        n_top_genes=3000,
        
        # Normalization
        normalize=True,
        normalization_method='library_size',
        log_transform=True,
        
        # Graph construction
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.2
    )
    
    print(f"   Configuration: {len([k for k, v in config.__dict__.items() if v is True])} features enabled")
    
    # 4. Apply preprocessing pipeline
    print("\n4. Applying preprocessing pipeline...")
    
    pipeline = PreprocessingPipeline(config)
    
    result = pipeline.fit_transform(
        expression=expression,
        gene_names=gene_names,
        cell_names=cell_names,
        batch_labels=batch_labels,
        cell_types=cell_types
    )
    
    print(f"   Original shape: {expression.shape}")
    print(f"   Final shape: {result['final_shape']}")
    print(f"   Cells filtered: {expression.shape[0] - result['final_shape'][0]} ({(1 - result['final_shape'][0]/expression.shape[0])*100:.1f}%)")
    print(f"   Genes filtered: {expression.shape[1] - result['final_shape'][1]} ({(1 - result['final_shape'][1]/expression.shape[1])*100:.1f}%)")
    
    # 5. Processing log summary
    print("\n5. Processing steps summary:")
    for i, log_entry in enumerate(result['processing_log'][:10]):  # Show first 10 steps
        print(f"   {i+1}. {log_entry}")
    if len(result['processing_log']) > 10:
        print(f"   ... and {len(result['processing_log']) - 10} more steps")
    
    # 6. Quality metrics comparison
    print("\n6. Quality metrics comparison:")
    
    orig_metrics = {
        'sparsity': (expression == 0).mean() * 100,
        'mean_umi': expression.sum(axis=1).mean(),
        'mean_genes': (expression > 0).sum(axis=1).mean()
    }
    
    proc_expr = result['expression'].numpy()
    proc_metrics = {
        'sparsity': (proc_expr == 0).mean() * 100,
        'mean_umi': proc_expr.sum(axis=1).mean(),
        'mean_genes': (proc_expr > 0).sum(axis=1).mean()
    }
    
    print(f"   Sparsity: {orig_metrics['sparsity']:.1f}% → {proc_metrics['sparsity']:.1f}%")
    print(f"   Mean UMI per cell: {orig_metrics['mean_umi']:.0f} → {proc_metrics['mean_umi']:.0f}")
    print(f"   Mean genes per cell: {orig_metrics['mean_genes']:.0f} → {proc_metrics['mean_genes']:.0f}")
    
    # 7. Create ScINTEG dataset
    print("\n7. Creating ScINTEG dataset from processed data...")
    
    dataset = ScINTEGDataset(
        expression_data=result['expression'],
        gene_names=result['gene_names'],
        cell_names=result['cell_names'],
        batch_labels=result['batch_labels'],
        cell_types=result['cell_types'],
        cell_graph=result['cell_graph'],
        gene_graph=result['gene_graph'],
        normalize=False,  # Already normalized
        build_cell_graph=False,  # Already built
        build_gene_graph=False   # Already built
    )
    
    print(f"   Dataset created: {dataset.n_cells} cells, {dataset.n_genes} genes")
    print(f"   Cell graph: {dataset.cell_graph.shape[1] if dataset.cell_graph is not None else 0} edges")
    print(f"   Gene graph: {dataset.gene_graph.shape[1] if dataset.gene_graph is not None else 0} edges")
    
    # 8. Visualize preprocessing effects
    print("\n8. Creating visualization...")
    
    original_data = {
        'expression': expression,
        'batch_labels': batch_labels
    }
    
    plot_preprocessing_comparison(original_data, result)
    print("   Preprocessing comparison plot saved to 'preprocessing_comparison.png'")
    
    # 9. Example of using convenience function
    print("\n9. Demonstrating convenience function...")
    
    # Quick preprocessing with convenience function
    quick_result = preprocess_data(
        expression=expression,
        gene_names=gene_names,
        batch_labels=batch_labels,
        normalize=True,
        filter_cells=True,
        filter_genes=True,
        build_cell_graph=False,
        build_gene_graph=False
    )
    
    print(f"   Quick preprocessing: {expression.shape} → {quick_result['final_shape']}")
    
    print("\n✅ Comprehensive preprocessing example completed successfully!")
    print("\nThe preprocessing pipeline provides:")
    print("  - Comprehensive data validation")
    print("  - Intelligent filtering and quality control")
    print("  - Flexible normalization options")
    print("  - Automated graph construction")
    print("  - Integration with ScINTEG workflows")


if __name__ == "__main__":
    main()