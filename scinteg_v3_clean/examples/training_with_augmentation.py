"""
Example of training ScINTEG with data augmentation.

This example demonstrates:
1. Setting up batch-aware data augmentation
2. Training with augmented data
3. Monitoring augmentation effects during training
"""

import torch
import numpy as np
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg import ScINTEGv3, ScINTEGConfig
from scinteg.data import (
    ScINTEGDataset,
    create_data_loaders,
    BatchAwareAugmenter,
    create_augmentation_pipeline
)
from scinteg.training import ScINTEGTrainer, TrainingConfig
import matplotlib.pyplot as plt


def create_synthetic_dataset(n_cells=1000, n_genes=2000, n_batches=3):
    """Create synthetic dataset with batch effects."""
    np.random.seed(42)
    
    # Generate expression data with batch effects
    all_expression = []
    all_batch_labels = []
    
    for batch_id in range(n_batches):
        # Base expression
        batch_expr = np.random.lognormal(0, 1, (n_cells // n_batches, n_genes))
        
        # Add batch effect
        batch_effect = 1.0 + (batch_id - n_batches/2) * 0.3
        batch_expr *= batch_effect
        
        all_expression.append(batch_expr)
        all_batch_labels.extend([batch_id] * (n_cells // n_batches))
    
    expression = np.vstack(all_expression)
    batch_labels = np.array(all_batch_labels)
    
    return expression, batch_labels


def plot_training_curves(trainer, save_path="training_curves_augmented.png"):
    """Plot training curves."""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Loss curves
    ax = axes[0, 0]
    ax.plot(trainer.training_history['train_loss'], label='Train Loss')
    ax.plot(trainer.training_history['val_loss'], label='Val Loss')
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('Training and Validation Loss')
    ax.legend()
    ax.grid(True)
    
    # Reconstruction loss
    ax = axes[0, 1]
    if 'reconstruction_loss' in trainer.training_history:
        ax.plot(trainer.training_history['reconstruction_loss'])
        ax.set_xlabel('Step')
        ax.set_ylabel('Reconstruction Loss')
        ax.set_title('Reconstruction Loss Over Time')
        ax.grid(True)
    
    # Learning rate
    ax = axes[1, 0]
    ax.plot(trainer.training_history['learning_rate'])
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Learning Rate')
    ax.set_title('Learning Rate Schedule')
    ax.set_yscale('log')
    ax.grid(True)
    
    # Best metrics
    ax = axes[1, 1]
    ax.text(0.1, 0.8, f"Best Val Loss: {trainer.best_val_loss:.4f}", fontsize=12)
    ax.text(0.1, 0.6, f"Best Epoch: {trainer.best_epoch}", fontsize=12)
    ax.text(0.1, 0.4, f"Total Epochs: {len(trainer.training_history['train_loss'])}", fontsize=12)
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    ax.set_title('Training Summary')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150)
    plt.close()


def main():
    """Main training example with augmentation."""
    print("ScINTEG v3 Training with Data Augmentation")
    print("=" * 50)
    
    # 1. Create synthetic dataset
    print("\n1. Creating synthetic dataset with batch effects...")
    expression, batch_labels = create_synthetic_dataset(
        n_cells=1000,
        n_genes=2000,
        n_batches=3
    )
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        batch_labels=batch_labels,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.3
    )
    
    print(f"   Dataset: {dataset.n_cells} cells, {dataset.n_genes} genes")
    print(f"   Batches: {np.unique(batch_labels)}")
    
    # 2. Create data loaders with balanced sampling
    print("\n2. Creating data loaders with balanced batch sampling...")
    data_loaders = create_data_loaders(
        dataset,
        train_ratio=0.7,
        val_ratio=0.15,
        test_ratio=0.15,
        batch_size=32,
        balanced_sampling=True,
        balance_by='batch'
    )
    
    # 3. Configure augmentation
    print("\n3. Configuring batch-aware augmentation...")
    augmentation_config = {
        'cross_batch_mixup': True,
        'batch_specific_noise': True,
        'mixup_alpha': 0.2,
        'noise_level': 0.1,
        'dropout_rate': 0.1,
        'augment_prob': 0.8
    }
    
    augmenter = create_augmentation_pipeline(
        augmentation_config,
        batch_aware=True
    )
    
    # 4. Initialize model
    print("\n4. Initializing ScINTEG model...")
    config = ScINTEGConfig(
        n_genes=dataset.n_genes,
        n_pathways=100,
        # Architecture
        hidden_dims=[512, 256],
        latent_dim=64,
        # Regularization
        dropout_rate=0.1,
        l1_lambda=0.001,
        l2_lambda=0.0001
    )
    
    model = ScINTEGv3(config)
    print(f"   Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 5. Setup training
    print("\n5. Setting up training configuration...")
    
    # Optimizer
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=1e-3,
        weight_decay=1e-4
    )
    
    # Learning rate scheduler
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=50,
        eta_min=1e-5
    )
    
    # Training configuration
    training_config = TrainingConfig(
        max_epochs=50,
        early_stopping_patience=10,
        checkpoint_dir="checkpoints_augmented",
        log_interval=10
    )
    
    # Initialize trainer with augmentation
    trainer = ScINTEGTrainer(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        augmenter=augmenter,
        augment_train=True,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        **training_config.__dict__
    )
    
    print(f"   Device: {trainer.device}")
    print(f"   Augmentation: Enabled")
    print(f"   Max epochs: {training_config.max_epochs}")
    
    # 6. Train model
    print("\n6. Training model with augmentation...")
    
    # Training callbacks
    def on_epoch_end(epoch, logs):
        """Custom callback for epoch end."""
        print(f"\n   Epoch {epoch + 1} Summary:")
        print(f"     Train Loss: {logs['train_loss']:.4f}")
        print(f"     Val Loss: {logs['val_loss']:.4f}")
        if 'val_reconstruction_corr' in logs:
            print(f"     Val Reconstruction Corr: {logs['val_reconstruction_corr']:.4f}")
    
    # Train
    trainer.fit(
        train_loader=data_loaders['train'],
        val_loader=data_loaders['val'],
        callbacks={'on_epoch_end': on_epoch_end}
    )
    
    # 7. Evaluate on test set
    print("\n7. Evaluating on test set...")
    test_metrics = trainer.evaluate(data_loaders['test'])
    
    print("\n   Test Metrics:")
    for key, value in test_metrics.items():
        if isinstance(value, (int, float)):
            print(f"     {key}: {value:.4f}")
    
    # 8. Compare with non-augmented training
    print("\n8. Training without augmentation for comparison...")
    
    # Reset model
    model_no_aug = ScINTEGv3(config)
    optimizer_no_aug = torch.optim.AdamW(
        model_no_aug.parameters(),
        lr=1e-3,
        weight_decay=1e-4
    )
    
    trainer_no_aug = ScINTEGTrainer(
        model=model_no_aug,
        optimizer=optimizer_no_aug,
        augmenter=None,  # No augmentation
        augment_train=False,
        device=trainer.device,
        **training_config.__dict__
    )
    
    # Train without augmentation
    trainer_no_aug.fit(
        train_loader=data_loaders['train'],
        val_loader=data_loaders['val']
    )
    
    # Compare results
    print("\n9. Comparison Results:")
    print(f"   With Augmentation:")
    print(f"     Best Val Loss: {trainer.best_val_loss:.4f}")
    print(f"     Final Train Loss: {trainer.training_history['train_loss'][-1]:.4f}")
    
    print(f"\n   Without Augmentation:")
    print(f"     Best Val Loss: {trainer_no_aug.best_val_loss:.4f}")
    print(f"     Final Train Loss: {trainer_no_aug.training_history['train_loss'][-1]:.4f}")
    
    # 10. Visualize results
    print("\n10. Visualizing training curves...")
    plot_training_curves(trainer, "training_with_augmentation.png")
    plot_training_curves(trainer_no_aug, "training_without_augmentation.png")
    
    print("\n   Training curves saved!")
    print("\n✅ Training example completed successfully!")


if __name__ == "__main__":
    main()