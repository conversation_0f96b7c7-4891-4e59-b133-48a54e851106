"""
Example script demonstrating data preprocessing and validation in ScINTEG v3.

This script shows how to:
1. Load raw single-cell RNA-seq data
2. Perform data validation and quality control
3. Apply comprehensive preprocessing pipeline
4. Visualize preprocessing results
"""

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

from scinteg.data import (
    validate_dataset,
    preprocess_data,
    PreprocessingConfig,
    PreprocessingPipeline
)


def generate_example_data(n_cells=1000, n_genes=2000, noise_level=0.1):
    """Generate synthetic single-cell RNA-seq data for demonstration."""
    print("Generating synthetic scRNA-seq data...")
    
    # Create realistic expression patterns
    np.random.seed(42)
    
    # Base expression levels
    base_expression = np.random.lognormal(0, 1, (n_cells, n_genes))
    
    # Add cell type structure (3 cell types)
    cell_type_labels = np.random.choice(['Type_A', 'Type_B', 'Type_C'], n_cells)
    
    # Cell type specific genes
    for i, cell_type in enumerate(['Type_A', 'Type_B', 'Type_C']):
        cell_mask = cell_type_labels == cell_type
        gene_start = i * (n_genes // 6)
        gene_end = gene_start + (n_genes // 6)
        
        # Increase expression for type-specific genes
        base_expression[cell_mask, gene_start:gene_end] *= 3
    
    # Add batch effects (2 batches)
    batch_labels = np.random.choice([0, 1], n_cells)
    batch_1_mask = batch_labels == 1
    base_expression[batch_1_mask] *= 1.5  # Batch 1 has higher overall expression
    
    # Add some low-quality cells
    low_quality_mask = np.random.choice([True, False], n_cells, p=[0.1, 0.9])
    base_expression[low_quality_mask] *= 0.1  # Very low expression
    
    # Add some lowly expressed genes
    lowly_expressed_genes = np.random.choice([True, False], n_genes, p=[0.2, 0.8])
    base_expression[:, lowly_expressed_genes] *= 0.01
    
    # Add noise
    noise = np.random.lognormal(0, noise_level, (n_cells, n_genes))
    expression = base_expression * noise
    
    # Ensure non-negative values
    expression = np.maximum(expression, 0)
    
    # Create metadata
    gene_names = [f"Gene_{i:04d}" for i in range(n_genes)]
    cell_names = [f"Cell_{i:04d}" for i in range(n_cells)]
    
    # Add some mitochondrial genes
    mito_genes = np.random.choice(range(n_genes), size=50, replace=False)
    for i in mito_genes:
        gene_names[i] = f"MT-Gene_{i:04d}"
    
    print(f"Generated data: {n_cells} cells × {n_genes} genes")
    print(f"Cell types: {np.unique(cell_type_labels, return_counts=True)}")
    print(f"Batches: {np.unique(batch_labels, return_counts=True)}")
    
    return expression, gene_names, cell_names, batch_labels, cell_type_labels


def demonstrate_validation(expression, gene_names, cell_names, batch_labels, cell_types):
    """Demonstrate data validation functionality."""
    print("\n" + "="*50)
    print("DATA VALIDATION DEMONSTRATION")
    print("="*50)
    
    # Perform validation
    print("Running comprehensive data validation...")
    validation_report = validate_dataset(
        expression=expression,
        gene_names=gene_names,
        cell_names=cell_names,
        batch_labels=batch_labels,
        cell_types=cell_types,
        create_plots=False  # Skip plots for this example
    )
    
    # Display results
    print(f"\nValidation Status: {'✓ PASSED' if validation_report.passed else '✗ FAILED'}")
    print(f"Warnings: {len(validation_report.warnings)}")
    print(f"Errors: {len(validation_report.errors)}")
    
    if validation_report.warnings:
        print("\nWarnings:")
        for warning in validation_report.warnings:
            print(f"  - {warning}")
    
    if validation_report.errors:
        print("\nErrors:")
        for error in validation_report.errors:
            print(f"  - {error}")
    
    if validation_report.recommendations:
        print("\nRecommendations:")
        for rec in validation_report.recommendations:
            print(f"  - {rec}")
    
    # Key metrics
    print(f"\nKey Metrics:")
    print(f"  Cells: {validation_report.metrics['n_cells']:,}")
    print(f"  Genes: {validation_report.metrics['n_genes']:,}")
    print(f"  Zero expression: {validation_report.metrics['zero_expression_pct']:.1f}%")
    print(f"  Mean UMI/cell: {validation_report.metrics['mean_umi_per_cell']:.1f}")
    print(f"  Mean genes/cell: {validation_report.metrics['mean_genes_per_cell']:.1f}")
    
    if 'batch_effect_strength' in validation_report.metrics:
        print(f"  Batch effect strength: {validation_report.metrics['batch_effect_strength']:.3f}")
    
    if 'outlier_cells_pct' in validation_report.metrics:
        print(f"  Outlier cells: {validation_report.metrics['outlier_cells_pct']:.1f}%")
    
    return validation_report


def demonstrate_preprocessing(expression, gene_names, cell_names, batch_labels, cell_types):
    """Demonstrate preprocessing pipeline functionality."""
    print("\n" + "="*50)
    print("PREPROCESSING PIPELINE DEMONSTRATION")
    print("="*50)
    
    # Create preprocessing configuration
    config = PreprocessingConfig(
        # Quality control
        validate_data=True,
        min_cells_per_gene=10,
        min_genes_per_cell=200,
        max_genes_per_cell=8000,
        min_umi_per_cell=500,
        max_umi_per_cell=50000,
        max_mitochondrial_pct=25.0,
        
        # Filtering
        filter_cells=True,
        filter_genes=True,
        remove_outliers=True,
        outlier_threshold=3.0,
        
        # Normalization
        normalize=True,
        normalization_method='library_size',
        log_transform=True,
        
        # Graph construction
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.3,
        
        # Advanced options
        highly_variable_genes=True,
        n_top_genes=1000
    )
    
    print("Preprocessing configuration:")
    print(f"  Filter genes: {config.filter_genes} (min {config.min_cells_per_gene} cells)")
    print(f"  Filter cells: {config.filter_cells} (UMI: {config.min_umi_per_cell}-{config.max_umi_per_cell})")
    print(f"  Remove outliers: {config.remove_outliers} (threshold: {config.outlier_threshold})")
    print(f"  Normalization: {config.normalize} ({config.normalization_method})")
    print(f"  HVG selection: {config.highly_variable_genes} (top {config.n_top_genes})")
    print(f"  Build graphs: cell={config.build_cell_graph}, gene={config.build_gene_graph}")
    
    # Apply preprocessing
    print(f"\nApplying preprocessing pipeline...")
    result = preprocess_data(
        expression=expression,
        gene_names=gene_names,
        cell_names=cell_names,
        batch_labels=batch_labels,
        cell_types=cell_types,
        config=config
    )
    
    # Display results
    print(f"\nPreprocessing completed!")
    print(f"Original shape: {result['original_shape']}")
    print(f"Final shape: {result['final_shape']}")
    print(f"Cells filtered: {result['original_shape'][0] - result['final_shape'][0]}")
    print(f"Genes filtered: {result['original_shape'][1] - result['final_shape'][1]}")
    
    # Processing log
    print(f"\nProcessing steps:")
    for step in result['processing_log']:
        print(f"  {step}")
    
    # Graph information
    if result['cell_graph'] is not None:
        print(f"\nCell graph: {result['cell_graph'].shape[1]} edges")
    if result['gene_graph'] is not None:
        print(f"Gene graph: {result['gene_graph'].shape[1]} edges")
    
    return result


def demonstrate_custom_pipeline(expression, gene_names, cell_names, batch_labels, cell_types):
    """Demonstrate custom preprocessing pipeline."""
    print("\n" + "="*50)
    print("CUSTOM PREPROCESSING PIPELINE")
    print("="*50)
    
    # Create custom configuration for specific use case
    custom_config = PreprocessingConfig(
        # Strict quality control
        validate_data=True,
        min_cells_per_gene=20,  # More stringent
        min_genes_per_cell=500,
        max_genes_per_cell=6000,
        min_umi_per_cell=1000,
        max_umi_per_cell=30000,
        max_mitochondrial_pct=15.0,  # Stricter
        
        # Aggressive filtering
        filter_cells=True,
        filter_genes=True,
        remove_outliers=True,
        outlier_threshold=2.5,  # More sensitive
        
        # Standard normalization
        normalize=True,
        normalization_method='library_size',
        log_transform=True,
        
        # Feature scaling
        scale_features=True,
        scaling_method='standard',
        
        # No HVG selection
        highly_variable_genes=False,
        
        # Build only cell graph
        build_cell_graph=True,
        build_gene_graph=False,
        cell_graph_k=10
    )
    
    # Use the pipeline class directly for more control
    pipeline = PreprocessingPipeline(custom_config)
    
    print("Custom pipeline configuration:")
    print("  - Strict quality thresholds")
    print("  - Standard scaling enabled")
    print("  - No HVG selection")
    print("  - Cell graph only")
    
    result = pipeline.fit_transform(
        expression=expression,
        gene_names=gene_names,
        cell_names=cell_names,
        batch_labels=batch_labels,
        cell_types=cell_types
    )
    
    print(f"\nCustom preprocessing results:")
    print(f"Shape: {result['original_shape']} → {result['final_shape']}")
    print(f"Data reduction: {(1 - np.prod(result['final_shape']) / np.prod(result['original_shape'])) * 100:.1f}%")
    
    # Analyze expression distribution after preprocessing
    expr_tensor = result['expression']
    print(f"\nExpression statistics after preprocessing:")
    print(f"  Mean: {expr_tensor.mean():.3f}")
    print(f"  Std: {expr_tensor.std():.3f}")
    print(f"  Min: {expr_tensor.min():.3f}")
    print(f"  Max: {expr_tensor.max():.3f}")
    
    return result


def visualize_preprocessing_results(original_data, preprocessed_result):
    """Create visualization of preprocessing results."""
    print("\n" + "="*50)
    print("PREPROCESSING VISUALIZATION")
    print("="*50)
    
    original_expr = original_data[0]
    processed_expr = preprocessed_result['expression'].numpy()
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 1. UMI distribution comparison
    orig_umi = original_expr.sum(axis=1)
    proc_umi = np.exp(processed_expr).sum(axis=1) if preprocessed_result.get('log_transformed', True) else processed_expr.sum(axis=1)
    
    axes[0, 0].hist(orig_umi, bins=50, alpha=0.7, label='Original', density=True)
    axes[0, 0].hist(proc_umi, bins=50, alpha=0.7, label='Processed', density=True)
    axes[0, 0].set_xlabel('UMI counts per cell')
    axes[0, 0].set_ylabel('Density')
    axes[0, 0].set_title('UMI Distribution')
    axes[0, 0].legend()
    axes[0, 0].set_yscale('log')
    
    # 2. Gene expression distribution
    orig_nonzero = original_expr[original_expr > 0]
    proc_nonzero = processed_expr[processed_expr > 0]
    
    axes[0, 1].hist(orig_nonzero.flatten(), bins=50, alpha=0.7, label='Original', density=True)
    axes[0, 1].hist(proc_nonzero.flatten(), bins=50, alpha=0.7, label='Processed', density=True)
    axes[0, 1].set_xlabel('Expression level')
    axes[0, 1].set_ylabel('Density')
    axes[0, 1].set_title('Expression Distribution')
    axes[0, 1].legend()
    axes[0, 1].set_yscale('log')
    
    # 3. Sparsity comparison
    orig_sparsity = (original_expr == 0).mean() * 100
    proc_sparsity = (processed_expr == 0).mean() * 100
    
    sparsity_data = [orig_sparsity, proc_sparsity]
    axes[0, 2].bar(['Original', 'Processed'], sparsity_data, alpha=0.7)
    axes[0, 2].set_ylabel('Sparsity (%)')
    axes[0, 2].set_title('Data Sparsity')
    
    # 4. Genes per cell
    orig_genes_per_cell = (original_expr > 0).sum(axis=1)
    proc_genes_per_cell = (processed_expr > 0).sum(axis=1)
    
    axes[1, 0].hist(orig_genes_per_cell, bins=50, alpha=0.7, label='Original', density=True)
    axes[1, 0].hist(proc_genes_per_cell, bins=50, alpha=0.7, label='Processed', density=True)
    axes[1, 0].set_xlabel('Genes per cell')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].set_title('Genes per Cell')
    axes[1, 0].legend()
    
    # 5. Cells per gene
    orig_cells_per_gene = (original_expr > 0).sum(axis=0)
    proc_cells_per_gene = (processed_expr > 0).sum(axis=0)
    
    axes[1, 1].hist(orig_cells_per_gene, bins=50, alpha=0.7, label='Original', density=True)
    axes[1, 1].hist(proc_cells_per_gene, bins=50, alpha=0.7, label='Processed', density=True)
    axes[1, 1].set_xlabel('Cells per gene')
    axes[1, 1].set_ylabel('Density')
    axes[1, 1].set_title('Cells per Gene')
    axes[1, 1].legend()
    
    # 6. Summary statistics
    axes[1, 2].axis('off')
    summary_text = f"""Preprocessing Summary:

Original: {original_expr.shape[0]:,} cells × {original_expr.shape[1]:,} genes
Processed: {processed_expr.shape[0]:,} cells × {processed_expr.shape[1]:,} genes

Cell retention: {processed_expr.shape[0]/original_expr.shape[0]*100:.1f}%
Gene retention: {processed_expr.shape[1]/original_expr.shape[1]*100:.1f}%

Original sparsity: {orig_sparsity:.1f}%
Processed sparsity: {proc_sparsity:.1f}%

Graphs constructed:
  Cell graph: {'✓' if preprocessed_result['cell_graph'] is not None else '✗'}
  Gene graph: {'✓' if preprocessed_result['gene_graph'] is not None else '✗'}
    """
    axes[1, 2].text(0.1, 0.9, summary_text, fontsize=10, verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('preprocessing_results.png', dpi=150, bbox_inches='tight')
    print("Visualization saved as 'preprocessing_results.png'")
    plt.show()


def main():
    """Main demonstration function."""
    print("ScINTEG v3 - Data Preprocessing and Validation Example")
    print("="*60)
    
    # Generate example data
    expression, gene_names, cell_names, batch_labels, cell_types = generate_example_data(
        n_cells=1000, n_genes=2000
    )
    
    # 1. Demonstrate validation
    validation_report = demonstrate_validation(
        expression, gene_names, cell_names, batch_labels, cell_types
    )
    
    # 2. Demonstrate standard preprocessing
    standard_result = demonstrate_preprocessing(
        expression, gene_names, cell_names, batch_labels, cell_types
    )
    
    # 3. Demonstrate custom preprocessing
    custom_result = demonstrate_custom_pipeline(
        expression, gene_names, cell_names, batch_labels, cell_types
    )
    
    # 4. Visualize results
    visualize_preprocessing_results(
        (expression, gene_names, cell_names, batch_labels, cell_types),
        standard_result
    )
    
    print("\n" + "="*60)
    print("EXAMPLE COMPLETED SUCCESSFULLY")
    print("="*60)
    print("\nNext steps:")
    print("- Use preprocessed data for model training")
    print("- Apply additional domain-specific filtering")
    print("- Integrate with ScINTEG training pipeline")
    print("- Customize preprocessing for your specific dataset")


if __name__ == "__main__":
    main()