"""
Example training script for ScINTEG v3.

This script demonstrates how to:
1. Load and prepare data
2. Configure the model and training
3. Train the model with checkpointing
4. Evaluate results
"""

import torch
import numpy as np
from pathlib import Path
import argparse
import logging
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.models import ScINTEGv3
from scinteg.data import ScINTEGDataset, create_data_loaders
from scinteg.training import (
    ScINTEGTrainer,
    CheckpointManager,
    AutoResumeTrainer,
    ExperimentConfig,
    create_default_config
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_synthetic_data(n_cells=1000, n_genes=2000, n_pathways=50):
    """Create synthetic data for demonstration."""
    logger.info("Creating synthetic dataset...")
    
    # Generate expression matrix
    np.random.seed(42)
    expression = np.random.lognormal(0, 1, (n_cells, n_genes))
    expression = torch.FloatTensor(expression)
    
    # Create pathway mask
    pathway_mask = torch.zeros(n_pathways, n_genes)
    genes_per_pathway = 40
    
    for i in range(n_pathways):
        gene_indices = np.random.choice(n_genes, genes_per_pathway, replace=False)
        pathway_mask[i, gene_indices] = 1
    
    # Create batch labels (simulate 3 batches)
    batch_labels = np.random.choice(3, n_cells)
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        pathway_mask=pathway_mask,
        batch_labels=batch_labels,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        build_gene_graph=True,
        cell_graph_k=15,
        gene_graph_threshold=0.5
    )
    
    return dataset


def create_model(config: ExperimentConfig, dataset: ScINTEGDataset):
    """Create model from configuration."""
    logger.info("Creating model...")
    
    model = ScINTEGv3(
        n_cells=dataset.n_cells,
        n_genes=dataset.n_genes,
        n_pathways=dataset.n_pathways,
        pathway_gene_mask=dataset.pathway_mask,
        # Dimensions from config
        n_meta_pathways=config.model.n_meta_pathways,
        cell_embedding_dim=config.model.cell_embedding_dim,
        gene_embedding_dim=config.model.gene_embedding_dim,
        cell_hidden_dim=config.model.cell_hidden_dim,
        gene_hidden_dim=config.model.gene_hidden_dim,
        # Architecture choices
        use_hierarchical_pathways=config.model.use_hierarchical_pathways,
        use_unet_decoder=config.model.use_unet_decoder,
        # Component configs
        cell_encoder_config=config.model.cell_encoder_config,
        gene_encoder_config=config.model.gene_encoder_config,
        projector_config=config.model.projector_config,
        decoder_config=config.model.decoder_config,
        grn_predictor_config=config.model.grn_predictor_config,
        # Loss configuration
        reconstruction_loss_type=config.model.reconstruction_loss_type,
        dropout=config.model.dropout,
        # Loss weights
        loss_config={
            'reconstruction_weight': config.model.reconstruction_weight,
            'grn_weight': config.model.grn_weight,
            'pathway_sparsity_weight': config.model.pathway_sparsity_weight,
            'use_adaptive_weighting': config.model.use_adaptive_weighting
        }
    )
    
    return model


def create_optimizer(model, config: ExperimentConfig):
    """Create optimizer from configuration."""
    opt_config = config.training.optimizer
    
    if opt_config.name == 'adam':
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=opt_config.lr,
            betas=opt_config.betas,
            eps=opt_config.eps,
            weight_decay=opt_config.weight_decay
        )
    elif opt_config.name == 'adamw':
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=opt_config.lr,
            betas=opt_config.betas,
            eps=opt_config.eps,
            weight_decay=opt_config.weight_decay
        )
    elif opt_config.name == 'sgd':
        optimizer = torch.optim.SGD(
            model.parameters(),
            lr=opt_config.lr,
            momentum=opt_config.momentum,
            weight_decay=opt_config.weight_decay,
            nesterov=opt_config.nesterov
        )
    else:
        raise ValueError(f"Unknown optimizer: {opt_config.name}")
    
    return optimizer


def create_scheduler(optimizer, config: ExperimentConfig):
    """Create learning rate scheduler from configuration."""
    sched_config = config.training.scheduler
    
    if sched_config is None or sched_config.name is None:
        return None
    
    if sched_config.name == 'cosine':
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=sched_config.T_max or config.training.epochs,
            eta_min=sched_config.eta_min
        )
    elif sched_config.name == 'step':
        scheduler = torch.optim.lr_scheduler.StepLR(
            optimizer,
            step_size=sched_config.step_size,
            gamma=sched_config.gamma
        )
    elif sched_config.name == 'exponential':
        scheduler = torch.optim.lr_scheduler.ExponentialLR(
            optimizer,
            gamma=sched_config.gamma
        )
    elif sched_config.name == 'reduce_on_plateau':
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode=sched_config.mode,
            factor=sched_config.factor,
            patience=sched_config.patience,
            threshold=sched_config.threshold,
            cooldown=sched_config.cooldown,
            min_lr=sched_config.min_lr
        )
    else:
        raise ValueError(f"Unknown scheduler: {sched_config.name}")
    
    return scheduler


def main(args):
    """Main training function."""
    # Load or create configuration
    if args.config:
        config = ExperimentConfig.load(args.config)
    else:
        config = create_default_config(
            name=args.name,
            model_type=args.model_type
        )
    
    # Override config with command line arguments
    if args.epochs:
        config.training.epochs = args.epochs
    if args.batch_size:
        config.training.batch_size = args.batch_size
        config.data.batch_size = args.batch_size
    if args.lr:
        config.training.optimizer.lr = args.lr
    
    # Setup directories
    config.setup_directories()
    exp_dir = config.get_experiment_dir()
    logger.info(f"Experiment directory: {exp_dir}")
    
    # Create or load dataset
    if args.data_path:
        dataset = ScINTEGDataset(
            expression_data=args.data_path,
            normalize=config.data.normalize,
            log_transform=config.data.log_transform,
            build_cell_graph=config.data.build_cell_graph,
            build_gene_graph=config.data.build_gene_graph,
            cell_graph_k=config.data.cell_graph_k,
            gene_graph_threshold=config.data.gene_graph_threshold
        )
    else:
        # Use synthetic data for demonstration
        dataset = create_synthetic_data()
    
    # Create data loaders
    loaders = create_data_loaders(
        dataset,
        train_ratio=config.data.train_ratio,
        val_ratio=config.data.val_ratio,
        test_ratio=config.data.test_ratio,
        batch_size=config.data.batch_size,
        num_workers=config.data.num_workers,
        pin_memory=config.data.pin_memory,
        balanced_sampling=config.data.balanced_sampling,
        balance_by=config.data.balance_by,
        rebuild_cell_graph_per_batch=config.data.rebuild_graph_per_batch
    )
    
    # Create model
    model = create_model(config, dataset)
    logger.info(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Create optimizer and scheduler
    optimizer = create_optimizer(model, config)
    scheduler = create_scheduler(optimizer, config)
    
    # Create trainer
    trainer = ScINTEGTrainer(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        device=config.training.device,
        checkpoint_dir=config.training.checkpoint_dir,
        gradient_clip_val=config.training.gradient_clip_val,
        accumulate_grad_batches=config.training.accumulate_grad_batches,
        mixed_precision=config.training.mixed_precision,
        val_check_interval=config.training.val_check_interval,
        early_stopping_patience=config.training.early_stopping_patience,
        early_stopping_min_delta=config.training.early_stopping_min_delta,
        log_interval=config.training.log_interval,
        verbose=True
    )
    
    # Setup checkpoint manager
    checkpoint_manager = CheckpointManager(
        checkpoint_dir=config.training.checkpoint_dir,
        max_checkpoints=config.training.save_top_k,
        save_best_only=False,
        monitor=config.training.checkpoint_monitor,
        mode=config.training.checkpoint_mode
    )
    
    # Wrap with auto-resume
    if args.resume:
        trainer = AutoResumeTrainer(
            trainer,
            checkpoint_manager,
            resume=args.resume
        )
    
    # Train model
    logger.info("Starting training...")
    trainer.fit(
        train_loader=loaders['train'],
        val_loader=loaders['val'],
        epochs=config.training.epochs
    )
    
    # Evaluate on test set
    if args.evaluate:
        logger.info("Evaluating on test set...")
        test_metrics = trainer._validate(loaders['test'])
        
        # Save results
        results_path = exp_dir / 'results' / 'test_results.json'
        results_path.parent.mkdir(exist_ok=True)
        
        import json
        with open(results_path, 'w') as f:
            json.dump(test_metrics, f, indent=2)
        
        logger.info(f"Test results saved to {results_path}")
        logger.info(f"Test metrics: {test_metrics}")
    
    logger.info("Training completed!")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train ScINTEG v3 model")
    
    # Experiment
    parser.add_argument('--name', type=str, default='scinteg_experiment',
                        help='Experiment name')
    parser.add_argument('--config', type=str, default=None,
                        help='Path to configuration file')
    
    # Data
    parser.add_argument('--data-path', type=str, default=None,
                        help='Path to data file')
    
    # Model
    parser.add_argument('--model-type', type=str, default='standard',
                        choices=['standard', 'hierarchical', 'unet'],
                        help='Model type')
    
    # Training
    parser.add_argument('--epochs', type=int, default=None,
                        help='Number of epochs')
    parser.add_argument('--batch-size', type=int, default=None,
                        help='Batch size')
    parser.add_argument('--lr', type=float, default=None,
                        help='Learning rate')
    parser.add_argument('--resume', type=str, default=None,
                        help='Resume from checkpoint')
    
    # Evaluation
    parser.add_argument('--evaluate', action='store_true',
                        help='Evaluate on test set after training')
    
    args = parser.parse_args()
    main(args)