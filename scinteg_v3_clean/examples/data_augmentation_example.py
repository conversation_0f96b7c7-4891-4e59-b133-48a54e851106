"""
Example of using data augmentation and network propagation features.

This example demonstrates:
1. Loading prior networks with caching
2. Feature propagation through cell graphs
3. Data augmentation with noise
"""

import torch
import numpy as np
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scinteg.data import (
    ScINTEGDataset,
    propagate_features,
    add_noise_to_expression,
    load_prior_network_from_url
)


def main():
    """Main example function."""
    print("ScINTEG v3 Data Augmentation Example")
    print("=" * 50)
    
    # 1. Create example dataset
    print("\n1. Creating example dataset...")
    n_cells = 200
    n_genes = 1000
    
    # Generate synthetic expression
    np.random.seed(42)
    expression = np.random.lognormal(0, 1, (n_cells, n_genes))
    expression_tensor = torch.FloatTensor(expression)
    
    # Create dataset
    dataset = ScINTEGDataset(
        expression_data=expression,
        normalize=True,
        log_transform=True,
        build_cell_graph=True,
        cell_graph_k=10
    )
    print(f"   Dataset: {dataset.n_cells} cells, {dataset.n_genes} genes")
    
    # 2. Network propagation example
    print("\n2. Demonstrating network propagation...")
    
    # Get cell graph adjacency matrix
    cell_graph = dataset.get_cell_graph()
    n_nodes = dataset.n_cells
    adjacency = torch.zeros(n_nodes, n_nodes)
    
    # Convert edge list to adjacency matrix
    for i in range(cell_graph.shape[1]):
        src, tgt = cell_graph[:, i]
        adjacency[src, tgt] = 1.0
        adjacency[tgt, src] = 1.0  # Undirected
    
    # Create a signal on specific cells
    signal = torch.zeros(n_cells)
    signal[:10] = 1.0  # First 10 cells have signal
    
    # Propagate signal
    propagated = propagate_features(signal, adjacency, alpha=0.8)
    
    print(f"   Original signal: {signal.sum().item():.0f} cells")
    print(f"   After propagation: {(propagated > 0.1).sum().item()} cells have signal > 0.1")
    print(f"   Max propagated value: {propagated.max().item():.3f}")
    
    # 3. Data augmentation
    print("\n3. Applying data augmentation...")
    
    # Get expression matrix
    expr = dataset.expression
    
    # Apply Gaussian noise
    noisy_gaussian = add_noise_to_expression(
        expr, noise_level=0.05, noise_type='gaussian', random_seed=42
    )
    
    # Apply dropout noise
    noisy_dropout = add_noise_to_expression(
        expr, noise_level=0.1, noise_type='dropout', random_seed=42
    )
    
    # Compare statistics
    print(f"   Original expression - mean: {expr.mean():.3f}, std: {expr.std():.3f}")
    print(f"   Gaussian noise - mean: {noisy_gaussian.mean():.3f}, std: {noisy_gaussian.std():.3f}")
    print(f"   Dropout noise - mean: {noisy_dropout.mean():.3f}, zeros: {(noisy_dropout == 0).float().mean():.1%}")
    
    # 4. Load prior network (demonstration with mock data)
    print("\n4. Loading prior network with caching...")
    
    # For demonstration, we'll use mock data
    mock_network_data = "from,to\nTP53,MDM2\nTP53,CDKN1A\nMYC,CCND1\nEGFR,KRAS\n"
    
    network = load_prior_network_from_url(
        url="http://example.com/demo_network.csv",
        cache_dir="./demo_cache",
        _test_data=mock_network_data
    )
    
    print(f"   Loaded network with {len(network)} edges")
    print("   Sample edges:")
    for _, row in network.head(3).iterrows():
        print(f"     {row['from']} -> {row['to']}")
    
    # 5. Combine augmentation with propagation
    print("\n5. Combining augmentation with propagation...")
    
    # Add noise to expression
    noisy_expr = add_noise_to_expression(expr, noise_level=0.1, noise_type='gaussian')
    
    # Select a gene and propagate its expression
    gene_idx = 0
    gene_expr = noisy_expr[:, gene_idx]
    
    # Propagate gene expression through cell graph
    propagated_expr = propagate_features(gene_expr, adjacency, alpha=0.7)
    
    print(f"   Gene {gene_idx} expression:")
    print(f"     Original mean: {expr[:, gene_idx].mean():.3f}")
    print(f"     Noisy mean: {gene_expr.mean():.3f}")
    print(f"     Propagated mean: {propagated_expr.mean():.3f}")
    
    print("\n✅ Example completed successfully!")


if __name__ == "__main__":
    main()