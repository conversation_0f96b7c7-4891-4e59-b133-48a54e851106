# ScINTEG v3 架构文档

## 目录
1. [概述](#概述)
2. [核心设计原则](#核心设计原则)
3. [架构图](#架构图)
4. [组件详解](#组件详解)
5. [数据流](#数据流)
6. [接口规范](#接口规范)
7. [扩展指南](#扩展指南)

## 概述

ScINTEG v3 是一个模块化的单细胞RNA测序数据基因调控网络推断框架。它通过整合细胞和基因的图结构信息，结合生物学通路知识，实现高质量的基因调控网络推断。

### 主要特性
- **模块化设计**: 所有组件可独立使用和测试
- **标准化接口**: 统一的数据结构确保组件间无缝集成
- **生物学约束**: 整合通路信息和TF注释等先验知识
- **多种架构选择**: 支持标准和U-Net解码器，分层通路投影等
- **灵活的损失函数**: MSE、NB、ZINB等多种选择

## 核心设计原则

### 1. 继承体系
```
BaseModule (抽象基类)
├── BaseEncoder
│   ├── StandardCellEncoder
│   └── StandardGeneEncoder
├── BaseProjector
│   ├── StandardPathwayProjector
│   └── HierarchicalPathwayProjector
├── BaseDecoder
│   ├── StandardExpressionDecoder
│   └── StandardUNetDecoder
└── BasePredictor
    └── StandardGRNPredictor
```

### 2. 标准化数据流
所有组件使用标准化的输入输出接口：
- 输入：标准数据结构（如 `ModelInputs`）
- 输出：标准输出结构（如 `EncoderOutputs`）
- 配置：支持保存/加载配置

### 3. 可组合性
组件可以灵活组合，例如：
- 不同的编码器架构（GCN vs GAT）
- 不同的投影策略（标准 vs 分层）
- 不同的解码器（MLP vs U-Net）

## 架构图

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        ScINTEG v3                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌───────────┐     ┌───────────┐     ┌──────────────┐     │
│  │   Input   │     │ Encoders  │     │  Projectors  │     │
│  │   Data    │ --> │ Cell/Gene │ --> │   Pathway    │     │
│  └───────────┘     └───────────┘     └──────────────┘     │
│                                               │             │
│                                               ▼             │
│  ┌───────────┐     ┌───────────┐     ┌──────────────┐     │
│  │  Output   │ <-- │ Predictors│ <-- │   Decoders   │     │
│  │   Data    │     │    GRN    │     │  Expression  │     │
│  └───────────┘     └───────────┘     └──────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 数据流详细图
```
表达矩阵 (n_cells × n_genes)
    │
    ├─────────────────┬─────────────────┐
    ▼                 ▼                 │
细胞图 → 细胞编码器 → 细胞嵌入         │
                      │                 │
                      ▼                 ▼
                 通路投影器 ← 通路掩码
                      │
                      ├──→ 通路特征
                      │       │
                      │       ▼
基因图 → 基因编码器 → 基因嵌入 → 表达解码器 → 重建表达
              │                           ▲
              │                           │
              └──→ GRN预测器 ────────────┘
                      │
                      ▼
                  基因调控网络
```

## 组件详解

### 1. 编码器 (Encoders)

#### StandardCellEncoder
- **功能**: 将细胞表达谱编码为低维嵌入
- **输入**: 
  - 表达矩阵: `[batch_size, n_genes]`
  - 细胞相似性图: `[2, n_edges]`
- **输出**: 
  - 细胞嵌入: `[batch_size, cell_embedding_dim]`
- **架构选择**:
  - GCN (图卷积网络)
  - GAT (图注意力网络)
- **关键参数**:
  ```python
  cell_encoder = StandardCellEncoder(
      input_dim=n_genes,
      hidden_dim=256,
      output_dim=128,
      n_layers=2,
      conv_type="gat",  # or "gcn"
      n_heads=8,        # for GAT
      dropout=0.1
  )
  ```

#### StandardGeneEncoder
- **功能**: 编码基因特征，整合生物学先验
- **输入**:
  - 基因索引: `[n_genes_batch]`
  - 基因共表达图: `[2, n_edges]`
  - 生物学先验（可选）: TF索引、通路掩码
- **输出**:
  - 基因嵌入: `[n_genes, gene_embedding_dim]`
  - 注意力权重: 多层注意力图
  - 重要性分数: `[n_genes, 1]`
- **特殊功能**:
  - 位置编码
  - TF特异性注意力
  - 通路感知注意力

### 2. 投影器 (Projectors)

#### StandardPathwayProjector
- **功能**: 将细胞嵌入投影到生物学通路空间
- **核心创新**: 可学习的软掩码机制
- **数学原理**:
  ```
  硬掩码: z_p = W_p @ h_c ⊙ M
  软掩码: z_p = W_p @ h_c ⊙ σ(M_soft)
  ```
- **输出特征**:
  - 通路激活度
  - 当前掩码状态
  - 通路稀疏度统计

#### HierarchicalPathwayProjector
- **功能**: 两级投影减少信息损失
- **架构**:
  ```
  细胞嵌入 → 通路层 → 元通路层
            (50-100)   (10-20)
  ```
- **优势**: >99%信息保留 vs 64%（单层投影）
- **应用场景**: 当通路数量远小于基因数量时

### 3. 解码器 (Decoders)

#### StandardExpressionDecoder
- **功能**: 从通路特征重建基因表达
- **架构**: 深度MLP with skip connections
- **损失函数支持**:
  - MSE: 标准均方误差
  - NB: 负二项分布（适合count数据）
  - ZINB: 零膨胀负二项分布（处理dropout）
- **特殊功能**:
  - 基因特异性调制
  - 残差连接
  - 层归一化

#### StandardUNetDecoder
- **功能**: 使用U-Net架构改善重建质量
- **架构特点**:
  ```
  编码路径: Conv → Pool → Conv → Pool
      ↓         ↘           ↘
  解码路径: Deconv ← Concat ← Deconv
  ```
- **优势**:
  - 多尺度特征融合
  - 更好的细节保留
  - 注意力机制增强

### 4. 预测器 (Predictors)

#### StandardGRNPredictor
- **功能**: 推断基因调控网络
- **推断模式**:
  1. **注意力模式**: 使用基因编码器的注意力权重
  2. **嵌入相似性**: 计算基因嵌入的余弦相似度
  3. **低秩分解**: 使用低秩矩阵分解提高效率
  4. **混合模式**: 结合多种方法
- **关键特性**:
  - **度数缩放**: 防止hub基因过度主导
  - **稀疏化**: 保留top-k边
  - **动态评分**: 整合基因重要性

### 5. 损失函数 (Losses)

#### ScINTEGLoss
- **组成部分**:
  1. **重建损失**: MSE/NB/ZINB
  2. **GRN正则化**: 
     - L1稀疏性
     - 熵正则化
     - 度数约束
  3. **通路稀疏性**: KL散度约束
- **自适应权重**: 可选的动态损失权重平衡

## 数据流

### 前向传播流程
```python
# 1. 输入准备
inputs = ModelInputs(
    expression=expression_matrix,
    cell_graph=cell_similarity_graph,
    gene_graph=gene_coexpression_graph
)

# 2. 编码阶段
cell_embeddings = cell_encoder(inputs.expression, inputs.cell_graph)
gene_embeddings = gene_encoder(gene_indices, inputs.gene_graph)

# 3. 投影阶段
pathway_features = pathway_projector(cell_embeddings)

# 4. 解码阶段
reconstructed_expr = expression_decoder(pathway_features, gene_embeddings)

# 5. GRN推断
grn = grn_predictor(gene_embeddings, attention_weights)

# 6. 损失计算
loss = loss_fn(reconstructed_expr, inputs.expression, grn)
```

### 信息流动路径
1. **细胞信息流**: 表达 → 细胞图 → 细胞嵌入 → 通路特征
2. **基因信息流**: 索引 → 基因图 → 基因嵌入 → GRN
3. **整合流**: 通路特征 + 基因嵌入 → 重建表达

## 接口规范

### 标准输入格式
```python
@dataclass
class ModelInputs:
    expression: Tensor      # [n_cells, n_genes]
    cell_graph: Tensor      # [2, n_cell_edges]
    gene_graph: Tensor      # [2, n_gene_edges]
    batch_labels: Optional[Tensor] = None
    time_labels: Optional[Tensor] = None
```

### 标准输出格式
```python
@dataclass
class ModelOutputs:
    reconstruction: Tensor  # [n_cells, n_genes]
    grn: Dict[str, Tensor] # edge_index, edge_weight, adjacency
    cell_embeddings: Tensor
    gene_embeddings: Tensor
    pathway_features: Tensor
    meta_pathway_features: Optional[Tensor]
    attention_weights: Dict[str, List[Tensor]]
    auxiliary: Dict[str, Any]
```

### 配置管理
所有组件支持：
```python
# 保存配置
config = model.get_config()
model.save_config("model_config.yaml")

# 从配置加载
model = ScINTEGv3.from_config("model_config.yaml")
```

## 扩展指南

### 添加新的编码器
1. 继承 `BaseEncoder`
2. 实现必要方法：
   ```python
   class MyEncoder(BaseEncoder):
       def _validate_config(self):
           # 验证参数
           
       def _build(self):
           # 构建网络
           
       def encode(self, x, *args):
           # 编码逻辑
           return EncoderOutputs(...)
   ```

### 添加新的损失函数
1. 继承 `nn.Module`
2. 实现前向传播：
   ```python
   class MyLoss(nn.Module):
       def forward(self, pred, target):
           # 计算损失
           return loss
   ```

### 集成新组件
1. 在相应的 `__init__.py` 中注册
2. 更新主模型以支持新组件
3. 添加相应的测试

## 性能优化建议

1. **批处理**: 使用DataLoader进行高效批处理
2. **图稀疏化**: 对大规模数据使用k-NN图而非全连接
3. **混合精度**: 使用PyTorch的自动混合精度训练
4. **并行化**: 利用多GPU进行数据并行训练

## 常见问题

### Q: 如何选择合适的通路数量？
A: 建议从50-100个通路开始，使用分层投影可以进一步压缩到10-20个元通路。

### Q: GCN vs GAT如何选择？
A: GAT在需要学习边权重时更有优势，但计算成本较高。对于密集图，GCN通常足够。

### Q: 如何处理批次效应？
A: 可以在损失函数中加入批次对抗项，或使用批次标签作为额外输入。

## 未来发展方向

1. **时序建模**: 整合时间序列信息
2. **多模态整合**: 支持空间转录组等数据
3. **可解释性**: 增强模型解释性工具
4. **自动架构搜索**: 自动选择最优组件组合