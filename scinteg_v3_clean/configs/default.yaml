# Default ScINTEG v3 Configuration

model:
  # Model dimensions
  n_pathways: 50
  n_meta_pathways: 20
  
  # Encoders
  cell_encoder:
    type: "standard"
    input_dim: null  # Inferred from data
    hidden_dim: 256
    output_dim: 64
    n_layers: 2
    dropout: 0.1
    use_batch_norm: true
    activation: "gelu"
  
  gene_encoder:
    type: "standard"
    feature_dim: 16
    hidden_dim: 256
    output_dim: 64
    n_layers: 2
    n_heads: 8
    dropout: 0.1
    use_multi_scale: true
  
  # Projectors
  pathway_projector:
    type: "hierarchical"
    use_soft_mask: true
    dropout: 0.1
  
  # Decoders
  expression_decoder:
    type: "unet"
    n_levels: 3
    hidden_dims: [64, 128, 256]
    use_skip_connections: true
    use_attention: true
    dropout: 0.1
  
  # Predictors
  grn_predictor:
    type: "attention"
    temperature: 1.0
    top_k_edges_per_gene: 50
  
  # Optional components
  use_batch_correction: true
  use_temporal_consistency: true
  use_contrastive_loss: false

training:
  # Optimization
  optimizer: "adamw"
  learning_rate: 1.0e-3
  weight_decay: 1.0e-4
  gradient_clip: 1.0
  
  # Scheduling
  scheduler: "cosine"
  warmup_epochs: 10
  
  # Training parameters
  epochs: 100
  batch_size: 128
  early_stopping_patience: 20
  
  # Loss weights
  loss_weights:
    reconstruction: 1.0
    grn: 0.1
    mask_sparsity: 0.1
    temporal: 0.05
    contrastive: 0.0

data:
  # Preprocessing
  normalize: true
  log_transform: true
  scale: true
  
  # Graph construction
  cell_graph:
    n_neighbors: 15
    metric: "euclidean"
  
  gene_graph:
    threshold: 0.3
    metric: "correlation"
  
  # Filtering
  min_genes: 200
  min_cells: 3
  
  # Batch size for data loading
  loader_batch_size: 1000

evaluation:
  # Metrics to compute
  metrics:
    - "reconstruction_correlation"
    - "reconstruction_mse"
    - "pathway_enrichment"
    - "grn_auroc"
  
  # Visualization
  visualize:
    - "embeddings"
    - "grn_network"
    - "pathway_activity"
    - "reconstruction_scatter"

# Hardware
device: "auto"  # auto, cpu, cuda, cuda:0, etc.
mixed_precision: false
num_workers: 4

# Logging
logging:
  level: "INFO"
  save_dir: "logs"
  tensorboard: true
  
# Checkpointing
checkpoint:
  save_dir: "checkpoints"
  save_frequency: 10
  keep_last_n: 3
  save_best: true