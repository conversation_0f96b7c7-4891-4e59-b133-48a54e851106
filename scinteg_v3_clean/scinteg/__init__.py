"""
ScINTEG - Single-cell Integrative Gene Regulatory Network Inference

A comprehensive framework for inferring gene regulatory networks from
single-cell RNA-seq data with pathway-level analysis.
"""

__version__ = "3.0.0"
__author__ = "ScINTEG Team"

# Import core components
from .core.base import (
    BaseModule,
    BaseEncoder, 
    BaseDecoder,
    BaseProjector,
    BasePredictor,
    BaseLoss
)

from .core.interfaces import (
    ModelInputs,
    ModelOutputs,
    DataBatch
)

# Make key classes available at package level
__all__ = [
    # Version
    "__version__",
    
    # Base classes
    "BaseModule",
    "BaseEncoder",
    "BaseDecoder", 
    "BaseProjector",
    "BasePredictor",
    "BaseLoss",
    
    # Data structures
    "ModelInputs",
    "ModelOutputs",
    "DataBatch",
]