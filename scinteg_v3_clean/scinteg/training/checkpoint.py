"""
Checkpoint management for ScINTEG v3.

This module provides utilities for saving and loading model checkpoints,
including automatic recovery and checkpoint selection.
"""

import torch
import torch.nn as nn
from pathlib import Path
import json
import shutil
from typing import Dict, Optional, Union, Any, List
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class CheckpointManager:
    """
    Manages model checkpoints with automatic cleanup and recovery.
    """
    
    def __init__(
        self,
        checkpoint_dir: Union[str, Path],
        max_checkpoints: int = 5,
        save_best_only: bool = False,
        monitor: str = 'val_loss',
        mode: str = 'min'
    ):
        """
        Initialize checkpoint manager.
        
        Args:
            checkpoint_dir: Directory for checkpoints
            max_checkpoints: Maximum number of checkpoints to keep
            save_best_only: Whether to save only the best model
            monitor: Metric to monitor for best model
            mode: 'min' or 'max' for the monitored metric
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_checkpoints = max_checkpoints
        self.save_best_only = save_best_only
        self.monitor = monitor
        self.mode = mode
        
        # Track checkpoints
        self.checkpoints = []
        self.best_score = float('inf') if mode == 'min' else float('-inf')
        self.best_checkpoint = None
        
        # Load existing checkpoints
        self._scan_existing_checkpoints()
    
    def save(
        self,
        model: nn.Module,
        optimizer: torch.optim.Optimizer,
        epoch: int,
        metrics: Dict[str, float],
        additional_state: Optional[Dict[str, Any]] = None,
        name: Optional[str] = None
    ) -> Optional[Path]:
        """
        Save a checkpoint.
        
        Args:
            model: Model to save
            optimizer: Optimizer state
            epoch: Current epoch
            metrics: Current metrics
            additional_state: Additional state to save
            name: Optional checkpoint name
            
        Returns:
            Path to saved checkpoint or None
        """
        # Check if we should save
        if self.save_best_only:
            current_score = metrics.get(self.monitor, 0)
            is_best = self._is_better(current_score, self.best_score)
            
            if not is_best:
                return None
            
            self.best_score = current_score
        
        # Generate checkpoint name
        if name is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            name = f'checkpoint_epoch_{epoch}_{timestamp}'
        
        checkpoint_path = self.checkpoint_dir / f'{name}.pt'
        
        # Prepare checkpoint data
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'metrics': metrics,
            'timestamp': datetime.now().isoformat(),
            'model_config': model.get_model_config() if hasattr(model, 'get_model_config') else None
        }
        
        if additional_state:
            checkpoint.update(additional_state)
        
        # Save checkpoint
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Saved checkpoint to {checkpoint_path}")
        
        # Save metadata
        metadata = {
            'epoch': epoch,
            'metrics': metrics,
            'timestamp': checkpoint['timestamp'],
            'path': str(checkpoint_path)
        }
        self._save_metadata(checkpoint_path, metadata)
        
        # Track checkpoint
        self.checkpoints.append({
            'path': checkpoint_path,
            'epoch': epoch,
            'score': metrics.get(self.monitor, 0),
            'metrics': metrics
        })
        
        # Update best checkpoint
        if self.save_best_only:
            self.best_checkpoint = checkpoint_path
            # Create symlink to best checkpoint
            best_link = self.checkpoint_dir / 'best.pt'
            if best_link.exists():
                best_link.unlink()
            best_link.symlink_to(checkpoint_path.name)
        
        # Cleanup old checkpoints
        self._cleanup_checkpoints()
        
        return checkpoint_path
    
    def load(
        self,
        model: nn.Module,
        optimizer: Optional[torch.optim.Optimizer] = None,
        checkpoint: Union[str, Path, None] = None,
        map_location: Optional[torch.device] = None
    ) -> Dict[str, Any]:
        """
        Load a checkpoint.
        
        Args:
            model: Model to load state into
            optimizer: Optimizer to load state into
            checkpoint: Checkpoint to load (None for latest)
            map_location: Device mapping
            
        Returns:
            Checkpoint dictionary
        """
        # Determine which checkpoint to load
        if checkpoint is None:
            # Load latest or best
            if self.best_checkpoint and self.best_checkpoint.exists():
                checkpoint_path = self.best_checkpoint
            elif self.checkpoints:
                checkpoint_path = self.checkpoints[-1]['path']
            else:
                raise ValueError("No checkpoints found")
        else:
            checkpoint_path = Path(checkpoint)
        
        # Load checkpoint
        logger.info(f"Loading checkpoint from {checkpoint_path}")
        state = torch.load(checkpoint_path, map_location=map_location)
        
        # Load model state
        model.load_state_dict(state['model_state_dict'])
        
        # Load optimizer state
        if optimizer and 'optimizer_state_dict' in state:
            optimizer.load_state_dict(state['optimizer_state_dict'])
        
        return state
    
    def get_best_checkpoint(self) -> Optional[Path]:
        """Get path to best checkpoint."""
        return self.best_checkpoint
    
    def get_latest_checkpoint(self) -> Optional[Path]:
        """Get path to latest checkpoint."""
        if self.checkpoints:
            return self.checkpoints[-1]['path']
        return None
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """List all available checkpoints."""
        return self.checkpoints.copy()
    
    def _is_better(self, current: float, best: float) -> bool:
        """Check if current score is better than best."""
        if self.mode == 'min':
            return current < best
        else:
            return current > best
    
    def _cleanup_checkpoints(self):
        """Remove old checkpoints if exceeding max_checkpoints."""
        if len(self.checkpoints) <= self.max_checkpoints:
            return
        
        # Sort by score (keep best)
        sorted_checkpoints = sorted(
            self.checkpoints,
            key=lambda x: x['score'],
            reverse=(self.mode == 'max')
        )
        
        # Keep top checkpoints
        to_keep = sorted_checkpoints[:self.max_checkpoints]
        to_remove = sorted_checkpoints[self.max_checkpoints:]
        
        # Remove old checkpoints
        for checkpoint in to_remove:
            checkpoint_path = checkpoint['path']
            if checkpoint_path.exists():
                checkpoint_path.unlink()
                logger.info(f"Removed old checkpoint: {checkpoint_path}")
            
            # Remove metadata
            metadata_path = checkpoint_path.with_suffix('.json')
            if metadata_path.exists():
                metadata_path.unlink()
        
        self.checkpoints = to_keep
    
    def _save_metadata(self, checkpoint_path: Path, metadata: Dict[str, Any]):
        """Save checkpoint metadata."""
        metadata_path = checkpoint_path.with_suffix('.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def _scan_existing_checkpoints(self):
        """Scan directory for existing checkpoints."""
        for checkpoint_path in self.checkpoint_dir.glob('*.pt'):
            if checkpoint_path.name == 'best.pt':
                continue
            
            # Load metadata if exists
            metadata_path = checkpoint_path.with_suffix('.json')
            if metadata_path.exists():
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                self.checkpoints.append({
                    'path': checkpoint_path,
                    'epoch': metadata.get('epoch', 0),
                    'score': metadata['metrics'].get(self.monitor, 0),
                    'metrics': metadata.get('metrics', {})
                })
        
        # Sort by epoch
        self.checkpoints.sort(key=lambda x: x['epoch'])
        
        # Find best checkpoint
        if self.checkpoints:
            best_checkpoint = min(
                self.checkpoints,
                key=lambda x: x['score'] if self.mode == 'min' else -x['score']
            )
            self.best_checkpoint = best_checkpoint['path']
            self.best_score = best_checkpoint['score']


class AutoResumeTrainer:
    """
    Wrapper for trainer with automatic checkpoint recovery.
    """
    
    def __init__(
        self,
        trainer: Any,
        checkpoint_manager: CheckpointManager,
        resume: Union[bool, str, Path] = True
    ):
        """
        Initialize auto-resume trainer.
        
        Args:
            trainer: Base trainer instance
            checkpoint_manager: Checkpoint manager
            resume: Whether to resume from checkpoint
                   True: resume from latest
                   False: start fresh
                   str/Path: resume from specific checkpoint
        """
        self.trainer = trainer
        self.checkpoint_manager = checkpoint_manager
        self.resume = resume
        
        # Resume if requested
        if resume:
            self._resume_training()
    
    def _resume_training(self):
        """Resume training from checkpoint."""
        if isinstance(self.resume, bool):
            # Resume from latest
            checkpoint_path = self.checkpoint_manager.get_latest_checkpoint()
        else:
            # Resume from specific checkpoint
            checkpoint_path = Path(self.resume)
        
        if checkpoint_path and checkpoint_path.exists():
            logger.info(f"Resuming training from {checkpoint_path}")
            
            # Load checkpoint
            state = self.checkpoint_manager.load(
                self.trainer.model,
                self.trainer.optimizer,
                checkpoint_path
            )
            
            # Restore trainer state
            self.trainer.current_epoch = state.get('epoch', 0)
            self.trainer.global_step = state.get('global_step', 0)
            self.trainer.best_val_loss = state.get('best_val_loss', float('inf'))
            self.trainer.training_history = state.get('training_history', {})
            
            # Restore scheduler state
            if self.trainer.scheduler and 'scheduler_state_dict' in state:
                self.trainer.scheduler.load_state_dict(state['scheduler_state_dict'])
            
            logger.info(f"Resumed from epoch {self.trainer.current_epoch}")
        else:
            logger.info("No checkpoint found, starting fresh")
    
    def fit(self, *args, **kwargs):
        """Fit with automatic checkpointing."""
        # Add checkpoint callback
        original_callbacks = kwargs.get('callbacks', [])
        callbacks = original_callbacks + [self._checkpoint_callback]
        kwargs['callbacks'] = callbacks
        
        # Run training
        return self.trainer.fit(*args, **kwargs)
    
    def _checkpoint_callback(self, trainer, epoch):
        """Callback to save checkpoints."""
        # Get current metrics
        metrics = {
            'train_loss': trainer.training_history['train_loss'][-1],
        }
        
        if 'val_loss' in trainer.training_history and trainer.training_history['val_loss']:
            metrics['val_loss'] = trainer.training_history['val_loss'][-1]
        
        # Additional state
        additional_state = {
            'global_step': trainer.global_step,
            'best_val_loss': trainer.best_val_loss,
            'training_history': trainer.training_history,
            'scheduler_state_dict': trainer.scheduler.state_dict() if trainer.scheduler else None
        }
        
        # Save checkpoint
        self.checkpoint_manager.save(
            trainer.model,
            trainer.optimizer,
            epoch,
            metrics,
            additional_state
        )


def find_checkpoint(
    checkpoint_dir: Union[str, Path],
    pattern: Optional[str] = None,
    best: bool = False,
    latest: bool = False
) -> Optional[Path]:
    """
    Find a checkpoint in directory.
    
    Args:
        checkpoint_dir: Directory to search
        pattern: Pattern to match (e.g., 'epoch_50')
        best: Return best checkpoint
        latest: Return latest checkpoint
        
    Returns:
        Path to checkpoint or None
    """
    checkpoint_dir = Path(checkpoint_dir)
    
    if best:
        best_path = checkpoint_dir / 'best.pt'
        if best_path.exists():
            # Follow symlink if exists
            if best_path.is_symlink():
                return best_path.resolve()
            return best_path
    
    # Find all checkpoints
    checkpoints = list(checkpoint_dir.glob('*.pt'))
    
    if pattern:
        # Filter by pattern
        checkpoints = [cp for cp in checkpoints if pattern in cp.name]
    
    if not checkpoints:
        return None
    
    if latest:
        # Return most recent
        return max(checkpoints, key=lambda p: p.stat().st_mtime)
    
    # Return first match
    return checkpoints[0]