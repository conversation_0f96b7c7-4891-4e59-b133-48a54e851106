"""
Loss functions for ScINTEG v3.

This module provides various loss functions for training ScINTEG:
- Reconstruction losses (MSE, NB, ZINB)
- GRN regularization losses
- Pathway sparsity losses
- Combined loss with adaptive weighting
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, Any, Union
import warnings


class MSELoss(nn.Module):
    """Mean Squared Error loss for expression reconstruction."""
    
    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.reduction = reduction
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute MSE loss."""
        loss = F.mse_loss(pred, target, reduction=self.reduction)
        return loss


class NegativeBinomialLoss(nn.Module):
    """
    Negative Binomial loss for count data.
    
    Suitable for modeling overdispersed count data like gene expression.
    """
    
    def __init__(self, eps: float = 1e-10):
        """
        Initialize NB loss.
        
        Args:
            eps: Small constant for numerical stability
        """
        super().__init__()
        self.eps = eps
    
    def forward(
        self,
        x_true: torch.Tensor,
        mu: torch.Tensor,
        theta: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute negative binomial loss.
        
        Args:
            x_true: Ground truth counts [batch_size, n_genes]
            mu: Predicted mean parameter [batch_size, n_genes]
            theta: Dispersion parameter [batch_size, n_genes]
            
        Returns:
            Loss value
        """
        # Ensure positive parameters
        mu = mu + self.eps
        theta = theta + self.eps
        
        # If theta is scalar, expand it
        if theta.dim() == 0:
            theta = theta.expand_as(mu)
        
        # Log likelihood computation
        log_theta_mu = torch.log(theta + mu + self.eps)
        
        t1 = (torch.lgamma(theta + x_true + self.eps) - 
              torch.lgamma(x_true + 1.0) - 
              torch.lgamma(theta))
        t2 = theta * (torch.log(theta + self.eps) - log_theta_mu)
        t3 = x_true * (torch.log(mu + self.eps) - log_theta_mu)
        
        log_likelihood = t1 + t2 + t3
        
        # Negative log likelihood
        loss = -log_likelihood
        
        return loss.mean()


class ZeroInflatedNegativeBinomialLoss(nn.Module):
    """
    Zero-Inflated Negative Binomial loss.
    
    Models count data with excess zeros.
    """
    
    def __init__(self, eps: float = 1e-8, ridge_lambda: float = 0.0):
        """
        Initialize ZINB loss.
        
        Args:
            eps: Small constant for numerical stability
            ridge_lambda: L2 regularization for pi_logits
        """
        super().__init__()
        self.eps = eps
        self.ridge_lambda = ridge_lambda
    
    def forward(
        self,
        x_true: torch.Tensor,
        mu: torch.Tensor,
        theta: torch.Tensor,
        pi_logits: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute ZINB loss.
        
        Args:
            x_true: Ground truth counts [batch_size, n_genes]
            mu: Predicted mean of NB [batch_size, n_genes]
            theta: Predicted dispersion of NB [batch_size, n_genes]
            pi_logits: Logits for zero-inflation probability [batch_size, n_genes]
            
        Returns:
            Loss value
        """
        # Ensure positive parameters
        mu = torch.clamp(mu, min=self.eps)
        theta = torch.clamp(theta, min=self.eps)
        
        # Zero-inflation probability
        pi = torch.sigmoid(pi_logits)
        
        # NB log probability
        log_theta_mu = torch.log(theta + mu + self.eps)
        
        log_nb_part = (
            torch.lgamma(theta + x_true + self.eps) -
            torch.lgamma(x_true + 1.0) -
            torch.lgamma(theta + self.eps) +
            theta * (torch.log(theta + self.eps) - log_theta_mu) +
            x_true * (torch.log(mu + self.eps) - log_theta_mu)
        )
        
        # Zero case: log(pi + (1-pi) * NB(0|mu,theta))
        log_nb_zero = theta * (torch.log(theta + self.eps) - log_theta_mu)
        nb_zero_prob = torch.exp(log_nb_zero)
        log_case_zero = torch.log(pi + (1.0 - pi) * nb_zero_prob + self.eps)
        
        # Non-zero case: log((1-pi) * NB(x|mu,theta))
        log_case_non_zero = torch.log(1.0 - pi + self.eps) + log_nb_part
        
        # Combine cases
        is_zero = (x_true < self.eps)
        log_likelihood = torch.where(is_zero, log_case_zero, log_case_non_zero)
        
        # Negative log likelihood
        loss = -log_likelihood.mean()
        
        # Optional ridge regularization
        if self.ridge_lambda > 0:
            ridge_penalty = self.ridge_lambda * (pi_logits ** 2).mean()
            loss = loss + ridge_penalty
        
        # Scale to match other losses
        loss = loss / 2.3
        
        return loss


class GRNRegularizationLoss(nn.Module):
    """
    Regularization loss for gene regulatory networks.
    
    Encourages sparsity and biological plausibility.
    """
    
    def __init__(
        self,
        sparsity_weight: float = 0.01,
        entropy_weight: float = 0.001,
        max_in_degree: Optional[int] = None
    ):
        """
        Initialize GRN regularization.
        
        Args:
            sparsity_weight: Weight for L1 sparsity
            entropy_weight: Weight for entropy regularization
            max_in_degree: Maximum in-degree constraint
        """
        super().__init__()
        self.sparsity_weight = sparsity_weight
        self.entropy_weight = entropy_weight
        self.max_in_degree = max_in_degree
    
    def forward(
        self,
        edge_weights: torch.Tensor,
        edge_index: Optional[torch.Tensor] = None,
        n_genes: Optional[int] = None
    ) -> torch.Tensor:
        """
        Compute GRN regularization loss.
        
        Args:
            edge_weights: Edge weights in GRN
            edge_index: Edge indices [2, n_edges]
            n_genes: Number of genes (for degree constraints)
            
        Returns:
            Regularization loss
        """
        loss = 0.0
        
        # L1 sparsity
        if self.sparsity_weight > 0:
            sparsity_loss = self.sparsity_weight * edge_weights.abs().mean()
            loss = loss + sparsity_loss
        
        # Entropy regularization
        if self.entropy_weight > 0:
            # Normalize weights per source gene
            if edge_index is not None and n_genes is not None:
                entropy_loss = 0.0
                gene_count = 0
                for i in range(n_genes):
                    mask = edge_index[0] == i
                    if mask.any():
                        weights = edge_weights[mask]
                        probs = F.softmax(weights, dim=0)
                        entropy = -(probs * torch.log(probs + 1e-8)).sum()
                        entropy_loss += entropy
                        gene_count += 1
                if gene_count > 0:
                    entropy_loss = self.entropy_weight * entropy_loss / gene_count
                else:
                    entropy_loss = torch.tensor(0.0, device=edge_weights.device)
            else:
                # Simple entropy on all weights
                probs = F.softmax(edge_weights, dim=0)
                entropy_loss = -self.entropy_weight * (probs * torch.log(probs + 1e-8)).sum()
            
            loss = loss + entropy_loss
        
        # In-degree constraint
        if self.max_in_degree is not None and edge_index is not None and n_genes is not None:
            # Count in-degrees
            in_degrees = torch.zeros(n_genes, device=edge_weights.device)
            in_degrees.scatter_add_(0, edge_index[1], torch.ones_like(edge_index[1], dtype=torch.float))
            
            # Penalize excessive in-degrees
            excess = F.relu(in_degrees - self.max_in_degree)
            degree_loss = excess.mean()
            loss = loss + degree_loss
        
        return loss


class PathwaySparsityLoss(nn.Module):
    """
    Sparsity loss for pathway activations.
    
    Encourages sparse pathway usage.
    """
    
    def __init__(self, sparsity_target: float = 0.2, weight: float = 0.01):
        """
        Initialize pathway sparsity loss.
        
        Args:
            sparsity_target: Target sparsity level
            weight: Loss weight
        """
        super().__init__()
        self.sparsity_target = sparsity_target
        self.weight = weight
    
    def forward(self, pathway_features: torch.Tensor) -> torch.Tensor:
        """
        Compute sparsity loss.
        
        Args:
            pathway_features: Pathway activation features
            
        Returns:
            Sparsity loss
        """
        # Compute mean activation per pathway
        mean_activation = pathway_features.abs().mean(dim=0)
        
        # Clamp to avoid numerical issues
        mean_activation = torch.clamp(mean_activation, min=1e-8, max=1-1e-8)
        
        # KL divergence from target sparsity
        kl_div = mean_activation * torch.log(mean_activation / self.sparsity_target + 1e-8)
        kl_div = kl_div + (1 - mean_activation) * torch.log((1 - mean_activation) / (1 - self.sparsity_target) + 1e-8)
        
        # Filter out NaN values
        kl_div = torch.where(torch.isnan(kl_div), torch.zeros_like(kl_div), kl_div)
        
        loss = self.weight * kl_div.mean()
        
        return loss


class AdaptiveLossWeighting(nn.Module):
    """
    Adaptive loss weighting using gradient normalization.
    
    Based on "Multi-Task Learning Using Uncertainty to Weigh Losses".
    """
    
    def __init__(self, n_losses: int, init_weights: Optional[torch.Tensor] = None):
        """
        Initialize adaptive weighting.
        
        Args:
            n_losses: Number of loss components
            init_weights: Initial weights (default: uniform)
        """
        super().__init__()
        
        if init_weights is None:
            init_weights = torch.ones(n_losses)
        
        # Log variance parameters (learnable)
        self.log_vars = nn.Parameter(torch.log(init_weights))
    
    def forward(self, losses: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        Compute weighted loss.
        
        Args:
            losses: Dictionary of loss components
            
        Returns:
            Total weighted loss and individual weights
        """
        total_loss = 0
        weights = {}
        
        for i, (name, loss) in enumerate(losses.items()):
            precision = torch.exp(-self.log_vars[i])
            weight = precision / 2
            
            weighted_loss = weight * loss + self.log_vars[i] / 2
            total_loss += weighted_loss
            
            weights[name] = weight.item()
        
        return total_loss, weights


class ScINTEGLoss(nn.Module):
    """
    Combined loss function for ScINTEG v3.
    
    Combines reconstruction, GRN regularization, and other losses.
    """
    
    def __init__(
        self,
        reconstruction_type: str = 'mse',
        # Loss weights
        reconstruction_weight: float = 1.0,
        grn_weight: float = 0.1,
        pathway_sparsity_weight: float = 0.01,
        # Adaptive weighting
        use_adaptive_weighting: bool = False,
        # Component configs
        grn_config: Optional[Dict[str, Any]] = None,
        pathway_config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize ScINTEG loss.
        
        Args:
            reconstruction_type: Type of reconstruction loss ('mse', 'nb', 'zinb')
            reconstruction_weight: Weight for reconstruction loss
            grn_weight: Weight for GRN regularization
            pathway_sparsity_weight: Weight for pathway sparsity
            use_adaptive_weighting: Whether to use adaptive weighting
            grn_config: Configuration for GRN regularization
            pathway_config: Configuration for pathway sparsity
        """
        super().__init__()
        
        # Reconstruction loss
        if reconstruction_type == 'mse':
            self.reconstruction_loss = MSELoss()
        elif reconstruction_type == 'nb':
            self.reconstruction_loss = NegativeBinomialLoss()
        elif reconstruction_type == 'zinb':
            self.reconstruction_loss = ZeroInflatedNegativeBinomialLoss()
        else:
            raise ValueError(f"Unknown reconstruction type: {reconstruction_type}")
        
        self.reconstruction_type = reconstruction_type
        
        # GRN regularization
        grn_config = grn_config or {}
        self.grn_regularization = GRNRegularizationLoss(**grn_config)
        
        # Pathway sparsity
        pathway_config = pathway_config or {}
        self.pathway_sparsity = PathwaySparsityLoss(**pathway_config)
        
        # Weights
        self.reconstruction_weight = reconstruction_weight
        self.grn_weight = grn_weight
        self.pathway_sparsity_weight = pathway_sparsity_weight
        
        # Adaptive weighting
        if use_adaptive_weighting:
            init_weights = torch.tensor([
                reconstruction_weight,
                grn_weight,
                pathway_sparsity_weight
            ])
            self.adaptive_weighting = AdaptiveLossWeighting(3, init_weights)
        else:
            self.adaptive_weighting = None
    
    def forward(
        self,
        outputs: Dict[str, torch.Tensor],
        targets: torch.Tensor,
        return_components: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, Dict[str, torch.Tensor]]]:
        """
        Compute combined loss.
        
        Args:
            outputs: Model outputs dictionary
            targets: Target expression values
            return_components: Whether to return individual components
            
        Returns:
            Total loss or (total_loss, components_dict)
        """
        losses = {}
        
        # Reconstruction loss
        if self.reconstruction_type == 'mse':
            recon_loss = self.reconstruction_loss(outputs['reconstruction'], targets)
        elif self.reconstruction_type == 'nb':
            recon_loss = self.reconstruction_loss(
                targets, outputs['reconstruction'], outputs['theta']
            )
        elif self.reconstruction_type == 'zinb':
            recon_loss = self.reconstruction_loss(
                targets, outputs['reconstruction'], 
                outputs['theta'], outputs['pi_logits']
            )
        
        losses['reconstruction'] = recon_loss
        
        # GRN regularization
        if 'grn_edge_weight' in outputs and outputs['grn_edge_weight'].numel() > 0:
            grn_loss = self.grn_regularization(
                outputs['grn_edge_weight'],
                outputs.get('grn_edge_index'),
                outputs.get('n_genes')
            )
            losses['grn_regularization'] = grn_loss
        else:
            losses['grn_regularization'] = torch.tensor(0.0, device=targets.device)
        
        # Pathway sparsity
        if 'pathway_features' in outputs:
            pathway_loss = self.pathway_sparsity(outputs['pathway_features'])
            losses['pathway_sparsity'] = pathway_loss
        else:
            losses['pathway_sparsity'] = torch.tensor(0.0, device=targets.device)
        
        # Combine losses
        if self.adaptive_weighting is not None:
            total_loss, weights = self.adaptive_weighting(losses)
            
            if return_components:
                # Add weights to output
                weighted_losses = {
                    f"{k}_weighted": v * weights[k] 
                    for k, v in losses.items()
                }
                weighted_losses.update({
                    f"{k}_weight": weights[k]
                    for k in losses.keys()
                })
                return total_loss, weighted_losses
        else:
            # Fixed weights
            total_loss = (
                self.reconstruction_weight * losses['reconstruction'] +
                self.grn_weight * losses['grn_regularization'] +
                self.pathway_sparsity_weight * losses['pathway_sparsity']
            )
        
        if return_components:
            return total_loss, losses
        else:
            return total_loss