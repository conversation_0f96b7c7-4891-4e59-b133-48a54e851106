"""
Trainer classes for ScINTEG v3.

This module provides training infrastructure including:
- Base trainer class with common functionality
- ScINTEG-specific trainer with advanced features
- Support for distributed training and mixed precision
"""

import torch
import torch.nn as nn
from torch.optim import Optimizer
from torch.optim.lr_scheduler import _LRScheduler
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, Optional, List, Tuple, Union, Any, Callable
from pathlib import Path
import time
import logging
from tqdm import tqdm
import json
from abc import ABC, abstractmethod

from ..data.augmentation import DataAugmenter, BatchAwareAugmenter

logger = logging.getLogger(__name__)


class BaseTrainer(ABC):
    """
    Abstract base trainer class with common functionality.
    """
    
    def __init__(
        self,
        model: nn.Module,
        # Optimization
        optimizer: Optimizer,
        scheduler: Optional[_LRScheduler] = None,
        # Device
        device: Union[str, torch.device] = 'cuda',
        # Paths
        checkpoint_dir: Optional[Union[str, Path]] = None,
        # Training options
        gradient_clip_val: Optional[float] = None,
        accumulate_grad_batches: int = 1,
        mixed_precision: bool = False,
        # Validation
        val_check_interval: Union[int, float] = 1.0,
        early_stopping_patience: Optional[int] = None,
        early_stopping_min_delta: float = 0.0,
        # Logging
        log_interval: int = 10,
        verbose: bool = True
    ):
        """
        Initialize base trainer.
        
        Args:
            model: Model to train
            optimizer: Optimizer
            scheduler: Learning rate scheduler
            device: Device to use
            checkpoint_dir: Directory for checkpoints
            gradient_clip_val: Gradient clipping value
            accumulate_grad_batches: Number of batches to accumulate gradients
            mixed_precision: Whether to use mixed precision training
            val_check_interval: Validation interval (epochs or fraction)
            early_stopping_patience: Patience for early stopping
            early_stopping_min_delta: Minimum change for early stopping
            log_interval: Logging interval (batches)
            verbose: Whether to print progress
        """
        self.model = model.to(device)
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.device = torch.device(device)
        
        # Paths
        if checkpoint_dir:
            self.checkpoint_dir = Path(checkpoint_dir)
            self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        else:
            self.checkpoint_dir = None
        
        # Training options
        self.gradient_clip_val = gradient_clip_val
        self.accumulate_grad_batches = accumulate_grad_batches
        self.mixed_precision = mixed_precision
        
        # Mixed precision setup
        if self.mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None
        
        # Validation
        self.val_check_interval = val_check_interval
        self.early_stopping_patience = early_stopping_patience
        self.early_stopping_min_delta = early_stopping_min_delta
        
        # Logging
        self.log_interval = log_interval
        self.verbose = verbose
        
        # State
        self.current_epoch = 0
        self.global_step = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'lr': []
        }
    
    @abstractmethod
    def training_step(
        self,
        batch: Dict[str, torch.Tensor],
        batch_idx: int
    ) -> torch.Tensor:
        """
        Single training step.
        
        Args:
            batch: Batch data
            batch_idx: Batch index
            
        Returns:
            Loss value
        """
        pass
    
    @abstractmethod
    def validation_step(
        self,
        batch: Dict[str, torch.Tensor],
        batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """
        Single validation step.
        
        Args:
            batch: Batch data
            batch_idx: Batch index
            
        Returns:
            Dictionary with validation metrics
        """
        pass
    
    def fit(
        self,
        train_loader: DataLoader,
        val_loader: Optional[DataLoader] = None,
        epochs: int = 100,
        callbacks: Optional[List[Callable]] = None
    ):
        """
        Main training loop.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            epochs: Number of epochs
            callbacks: List of callback functions
        """
        callbacks = callbacks or []
        
        for epoch in range(epochs):
            self.current_epoch = epoch
            
            # Training
            train_loss = self._train_epoch(train_loader)
            self.training_history['train_loss'].append(train_loss)
            
            # Validation
            if val_loader and self._should_validate():
                val_metrics = self._validate(val_loader)
                val_loss = val_metrics['loss']
                self.training_history['val_loss'].append(val_loss)
                
                # Early stopping
                if self._check_early_stopping(val_loss):
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
                
                # Checkpoint best model
                if val_loss < self.best_val_loss:
                    self.best_val_loss = val_loss
                    self._save_checkpoint('best')
            
            # Learning rate scheduling
            if self.scheduler:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_loss if val_loader else train_loss)
                else:
                    self.scheduler.step()
            
            # Record learning rate
            current_lr = self.optimizer.param_groups[0]['lr']
            self.training_history['lr'].append(current_lr)
            
            # Callbacks
            for callback in callbacks:
                callback(self, epoch)
            
            # Save regular checkpoint
            if self.checkpoint_dir and epoch % 10 == 0:
                self._save_checkpoint(f'epoch_{epoch}')
        
        # Save final model
        if self.checkpoint_dir:
            self._save_checkpoint('final')
    
    def _train_epoch(self, train_loader: DataLoader) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        n_batches = len(train_loader)
        
        if self.verbose:
            pbar = tqdm(train_loader, desc=f'Epoch {self.current_epoch}')
        else:
            pbar = train_loader
        
        for batch_idx, batch in enumerate(pbar):
            # Move batch to device
            batch = self._batch_to_device(batch)
            
            # Forward pass with mixed precision
            if self.mixed_precision:
                with torch.cuda.amp.autocast():
                    loss = self.training_step(batch, batch_idx)
            else:
                loss = self.training_step(batch, batch_idx)
            
            # Scale loss for gradient accumulation
            loss = loss / self.accumulate_grad_batches
            
            # Backward pass
            if self.mixed_precision:
                self.scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # Gradient accumulation
            if (batch_idx + 1) % self.accumulate_grad_batches == 0:
                # Gradient clipping
                if self.gradient_clip_val:
                    if self.mixed_precision:
                        self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(),
                        self.gradient_clip_val
                    )
                
                # Optimizer step
                if self.mixed_precision:
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    self.optimizer.step()
                
                self.optimizer.zero_grad()
            
            # Update metrics
            total_loss += loss.item() * self.accumulate_grad_batches
            self.global_step += 1
            
            # Logging
            if self.verbose and batch_idx % self.log_interval == 0:
                avg_loss = total_loss / (batch_idx + 1)
                current_lr = self.optimizer.param_groups[0]['lr']
                pbar.set_postfix({
                    'loss': f'{avg_loss:.4f}',
                    'lr': f'{current_lr:.2e}'
                })
        
        return total_loss / n_batches
    
    def _validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """Validate the model."""
        self.model.eval()
        total_metrics = {}
        n_batches = len(val_loader)
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                batch = self._batch_to_device(batch)
                
                # Forward pass
                metrics = self.validation_step(batch, batch_idx)
                
                # Accumulate metrics
                for key, value in metrics.items():
                    if key not in total_metrics:
                        total_metrics[key] = 0.0
                    total_metrics[key] += value.item()
        
        # Average metrics
        avg_metrics = {k: v / n_batches for k, v in total_metrics.items()}
        
        if self.verbose:
            metric_str = ', '.join([f'{k}: {v:.4f}' for k, v in avg_metrics.items()])
            logger.info(f"Validation - {metric_str}")
        
        return avg_metrics
    
    def _should_validate(self) -> bool:
        """Check if validation should be performed."""
        if isinstance(self.val_check_interval, float):
            # Fraction of epoch
            return np.random.random() < self.val_check_interval
        else:
            # Every n epochs
            return (self.current_epoch + 1) % self.val_check_interval == 0
    
    def _check_early_stopping(self, val_loss: float) -> bool:
        """Check early stopping criteria."""
        if self.early_stopping_patience is None:
            return False
        
        if val_loss < self.best_val_loss - self.early_stopping_min_delta:
            self.patience_counter = 0
        else:
            self.patience_counter += 1
        
        return self.patience_counter >= self.early_stopping_patience
    
    def _batch_to_device(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Move batch to device."""
        return {
            k: v.to(self.device) if isinstance(v, torch.Tensor) else v
            for k, v in batch.items()
        }
    
    def _save_checkpoint(self, name: str):
        """Save model checkpoint."""
        if not self.checkpoint_dir:
            return
        
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history,
            'config': self._get_config()
        }
        
        path = self.checkpoint_dir / f'{name}.pt'
        torch.save(checkpoint, path)
        logger.info(f"Saved checkpoint to {path}")
    
    def load_checkpoint(self, path: Union[str, Path]):
        """Load model checkpoint."""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_val_loss = checkpoint['best_val_loss']
        self.training_history = checkpoint['training_history']
        
        logger.info(f"Loaded checkpoint from {path}")
    
    def _get_config(self) -> Dict[str, Any]:
        """Get trainer configuration."""
        return {
            'gradient_clip_val': self.gradient_clip_val,
            'accumulate_grad_batches': self.accumulate_grad_batches,
            'mixed_precision': self.mixed_precision,
            'val_check_interval': self.val_check_interval,
            'early_stopping_patience': self.early_stopping_patience,
            'early_stopping_min_delta': self.early_stopping_min_delta
        }


class ScINTEGTrainer(BaseTrainer):
    """
    Trainer specifically for ScINTEG v3 models.
    """
    
    def __init__(
        self,
        model: nn.Module,
        optimizer: Optimizer,
        # Loss configuration
        loss_weights: Optional[Dict[str, float]] = None,
        # Additional options
        compute_grn_metrics: bool = True,
        pathway_analysis_interval: int = 10,
        # Data augmentation
        augmenter: Optional[Union[DataAugmenter, BatchAwareAugmenter]] = None,
        augment_train: bool = True,
        **kwargs
    ):
        """
        Initialize ScINTEG trainer.
        
        Args:
            model: ScINTEG model
            optimizer: Optimizer
            loss_weights: Weights for different loss components
            compute_grn_metrics: Whether to compute GRN metrics
            pathway_analysis_interval: Interval for pathway analysis
            augmenter: Data augmenter for training
            augment_train: Whether to augment training data
            **kwargs: Additional arguments for BaseTrainer
        """
        super().__init__(model, optimizer, **kwargs)
        
        self.loss_weights = loss_weights or {}
        self.compute_grn_metrics = compute_grn_metrics
        self.pathway_analysis_interval = pathway_analysis_interval
        self.augmenter = augmenter
        self.augment_train = augment_train
        
        # Additional metrics
        self.training_history.update({
            'reconstruction_loss': [],
            'grn_loss': [],
            'pathway_loss': [],
            'grn_sparsity': [],
            'pathway_sparsity': []
        })
    
    def training_step(
        self,
        batch: Dict[str, torch.Tensor],
        batch_idx: int
    ) -> torch.Tensor:
        """ScINTEG training step."""
        # Apply data augmentation if enabled
        if self.augment_train and self.augmenter is not None:
            batch = self.augmenter(batch)
        
        # Forward pass
        outputs = self.model(
            batch['expression'],
            batch['cell_graph'],
            batch['gene_graph'],
            return_intermediates=True
        )
        
        # Compute loss
        loss, loss_components = self.model.compute_loss(
            outputs,
            batch['expression'],
            return_components=True
        )
        
        # Log components
        if batch_idx % self.log_interval == 0:
            for key, value in loss_components.items():
                if key in self.training_history:
                    self.training_history[key].append(value.item())
        
        return loss
    
    def validation_step(
        self,
        batch: Dict[str, torch.Tensor],
        batch_idx: int
    ) -> Dict[str, torch.Tensor]:
        """ScINTEG validation step."""
        # Forward pass
        outputs = self.model(
            batch['expression'],
            batch['cell_graph'],
            batch['gene_graph'],
            return_intermediates=True
        )
        
        # Compute loss
        loss, loss_components = self.model.compute_loss(
            outputs,
            batch['expression'],
            return_components=True
        )
        
        # Compute additional metrics
        metrics = {'loss': loss}
        
        # Reconstruction metrics
        mse = torch.nn.functional.mse_loss(
            outputs['reconstruction'],
            batch['expression']
        )
        metrics['reconstruction_mse'] = mse
        
        # Correlation
        pearson_r = self._compute_correlation(
            outputs['reconstruction'],
            batch['expression']
        )
        metrics['reconstruction_corr'] = pearson_r
        
        # GRN metrics
        if self.compute_grn_metrics and outputs['grn_edge_weight'].numel() > 0:
            grn_sparsity = (outputs['grn_edge_weight'] > 0.01).float().mean()
            metrics['grn_sparsity'] = grn_sparsity
        
        # Pathway metrics
        if 'pathway_features' in outputs:
            pathway_sparsity = (outputs['pathway_features'].abs() > 0.1).float().mean()
            metrics['pathway_sparsity'] = pathway_sparsity
        
        return metrics
    
    def _compute_correlation(
        self,
        pred: torch.Tensor,
        target: torch.Tensor
    ) -> torch.Tensor:
        """Compute average Pearson correlation."""
        batch_size = pred.shape[0]
        correlations = []
        
        for i in range(batch_size):
            # Compute correlation for each cell
            pred_i = pred[i] - pred[i].mean()
            target_i = target[i] - target[i].mean()
            
            corr = torch.sum(pred_i * target_i) / (
                torch.sqrt(torch.sum(pred_i ** 2)) *
                torch.sqrt(torch.sum(target_i ** 2)) + 1e-8
            )
            correlations.append(corr)
        
        return torch.stack(correlations).mean()
    
    def analyze_pathways(self, val_loader: DataLoader) -> Dict[str, List[str]]:
        """Analyze pathway activations."""
        self.model.eval()
        all_pathway_features = []
        
        with torch.no_grad():
            for batch in val_loader:
                batch = self._batch_to_device(batch)
                outputs = self.model(
                    batch['expression'],
                    batch['cell_graph'],
                    batch['gene_graph'],
                    return_intermediates=True
                )
                
                if 'pathway_features' in outputs:
                    all_pathway_features.append(outputs['pathway_features'])
        
        if all_pathway_features:
            # Concatenate all features
            pathway_features = torch.cat(all_pathway_features, dim=0)
            
            # Compute statistics
            mean_activation = pathway_features.mean(dim=0)
            std_activation = pathway_features.std(dim=0)
            
            # Get top activated pathways
            top_k = 10
            top_indices = torch.topk(mean_activation, top_k).indices
            
            return {
                'top_pathways': top_indices.cpu().tolist(),
                'mean_activation': mean_activation.cpu().tolist(),
                'std_activation': std_activation.cpu().tolist()
            }
        
        return {}