"""
Learning rate schedulers for ScINTEG v3.

This module provides various learning rate scheduling strategies optimized for
single-cell integration training.
"""

import torch
import torch.optim as optim
from torch.optim.lr_scheduler import _LRScheduler
import numpy as np
import math
from typing import Dict, List, Optional, Union, Any
import warnings
import logging

logger = logging.getLogger(__name__)


class WarmupScheduler(_LRScheduler):
    """
    Learning rate scheduler with warmup period.
    
    Gradually increases learning rate from 0 to base_lr during warmup,
    then applies the main scheduler.
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        warmup_steps: int,
        main_scheduler: Optional[_LRScheduler] = None,
        warmup_factor: float = 0.1,
        last_epoch: int = -1
    ):
        """
        Initialize warmup scheduler.
        
        Args:
            optimizer: PyTorch optimizer
            warmup_steps: Number of warmup steps
            main_scheduler: Main scheduler to use after warmup
            warmup_factor: Initial warmup factor (lr = base_lr * warmup_factor)
            last_epoch: Last epoch index
        """
        self.warmup_steps = warmup_steps
        self.main_scheduler = main_scheduler
        self.warmup_factor = warmup_factor
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        if self.last_epoch < self.warmup_steps:
            # Warmup phase
            warmup_progress = self.last_epoch / self.warmup_steps
            warmup_lr = [
                base_lr * (self.warmup_factor + (1 - self.warmup_factor) * warmup_progress)
                for base_lr in self.base_lrs
            ]
            return warmup_lr
        else:
            # Main scheduler phase
            if self.main_scheduler is not None:
                return self.main_scheduler.get_lr()
            else:
                return self.base_lrs
    
    def step(self, epoch=None):
        super().step(epoch)
        if self.main_scheduler is not None and self.last_epoch >= self.warmup_steps:
            # Update main scheduler
            main_epoch = self.last_epoch - self.warmup_steps
            self.main_scheduler.last_epoch = main_epoch
            if epoch is None:
                self.main_scheduler.step()
            else:
                self.main_scheduler.step(epoch - self.warmup_steps)


class CosineAnnealingWarmRestarts(_LRScheduler):
    """
    Cosine annealing with warm restarts (SGDR).
    
    Implements the scheduler from "SGDR: Stochastic Gradient Descent with Warm Restarts"
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        T_0: int,
        T_mult: int = 1,
        eta_min: float = 0,
        last_epoch: int = -1
    ):
        """
        Initialize cosine annealing with warm restarts.
        
        Args:
            optimizer: PyTorch optimizer
            T_0: Number of iterations for the first restart
            T_mult: Factor for increasing restart intervals
            eta_min: Minimum learning rate
            last_epoch: Last epoch index
        """
        self.T_0 = T_0
        self.T_mult = T_mult
        self.eta_min = eta_min
        self.T_cur = 0
        self.T_i = T_0
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        return [
            self.eta_min + (base_lr - self.eta_min) *
            (1 + math.cos(math.pi * self.T_cur / self.T_i)) / 2
            for base_lr in self.base_lrs
        ]
    
    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
        self.last_epoch = epoch
        
        self.T_cur += 1
        if self.T_cur >= self.T_i:
            # Restart
            self.T_cur = 0
            self.T_i *= self.T_mult
        
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr


class OneCycleLR(_LRScheduler):
    """
    One cycle learning rate policy.
    
    Implements the 1cycle policy from "Super-Convergence: Very Fast Training of Neural Networks Using Large Learning Rates"
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        max_lr: Union[float, List[float]],
        total_steps: int,
        pct_start: float = 0.3,
        anneal_strategy: str = 'cos',
        div_factor: float = 25.0,
        final_div_factor: float = 1e4,
        last_epoch: int = -1
    ):
        """
        Initialize one cycle LR scheduler.
        
        Args:
            optimizer: PyTorch optimizer
            max_lr: Maximum learning rate(s)
            total_steps: Total number of steps
            pct_start: Percentage of cycle spent increasing lr
            anneal_strategy: Annealing strategy ('cos' or 'linear')
            div_factor: Initial lr = max_lr / div_factor
            final_div_factor: Final lr = initial_lr / final_div_factor
            last_epoch: Last epoch index
        """
        if isinstance(max_lr, (int, float)):
            self.max_lrs = [max_lr] * len(optimizer.param_groups)
        else:
            self.max_lrs = max_lr
        
        self.total_steps = total_steps
        self.pct_start = pct_start
        self.anneal_strategy = anneal_strategy
        self.div_factor = div_factor
        self.final_div_factor = final_div_factor
        
        # Calculate initial and final learning rates
        self.initial_lrs = [max_lr / div_factor for max_lr in self.max_lrs]
        self.final_lrs = [initial_lr / final_div_factor for initial_lr in self.initial_lrs]
        
        # Calculate step boundaries
        self.step_up = int(total_steps * pct_start)
        self.step_down = total_steps - self.step_up
        
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        if self.last_epoch <= self.step_up:
            # Increasing phase
            pct = self.last_epoch / self.step_up
            if self.anneal_strategy == 'cos':
                factor = (1 - math.cos(math.pi * pct)) / 2
            else:  # linear
                factor = pct
            
            return [
                initial_lr + (max_lr - initial_lr) * factor
                for initial_lr, max_lr in zip(self.initial_lrs, self.max_lrs)
            ]
        else:
            # Decreasing phase
            pct = (self.last_epoch - self.step_up) / self.step_down
            if self.anneal_strategy == 'cos':
                factor = (1 + math.cos(math.pi * pct)) / 2
            else:  # linear
                factor = 1 - pct
            
            return [
                final_lr + (max_lr - final_lr) * factor
                for final_lr, max_lr in zip(self.final_lrs, self.max_lrs)
            ]


class PolynomialLR(_LRScheduler):
    """
    Polynomial learning rate decay.
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        total_steps: int,
        power: float = 0.9,
        min_lr: float = 0,
        last_epoch: int = -1
    ):
        """
        Initialize polynomial LR scheduler.
        
        Args:
            optimizer: PyTorch optimizer
            total_steps: Total number of steps
            power: Polynomial power
            min_lr: Minimum learning rate
            last_epoch: Last epoch index
        """
        self.total_steps = total_steps
        self.power = power
        self.min_lr = min_lr
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self):
        factor = (1 - self.last_epoch / self.total_steps) ** self.power
        return [
            self.min_lr + (base_lr - self.min_lr) * factor
            for base_lr in self.base_lrs
        ]


class AdaptiveLR:
    """
    Adaptive learning rate scheduler that adjusts based on training metrics.
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        patience: int = 10,
        factor: float = 0.5,
        min_lr: float = 1e-8,
        threshold: float = 1e-4,
        threshold_mode: str = 'rel',
        cooldown: int = 0,
        verbose: bool = False
    ):
        """
        Initialize adaptive LR scheduler.
        
        Args:
            optimizer: PyTorch optimizer
            patience: Number of epochs to wait before reducing lr
            factor: Factor by which to reduce lr
            min_lr: Minimum learning rate
            threshold: Threshold for measuring improvement
            threshold_mode: 'rel' or 'abs'
            cooldown: Number of epochs to wait after lr reduction
            verbose: Print lr changes
        """
        self.optimizer = optimizer
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.threshold = threshold
        self.threshold_mode = threshold_mode
        self.cooldown = cooldown
        self.verbose = verbose
        
        self.best_metric = None
        self.num_bad_epochs = 0
        self.cooldown_counter = 0
        self.last_epoch = 0
    
    def step(self, metric: float):
        """Step the scheduler with a metric value."""
        current_lr = self.optimizer.param_groups[0]['lr']
        
        if self.cooldown_counter > 0:
            self.cooldown_counter -= 1
            self.last_epoch += 1
            return
        
        if self.best_metric is None:
            self.best_metric = metric
        elif self._is_better(metric, self.best_metric):
            self.best_metric = metric
            self.num_bad_epochs = 0
        else:
            self.num_bad_epochs += 1
        
        if self.num_bad_epochs >= self.patience:
            if current_lr > self.min_lr:
                new_lr = max(current_lr * self.factor, self.min_lr)
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] = new_lr
                
                if self.verbose:
                    logger.info(f"Reducing learning rate to {new_lr:.2e}")
                
                self.cooldown_counter = self.cooldown
                self.num_bad_epochs = 0
        
        self.last_epoch += 1
    
    def _is_better(self, current: float, best: float) -> bool:
        """Check if current metric is better than best."""
        if self.threshold_mode == 'rel':
            rel_epsilon = 1. - self.threshold
            return current < best * rel_epsilon
        else:  # abs
            return current < best - self.threshold


def create_scheduler(
    optimizer: torch.optim.Optimizer,
    scheduler_type: str,
    scheduler_params: Dict[str, Any],
    total_steps: Optional[int] = None
) -> Union[_LRScheduler, AdaptiveLR]:
    """
    Factory function to create learning rate schedulers.
    
    Args:
        optimizer: PyTorch optimizer
        scheduler_type: Type of scheduler
        scheduler_params: Scheduler parameters
        total_steps: Total training steps (required for some schedulers)
        
    Returns:
        Learning rate scheduler
    """
    scheduler_type = scheduler_type.lower()
    
    if scheduler_type == 'step':
        return optim.lr_scheduler.StepLR(optimizer, **scheduler_params)
    
    elif scheduler_type == 'multistep':
        return optim.lr_scheduler.MultiStepLR(optimizer, **scheduler_params)
    
    elif scheduler_type == 'exponential':
        return optim.lr_scheduler.ExponentialLR(optimizer, **scheduler_params)
    
    elif scheduler_type == 'cosine':
        return optim.lr_scheduler.CosineAnnealingLR(optimizer, **scheduler_params)
    
    elif scheduler_type == 'cosine_restarts':
        return CosineAnnealingWarmRestarts(optimizer, **scheduler_params)
    
    elif scheduler_type == 'plateau':
        return optim.lr_scheduler.ReduceLROnPlateau(optimizer, **scheduler_params)
    
    elif scheduler_type == 'onecycle':
        if total_steps is None:
            raise ValueError("total_steps must be provided for OneCycleLR")
        return OneCycleLR(optimizer, total_steps=total_steps, **scheduler_params)
    
    elif scheduler_type == 'polynomial':
        if total_steps is None:
            raise ValueError("total_steps must be provided for PolynomialLR")
        return PolynomialLR(optimizer, total_steps=total_steps, **scheduler_params)
    
    elif scheduler_type == 'warmup':
        main_scheduler = None
        if 'main_scheduler' in scheduler_params:
            main_scheduler_config = scheduler_params.pop('main_scheduler')
            main_scheduler = create_scheduler(
                optimizer, 
                main_scheduler_config['type'],
                main_scheduler_config['params'],
                total_steps
            )
        return WarmupScheduler(optimizer, main_scheduler=main_scheduler, **scheduler_params)
    
    elif scheduler_type == 'adaptive':
        return AdaptiveLR(optimizer, **scheduler_params)
    
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")


def get_lr_schedule_info(scheduler: Union[_LRScheduler, AdaptiveLR]) -> Dict[str, Any]:
    """
    Get information about the learning rate schedule.
    
    Args:
        scheduler: Learning rate scheduler
        
    Returns:
        Dictionary with schedule information
    """
    info = {
        'type': scheduler.__class__.__name__,
        'current_lr': None,
        'state': {}
    }
    
    if hasattr(scheduler, 'optimizer'):
        info['current_lr'] = [group['lr'] for group in scheduler.optimizer.param_groups]
    
    # Add scheduler-specific information
    if isinstance(scheduler, WarmupScheduler):
        info['state'] = {
            'warmup_steps': scheduler.warmup_steps,
            'in_warmup': scheduler.last_epoch < scheduler.warmup_steps,
            'warmup_progress': min(scheduler.last_epoch / scheduler.warmup_steps, 1.0)
        }
    
    elif isinstance(scheduler, CosineAnnealingWarmRestarts):
        info['state'] = {
            'T_0': scheduler.T_0,
            'T_mult': scheduler.T_mult,
            'T_cur': scheduler.T_cur,
            'T_i': scheduler.T_i,
            'eta_min': scheduler.eta_min
        }
    
    elif isinstance(scheduler, OneCycleLR):
        info['state'] = {
            'total_steps': scheduler.total_steps,
            'step_up': scheduler.step_up,
            'step_down': scheduler.step_down,
            'pct_start': scheduler.pct_start,
            'max_lrs': scheduler.max_lrs,
            'phase': 'increasing' if scheduler.last_epoch <= scheduler.step_up else 'decreasing'
        }
    
    elif isinstance(scheduler, AdaptiveLR):
        info['state'] = {
            'patience': scheduler.patience,
            'factor': scheduler.factor,
            'min_lr': scheduler.min_lr,
            'best_metric': scheduler.best_metric,
            'num_bad_epochs': scheduler.num_bad_epochs,
            'cooldown_counter': scheduler.cooldown_counter
        }
    
    return info