"""
Evaluation metrics for ScINTEG v3.

This module provides comprehensive evaluation metrics for single-cell integration tasks:
- Cell type classification metrics
- Batch correction metrics  
- Integration quality metrics
- Clustering metrics
- Biological conservation metrics
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.metrics import (
    accuracy_score, f1_score, precision_score, recall_score,
    adjusted_rand_score, normalized_mutual_info_score,
    silhouette_score, calinski_harabasz_score, davies_bouldin_score
)
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
from sklearn.preprocessing import LabelEncoder
import warnings
import logging

logger = logging.getLogger(__name__)


class IntegrationMetrics:
    """
    Comprehensive metrics for evaluating single-cell data integration.
    
    This class provides methods to compute various metrics that assess:
    1. Batch correction quality
    2. Cell type classification performance
    3. Integration quality
    4. Clustering performance
    5. Biological conservation
    """
    
    def __init__(self, k_neighbors: int = 15):
        """
        Initialize metrics calculator.
        
        Args:
            k_neighbors: Number of neighbors for neighborhood-based metrics
        """
        self.k_neighbors = k_neighbors
        self.label_encoder = LabelEncoder()
    
    def compute_all_metrics(
        self,
        embeddings: Union[torch.Tensor, np.ndarray],
        batch_labels: Union[List, np.ndarray],
        cell_type_labels: Optional[Union[List, np.ndarray]] = None,
        cluster_labels: Optional[Union[List, np.ndarray]] = None,
        original_embeddings: Optional[Union[torch.Tensor, np.ndarray]] = None
    ) -> Dict[str, float]:
        """
        Compute all available metrics.
        
        Args:
            embeddings: Integrated embeddings [n_cells, n_dims]
            batch_labels: Batch labels for each cell
            cell_type_labels: Cell type labels (if available)
            cluster_labels: Cluster assignments (if available)
            original_embeddings: Original embeddings before integration
            
        Returns:
            Dictionary of computed metrics
        """
        metrics = {}
        
        # Convert to numpy if needed
        if isinstance(embeddings, torch.Tensor):
            embeddings = embeddings.cpu().numpy()
        if original_embeddings is not None and isinstance(original_embeddings, torch.Tensor):
            original_embeddings = original_embeddings.cpu().numpy()
        
        batch_labels = np.array(batch_labels)
        
        # 1. Batch correction metrics
        logger.info("Computing batch correction metrics...")
        batch_metrics = self.compute_batch_correction_metrics(embeddings, batch_labels)
        metrics.update(batch_metrics)
        
        # 2. Cell type classification metrics
        if cell_type_labels is not None:
            logger.info("Computing cell type classification metrics...")
            cell_type_labels = np.array(cell_type_labels)
            celltype_metrics = self.compute_celltype_metrics(
                embeddings, cell_type_labels, batch_labels
            )
            metrics.update(celltype_metrics)
        
        # 3. Clustering metrics
        if cluster_labels is not None:
            logger.info("Computing clustering metrics...")
            cluster_labels = np.array(cluster_labels)
            clustering_metrics = self.compute_clustering_metrics(
                embeddings, cluster_labels, cell_type_labels
            )
            metrics.update(clustering_metrics)
        
        # 4. Integration quality metrics
        logger.info("Computing integration quality metrics...")
        integration_metrics = self.compute_integration_metrics(
            embeddings, batch_labels, cell_type_labels
        )
        metrics.update(integration_metrics)
        
        # 5. Biological conservation metrics
        if original_embeddings is not None:
            logger.info("Computing biological conservation metrics...")
            conservation_metrics = self.compute_conservation_metrics(
                original_embeddings, embeddings, cell_type_labels
            )
            metrics.update(conservation_metrics)
        
        return metrics
    
    def compute_batch_correction_metrics(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray
    ) -> Dict[str, float]:
        """Compute metrics for batch correction quality."""
        metrics = {}
        
        try:
            # 1. Batch mixing entropy
            batch_mixing = self._compute_batch_mixing_entropy(embeddings, batch_labels)
            metrics['batch_mixing_entropy'] = batch_mixing
            
            # 2. Silhouette score for batches (lower is better for integration)
            if len(np.unique(batch_labels)) > 1:
                sil_batch = silhouette_score(embeddings, batch_labels)
                metrics['batch_silhouette'] = sil_batch
                metrics['batch_correction_score'] = 1 - abs(sil_batch)  # Inverted for integration
            
            # 3. kBET (k-nearest neighbor Batch Effect Test) approximation
            kbet_score = self._compute_kbet_score(embeddings, batch_labels)
            metrics['kbet_score'] = kbet_score
            
            # 4. LISI (Local Inverse Simpson's Index) for batches
            lisi_batch = self._compute_lisi(embeddings, batch_labels, 'batch')
            metrics['lisi_batch'] = lisi_batch
            
        except Exception as e:
            logger.warning(f"Error computing batch correction metrics: {e}")
            metrics.update({
                'batch_mixing_entropy': 0.0,
                'batch_silhouette': 0.0,
                'batch_correction_score': 0.0,
                'kbet_score': 0.0,
                'lisi_batch': 1.0
            })
        
        return metrics
    
    def compute_celltype_metrics(
        self,
        embeddings: np.ndarray,
        cell_type_labels: np.ndarray,
        batch_labels: np.ndarray
    ) -> Dict[str, float]:
        """Compute cell type classification and separation metrics."""
        metrics = {}
        
        try:
            # 1. Cell type silhouette score
            if len(np.unique(cell_type_labels)) > 1:
                sil_celltype = silhouette_score(embeddings, cell_type_labels)
                metrics['celltype_silhouette'] = sil_celltype
            
            # 2. Cell type LISI
            lisi_celltype = self._compute_lisi(embeddings, cell_type_labels, 'celltype')
            metrics['lisi_celltype'] = lisi_celltype
            
            # 3. Neighborhood purity
            neighborhood_purity = self._compute_neighborhood_purity(embeddings, cell_type_labels)
            metrics['neighborhood_purity'] = neighborhood_purity
            
            # 4. Cross-batch cell type consistency
            cross_batch_consistency = self._compute_cross_batch_celltype_consistency(
                embeddings, cell_type_labels, batch_labels
            )
            metrics['cross_batch_celltype_consistency'] = cross_batch_consistency
            
        except Exception as e:
            logger.warning(f"Error computing cell type metrics: {e}")
            metrics.update({
                'celltype_silhouette': 0.0,
                'lisi_celltype': 1.0,
                'neighborhood_purity': 0.0,
                'cross_batch_celltype_consistency': 0.0
            })
        
        return metrics
    
    def compute_clustering_metrics(
        self,
        embeddings: np.ndarray,
        cluster_labels: np.ndarray,
        cell_type_labels: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """Compute clustering quality metrics."""
        metrics = {}
        
        try:
            # 1. Internal clustering metrics
            if len(np.unique(cluster_labels)) > 1:
                # Silhouette score
                sil_cluster = silhouette_score(embeddings, cluster_labels)
                metrics['cluster_silhouette'] = sil_cluster
                
                # Calinski-Harabasz index
                ch_score = calinski_harabasz_score(embeddings, cluster_labels)
                metrics['calinski_harabasz'] = ch_score
                
                # Davies-Bouldin index (lower is better)
                db_score = davies_bouldin_score(embeddings, cluster_labels)
                metrics['davies_bouldin'] = db_score
            
            # 2. External clustering metrics (if true labels available)
            if cell_type_labels is not None:
                # Adjusted Rand Index
                ari = adjusted_rand_score(cell_type_labels, cluster_labels)
                metrics['adjusted_rand_index'] = ari
                
                # Normalized Mutual Information
                nmi = normalized_mutual_info_score(cell_type_labels, cluster_labels)
                metrics['normalized_mutual_info'] = nmi
                
                # Homogeneity, completeness, and V-measure
                from sklearn.metrics import homogeneity_completeness_v_measure
                homogeneity, completeness, v_measure = homogeneity_completeness_v_measure(
                    cell_type_labels, cluster_labels
                )
                metrics['homogeneity'] = homogeneity
                metrics['completeness'] = completeness
                metrics['v_measure'] = v_measure
                
        except Exception as e:
            logger.warning(f"Error computing clustering metrics: {e}")
            base_metrics = {
                'cluster_silhouette': 0.0,
                'calinski_harabasz': 0.0,
                'davies_bouldin': float('inf')
            }
            if cell_type_labels is not None:
                base_metrics.update({
                    'adjusted_rand_index': 0.0,
                    'normalized_mutual_info': 0.0,
                    'homogeneity': 0.0,
                    'completeness': 0.0,
                    'v_measure': 0.0
                })
            metrics.update(base_metrics)
        
        return metrics
    
    def compute_integration_metrics(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray,
        cell_type_labels: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """Compute overall integration quality metrics."""
        metrics = {}
        
        try:
            # 1. Integration score (combines batch mixing and cell type separation)
            batch_mixing = self._compute_batch_mixing_entropy(embeddings, batch_labels)
            
            if cell_type_labels is not None:
                celltype_separation = self._compute_celltype_separation(embeddings, cell_type_labels)
                integration_score = (batch_mixing + celltype_separation) / 2
                metrics['celltype_separation'] = celltype_separation
            else:
                integration_score = batch_mixing
            
            metrics['integration_score'] = integration_score
            
            # 2. Graph connectivity (assess if integration preserves local structure)
            graph_connectivity = self._compute_graph_connectivity(embeddings, batch_labels)
            metrics['graph_connectivity'] = graph_connectivity
            
            # 3. Mixing metric based on k-nearest neighbors
            mixing_metric = self._compute_mixing_metric(embeddings, batch_labels)
            metrics['mixing_metric'] = mixing_metric
            
        except Exception as e:
            logger.warning(f"Error computing integration metrics: {e}")
            metrics.update({
                'integration_score': 0.0,
                'graph_connectivity': 0.0,
                'mixing_metric': 0.0
            })
            if cell_type_labels is not None:
                metrics['celltype_separation'] = 0.0
        
        return metrics
    
    def compute_conservation_metrics(
        self,
        original_embeddings: np.ndarray,
        integrated_embeddings: np.ndarray,
        cell_type_labels: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """Compute biological conservation metrics."""
        metrics = {}
        
        try:
            # 1. Neighborhood preservation
            neighborhood_preservation = self._compute_neighborhood_preservation(
                original_embeddings, integrated_embeddings
            )
            metrics['neighborhood_preservation'] = neighborhood_preservation
            
            # 2. Rank correlation of distances
            distance_correlation = self._compute_distance_correlation(
                original_embeddings, integrated_embeddings
            )
            metrics['distance_correlation'] = distance_correlation
            
            # 3. Procrustes analysis
            procrustes_distance = self._compute_procrustes_distance(
                original_embeddings, integrated_embeddings
            )
            metrics['procrustes_distance'] = procrustes_distance
            
            # 4. Cell type preservation
            if cell_type_labels is not None:
                celltype_preservation = self._compute_celltype_preservation(
                    original_embeddings, integrated_embeddings, cell_type_labels
                )
                metrics['celltype_preservation'] = celltype_preservation
            
        except Exception as e:
            logger.warning(f"Error computing conservation metrics: {e}")
            base_metrics = {
                'neighborhood_preservation': 0.0,
                'distance_correlation': 0.0,
                'procrustes_distance': 1.0
            }
            if cell_type_labels is not None:
                base_metrics['celltype_preservation'] = 0.0
            metrics.update(base_metrics)
        
        return metrics
    
    def _compute_batch_mixing_entropy(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray
    ) -> float:
        """Compute batch mixing entropy based on k-nearest neighbors."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            mixing_scores = []
            for i, neighbors in enumerate(indices):
                neighbor_batches = batch_labels[neighbors[1:]]  # Exclude self
                batch_counts = np.bincount(neighbor_batches)
                batch_probs = batch_counts / len(neighbor_batches)
                # Compute entropy
                entropy = -np.sum(batch_probs * np.log(batch_probs + 1e-8))
                mixing_scores.append(entropy)
            
            return np.mean(mixing_scores)
        except:
            return 0.0
    
    def _compute_kbet_score(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray
    ) -> float:
        """Compute k-nearest neighbor Batch Effect Test score."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            # Expected batch distribution
            batch_counts = np.bincount(batch_labels)
            expected_probs = batch_counts / len(batch_labels)
            
            kbet_scores = []
            for i, neighbors in enumerate(indices):
                neighbor_batches = batch_labels[neighbors[1:]]  # Exclude self
                observed_counts = np.bincount(neighbor_batches, minlength=len(expected_probs))
                observed_probs = observed_counts / len(neighbor_batches)
                
                # Chi-square test statistic
                chi2 = np.sum((observed_counts - len(neighbor_batches) * expected_probs) ** 2 /
                             (len(neighbor_batches) * expected_probs + 1e-8))
                kbet_scores.append(chi2)
            
            # Lower scores indicate better mixing
            return 1.0 / (1.0 + np.mean(kbet_scores))
        except:
            return 0.0
    
    def _compute_lisi(
        self,
        embeddings: np.ndarray,
        labels: np.ndarray,
        label_type: str
    ) -> float:
        """Compute Local Inverse Simpson's Index."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            lisi_scores = []
            for i, neighbors in enumerate(indices):
                neighbor_labels = labels[neighbors[1:]]  # Exclude self
                label_counts = np.bincount(neighbor_labels)
                label_probs = label_counts / len(neighbor_labels)
                
                # Simpson's index
                simpson = np.sum(label_probs ** 2)
                # Inverse Simpson's index
                lisi = 1.0 / simpson if simpson > 0 else 1.0
                lisi_scores.append(lisi)
            
            return np.mean(lisi_scores)
        except:
            return 1.0
    
    def _compute_neighborhood_purity(
        self,
        embeddings: np.ndarray,
        cell_type_labels: np.ndarray
    ) -> float:
        """Compute neighborhood purity for cell types."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            purity_scores = []
            for i, neighbors in enumerate(indices):
                true_label = cell_type_labels[i]
                neighbor_labels = cell_type_labels[neighbors[1:]]  # Exclude self
                purity = np.mean(neighbor_labels == true_label)
                purity_scores.append(purity)
            
            return np.mean(purity_scores)
        except:
            return 0.0
    
    def _compute_cross_batch_celltype_consistency(
        self,
        embeddings: np.ndarray,
        cell_type_labels: np.ndarray,
        batch_labels: np.ndarray
    ) -> float:
        """Compute consistency of cell type neighborhoods across batches."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            consistency_scores = []
            for i, neighbors in enumerate(indices):
                true_celltype = cell_type_labels[i]
                neighbor_celltypes = cell_type_labels[neighbors[1:]]
                neighbor_batches = batch_labels[neighbors[1:]]
                
                # Check if neighbors of same cell type come from different batches
                same_celltype_neighbors = neighbors[1:][neighbor_celltypes == true_celltype]
                if len(same_celltype_neighbors) > 1:
                    same_celltype_batches = batch_labels[same_celltype_neighbors]
                    batch_diversity = len(np.unique(same_celltype_batches)) / len(np.unique(batch_labels))
                    consistency_scores.append(batch_diversity)
            
            return np.mean(consistency_scores) if consistency_scores else 0.0
        except:
            return 0.0
    
    def _compute_celltype_separation(
        self,
        embeddings: np.ndarray,
        cell_type_labels: np.ndarray
    ) -> float:
        """Compute cell type separation score."""
        try:
            if len(np.unique(cell_type_labels)) <= 1:
                return 0.0
            return silhouette_score(embeddings, cell_type_labels)
        except:
            return 0.0
    
    def _compute_graph_connectivity(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray
    ) -> float:
        """Compute graph connectivity across batches."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            connected_pairs = 0
            total_pairs = 0
            
            unique_batches = np.unique(batch_labels)
            for b1 in unique_batches:
                for b2 in unique_batches:
                    if b1 != b2:
                        batch1_indices = np.where(batch_labels == b1)[0]
                        batch2_indices = np.where(batch_labels == b2)[0]
                        
                        for i in batch1_indices:
                            neighbors = indices[i][1:]  # Exclude self
                            connections = np.sum(np.isin(neighbors, batch2_indices))
                            connected_pairs += connections
                            total_pairs += len(neighbors)
            
            return connected_pairs / total_pairs if total_pairs > 0 else 0.0
        except:
            return 0.0
    
    def _compute_mixing_metric(
        self,
        embeddings: np.ndarray,
        batch_labels: np.ndarray
    ) -> float:
        """Compute mixing metric based on batch distribution in neighborhoods."""
        try:
            nbrs = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(embeddings)
            _, indices = nbrs.kneighbors(embeddings)
            
            batch_counts = np.bincount(batch_labels)
            expected_probs = batch_counts / len(batch_labels)
            
            mixing_scores = []
            for i, neighbors in enumerate(indices):
                neighbor_batches = batch_labels[neighbors[1:]]  # Exclude self
                observed_counts = np.bincount(neighbor_batches, minlength=len(expected_probs))
                observed_probs = observed_counts / len(neighbor_batches)
                
                # Jensen-Shannon divergence
                m = (expected_probs + observed_probs) / 2
                js_div = 0.5 * np.sum(expected_probs * np.log(expected_probs / (m + 1e-8) + 1e-8)) + \
                        0.5 * np.sum(observed_probs * np.log(observed_probs / (m + 1e-8) + 1e-8))
                
                mixing_scores.append(1 - js_div)  # Higher is better mixing
            
            return np.mean(mixing_scores)
        except:
            return 0.0
    
    def _compute_neighborhood_preservation(
        self,
        original_embeddings: np.ndarray,
        integrated_embeddings: np.ndarray
    ) -> float:
        """Compute neighborhood preservation between original and integrated embeddings."""
        try:
            # Find k-nearest neighbors in both spaces
            nbrs_orig = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(original_embeddings)
            _, indices_orig = nbrs_orig.kneighbors(original_embeddings)
            
            nbrs_int = NearestNeighbors(n_neighbors=self.k_neighbors + 1).fit(integrated_embeddings)
            _, indices_int = nbrs_int.kneighbors(integrated_embeddings)
            
            preservation_scores = []
            for i in range(len(original_embeddings)):
                orig_neighbors = set(indices_orig[i][1:])  # Exclude self
                int_neighbors = set(indices_int[i][1:])   # Exclude self
                
                overlap = len(orig_neighbors.intersection(int_neighbors))
                preservation = overlap / self.k_neighbors
                preservation_scores.append(preservation)
            
            return np.mean(preservation_scores)
        except:
            return 0.0
    
    def _compute_distance_correlation(
        self,
        original_embeddings: np.ndarray,
        integrated_embeddings: np.ndarray
    ) -> float:
        """Compute correlation between distance matrices."""
        try:
            from scipy.spatial.distance import pdist
            from scipy.stats import spearmanr
            
            # Sample subset for efficiency
            n_samples = min(1000, len(original_embeddings))
            indices = np.random.choice(len(original_embeddings), n_samples, replace=False)
            
            orig_subset = original_embeddings[indices]
            int_subset = integrated_embeddings[indices]
            
            orig_distances = pdist(orig_subset)
            int_distances = pdist(int_subset)
            
            correlation, _ = spearmanr(orig_distances, int_distances)
            return correlation if not np.isnan(correlation) else 0.0
        except:
            return 0.0
    
    def _compute_procrustes_distance(
        self,
        original_embeddings: np.ndarray,
        integrated_embeddings: np.ndarray
    ) -> float:
        """Compute Procrustes distance between embeddings."""
        try:
            from scipy.spatial.distance import procrustes
            _, _, disparity = procrustes(original_embeddings, integrated_embeddings)
            return disparity
        except:
            return 1.0
    
    def _compute_celltype_preservation(
        self,
        original_embeddings: np.ndarray,
        integrated_embeddings: np.ndarray,
        cell_type_labels: np.ndarray
    ) -> float:
        """Compute how well cell type structure is preserved."""
        try:
            orig_silhouette = silhouette_score(original_embeddings, cell_type_labels)
            int_silhouette = silhouette_score(integrated_embeddings, cell_type_labels)
            
            # Return ratio of silhouette scores
            return int_silhouette / orig_silhouette if orig_silhouette > 0 else 0.0
        except:
            return 0.0


def compute_integration_metrics(
    embeddings: Union[torch.Tensor, np.ndarray],
    batch_labels: Union[List, np.ndarray],
    cell_type_labels: Optional[Union[List, np.ndarray]] = None,
    cluster_labels: Optional[Union[List, np.ndarray]] = None,
    original_embeddings: Optional[Union[torch.Tensor, np.ndarray]] = None,
    k_neighbors: int = 15
) -> Dict[str, float]:
    """
    Convenience function to compute all integration metrics.
    
    Args:
        embeddings: Integrated embeddings
        batch_labels: Batch labels
        cell_type_labels: Cell type labels (optional)
        cluster_labels: Cluster labels (optional)
        original_embeddings: Original embeddings (optional)
        k_neighbors: Number of neighbors for metrics
        
    Returns:
        Dictionary of computed metrics
    """
    calculator = IntegrationMetrics(k_neighbors=k_neighbors)
    return calculator.compute_all_metrics(
        embeddings, batch_labels, cell_type_labels, cluster_labels, original_embeddings
    )