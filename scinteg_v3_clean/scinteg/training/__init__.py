"""
Training modules for ScINTEG v3.
"""

from .trainer import <PERSON><PERSON>rain<PERSON>, ScINTEGTrainer
from .checkpoint import CheckpointManager, AutoResumeTrainer, find_checkpoint
from .config import (
    OptimizerConfig,
    SchedulerConfig,
    TrainingConfig,
    DataConfig,
    ModelConfig,
    ExperimentConfig,
    create_default_config,
    validate_config
)
from .losses import (
    ScINTEGLoss,
    MSELoss,
    NegativeBinomialLoss,
    ZeroInflatedNegativeBinomialLoss,
    GRNRegularizationLoss,
    PathwaySparsityLoss,
    AdaptiveLossWeighting
)
from .metrics import IntegrationMetrics, compute_integration_metrics
from .schedulers import (
    WarmupScheduler,
    CosineAnnealingWarmRestarts,
    OneCycleLR,
    PolynomialLR,
    AdaptiveLR,
    create_scheduler,
    get_lr_schedule_info
)

__all__ = [
    # Trainer
    'BaseTrainer',
    'ScINTEGTrainer',
    # Checkpoint
    'CheckpointManager',
    'AutoResumeTrainer',
    'find_checkpoint',
    # Config
    'OptimizerConfig',
    'SchedulerConfig',
    'TrainingConfig',
    'DataConfig',
    'ModelConfig',
    'ExperimentConfig',
    'create_default_config',
    'validate_config',
    # Losses
    'ScINTEGLoss',
    'MSELoss',
    'NegativeBinomialLoss',
    'ZeroInflatedNegativeBinomialLoss',
    'GRNRegularizationLoss',
    'PathwaySparsityLoss',
    'AdaptiveLossWeighting',
    # Metrics
    'IntegrationMetrics',
    'compute_integration_metrics',
    # Schedulers
    'WarmupScheduler',
    'CosineAnnealingWarmRestarts',
    'OneCycleLR',
    'PolynomialLR',
    'AdaptiveLR',
    'create_scheduler',
    'get_lr_schedule_info'
]