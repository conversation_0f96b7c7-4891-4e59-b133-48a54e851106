"""
Configuration management for ScINTEG v3 training.

This module provides configuration classes and utilities for training,
including default configurations and validation.
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, Optional, Any, Union, List
from pathlib import Path
import yaml
import json


@dataclass
class OptimizerConfig:
    """Optimizer configuration."""
    name: str = 'adam'  # adam, sgd, adamw, rmsprop
    lr: float = 1e-3
    weight_decay: float = 0.0
    # Adam/AdamW specific
    betas: tuple = (0.9, 0.999)
    eps: float = 1e-8
    # SGD specific
    momentum: float = 0.9
    nesterov: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class SchedulerConfig:
    """Learning rate scheduler configuration."""
    name: Optional[str] = None  # cosine, step, exponential, reduce_on_plateau
    # Cosine annealing
    T_max: Optional[int] = None
    eta_min: float = 0.0
    # Step LR
    step_size: int = 30
    gamma: float = 0.1
    # Exponential
    # gamma already defined above
    # ReduceLROnPlateau
    mode: str = 'min'
    factor: float = 0.1
    patience: int = 10
    threshold: float = 0.0001
    cooldown: int = 0
    min_lr: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class TrainingConfig:
    """Training configuration."""
    # Basic training
    epochs: int = 100
    batch_size: int = 32
    accumulate_grad_batches: int = 1
    gradient_clip_val: Optional[float] = 1.0
    
    # Optimization
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    scheduler: Optional[SchedulerConfig] = field(default_factory=SchedulerConfig)
    
    # Validation
    val_check_interval: Union[int, float] = 1
    val_batch_size: Optional[int] = None  # If None, use batch_size
    
    # Early stopping
    early_stopping: bool = True
    early_stopping_patience: int = 20
    early_stopping_min_delta: float = 0.0001
    early_stopping_mode: str = 'min'
    
    # Checkpointing
    checkpoint_dir: Optional[str] = './checkpoints'
    save_top_k: int = 3
    save_last: bool = True
    checkpoint_monitor: str = 'val_loss'
    checkpoint_mode: str = 'min'
    
    # Mixed precision
    mixed_precision: bool = False
    
    # Logging
    log_interval: int = 10
    log_dir: Optional[str] = './logs'
    use_tensorboard: bool = True
    use_wandb: bool = False
    wandb_project: Optional[str] = None
    
    # Device
    device: str = 'cuda'
    num_workers: int = 4
    pin_memory: bool = True
    
    # Reproducibility
    seed: int = 42
    deterministic: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        config_dict = asdict(self)
        # Convert nested configs
        config_dict['optimizer'] = self.optimizer.to_dict()
        if self.scheduler:
            config_dict['scheduler'] = self.scheduler.to_dict()
        return config_dict
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create from dictionary."""
        config_dict = config_dict.copy()
        
        # Convert optimizer config
        if 'optimizer' in config_dict:
            config_dict['optimizer'] = OptimizerConfig(**config_dict['optimizer'])
        
        # Convert scheduler config
        if 'scheduler' in config_dict and config_dict['scheduler']:
            config_dict['scheduler'] = SchedulerConfig(**config_dict['scheduler'])
        
        return cls(**config_dict)
    
    def save(self, path: Union[str, Path]):
        """Save configuration to file."""
        path = Path(path)
        config_dict = self.to_dict()
        
        if path.suffix == '.yaml':
            with open(path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
        else:
            with open(path, 'w') as f:
                json.dump(config_dict, f, indent=2)
    
    @classmethod
    def load(cls, path: Union[str, Path]) -> 'TrainingConfig':
        """Load configuration from file."""
        path = Path(path)
        
        if path.suffix == '.yaml':
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            with open(path, 'r') as f:
                config_dict = json.load(f)
        
        return cls.from_dict(config_dict)


@dataclass
class DataConfig:
    """Data configuration."""
    # Dataset paths
    train_data: Optional[str] = None
    val_data: Optional[str] = None
    test_data: Optional[str] = None
    
    # Or single dataset with split
    data_path: Optional[str] = None
    train_ratio: float = 0.8
    val_ratio: float = 0.1
    test_ratio: float = 0.1
    
    # Data loading
    batch_size: int = 32
    num_workers: int = 4
    pin_memory: bool = True
    
    # Graph construction
    build_cell_graph: bool = True
    build_gene_graph: bool = True
    cell_graph_k: int = 15
    gene_graph_threshold: float = 0.5
    rebuild_graph_per_batch: bool = True
    
    # Preprocessing
    normalize: bool = True
    log_transform: bool = True
    scale: bool = False
    
    # Sampling
    balanced_sampling: bool = False
    balance_by: str = 'batch'  # 'batch' or 'cell_type'
    
    # Augmentation
    expression_noise: float = 0.0
    dropout_rate: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ModelConfig:
    """Model configuration."""
    # Core dimensions
    n_genes: int = 2000
    n_pathways: int = 50
    n_meta_pathways: Optional[int] = None
    
    # Embedding dimensions
    cell_embedding_dim: int = 128
    gene_embedding_dim: int = 64
    cell_hidden_dim: int = 256
    gene_hidden_dim: int = 128
    
    # Architecture choices
    use_hierarchical_pathways: bool = False
    use_unet_decoder: bool = False
    cell_encoder_type: str = 'standard'
    gene_encoder_type: str = 'standard'
    
    # Component configurations
    cell_encoder_config: Dict[str, Any] = field(default_factory=dict)
    gene_encoder_config: Dict[str, Any] = field(default_factory=dict)
    projector_config: Dict[str, Any] = field(default_factory=dict)
    decoder_config: Dict[str, Any] = field(default_factory=dict)
    grn_predictor_config: Dict[str, Any] = field(default_factory=dict)
    
    # Loss configuration
    reconstruction_loss_type: str = 'mse'  # mse, nb, zinb
    reconstruction_weight: float = 1.0
    grn_weight: float = 0.1
    pathway_sparsity_weight: float = 0.01
    use_adaptive_weighting: bool = False
    
    # Regularization
    dropout: float = 0.1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ExperimentConfig:
    """Complete experiment configuration."""
    name: str = 'scinteg_experiment'
    description: str = ''
    
    # Sub-configurations
    model: ModelConfig = field(default_factory=ModelConfig)
    data: DataConfig = field(default_factory=DataConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    
    # Output paths
    output_dir: str = './experiments'
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'name': self.name,
            'description': self.description,
            'model': self.model.to_dict(),
            'data': self.data.to_dict(),
            'training': self.training.to_dict(),
            'output_dir': self.output_dir
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ExperimentConfig':
        """Create from dictionary."""
        config_dict = config_dict.copy()
        
        # Convert sub-configs
        config_dict['model'] = ModelConfig(**config_dict['model'])
        config_dict['data'] = DataConfig(**config_dict['data'])
        config_dict['training'] = TrainingConfig.from_dict(config_dict['training'])
        
        return cls(**config_dict)
    
    def save(self, path: Union[str, Path]):
        """Save configuration to file."""
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        config_dict = self.to_dict()
        
        if path.suffix == '.yaml':
            with open(path, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False)
        else:
            with open(path, 'w') as f:
                json.dump(config_dict, f, indent=2)
    
    @classmethod
    def load(cls, path: Union[str, Path]) -> 'ExperimentConfig':
        """Load configuration from file."""
        path = Path(path)
        
        if path.suffix == '.yaml':
            with open(path, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            with open(path, 'r') as f:
                config_dict = json.load(f)
        
        return cls.from_dict(config_dict)
    
    def get_experiment_dir(self) -> Path:
        """Get experiment directory path."""
        return Path(self.output_dir) / self.name
    
    def setup_directories(self):
        """Create necessary directories."""
        exp_dir = self.get_experiment_dir()
        
        # Create directories
        (exp_dir / 'checkpoints').mkdir(parents=True, exist_ok=True)
        (exp_dir / 'logs').mkdir(parents=True, exist_ok=True)
        (exp_dir / 'results').mkdir(parents=True, exist_ok=True)
        
        # Update paths in config
        self.training.checkpoint_dir = str(exp_dir / 'checkpoints')
        self.training.log_dir = str(exp_dir / 'logs')
        
        # Save config
        self.save(exp_dir / 'config.yaml')


def create_default_config(
    name: str = 'default_experiment',
    model_type: str = 'standard',
    dataset: str = 'pbmc'
) -> ExperimentConfig:
    """
    Create default configuration for common scenarios.
    
    Args:
        name: Experiment name
        model_type: Model variant ('standard', 'hierarchical', 'unet')
        dataset: Dataset type ('pbmc', 'mouse_brain', 'custom')
        
    Returns:
        Default experiment configuration
    """
    config = ExperimentConfig(name=name)
    
    # Model-specific settings
    if model_type == 'hierarchical':
        config.model.use_hierarchical_pathways = True
        config.model.n_meta_pathways = 20
    elif model_type == 'unet':
        config.model.use_unet_decoder = True
    
    # Dataset-specific settings
    if dataset == 'pbmc':
        config.data.cell_graph_k = 15
        config.data.gene_graph_threshold = 0.5
    elif dataset == 'mouse_brain':
        config.data.cell_graph_k = 20
        config.data.gene_graph_threshold = 0.6
        config.data.balanced_sampling = True
        config.data.balance_by = 'cell_type'
    
    return config


def validate_config(config: ExperimentConfig) -> List[str]:
    """
    Validate experiment configuration.
    
    Args:
        config: Configuration to validate
        
    Returns:
        List of validation errors (empty if valid)
    """
    errors = []
    
    # Check data paths
    if config.data.data_path is None:
        if config.data.train_data is None:
            errors.append("Either data_path or train_data must be specified")
    
    # Check dimensions
    if config.model.n_genes <= 0:
        errors.append("n_genes must be positive")
    if config.model.n_pathways <= 0:
        errors.append("n_pathways must be positive")
    
    if config.model.use_hierarchical_pathways:
        if config.model.n_meta_pathways is None:
            errors.append("n_meta_pathways required for hierarchical pathways")
        elif config.model.n_meta_pathways >= config.model.n_pathways:
            errors.append("n_meta_pathways must be less than n_pathways")
    
    # Check training settings
    if config.training.batch_size <= 0:
        errors.append("batch_size must be positive")
    if config.training.epochs <= 0:
        errors.append("epochs must be positive")
    
    # Check loss weights
    if config.model.reconstruction_weight < 0:
        errors.append("reconstruction_weight must be non-negative")
    if config.model.grn_weight < 0:
        errors.append("grn_weight must be non-negative")
    
    return errors