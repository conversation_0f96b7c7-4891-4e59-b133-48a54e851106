"""
Standard interfaces and data structures for ScINTEG.

This module defines the standard data structures used throughout ScINTEG
to ensure consistent data flow between components.
"""

from dataclasses import dataclass
from typing import Dict, Optional, List, Tuple, Union
import torch
from torch import Tensor


@dataclass
class ModelInputs:
    """Standard input data structure for ScINTEG models."""
    
    # Expression data
    expression: Tensor  # [n_cells, n_genes]
    
    # Graph structures
    cell_graph: Tensor  # [2, n_cell_edges]
    gene_graph: Tensor  # [2, n_gene_edges]
    
    # Optional metadata
    batch_labels: Optional[Tensor] = None  # [n_cells]
    time_labels: Optional[Tensor] = None   # [n_cells]
    cell_types: Optional[List[str]] = None # [n_cells]
    
    # Optional masks
    gene_mask: Optional[Tensor] = None     # [n_genes]
    cell_mask: Optional[Tensor] = None     # [n_cells]
    
    def to(self, device: Union[str, torch.device]) -> 'ModelInputs':
        """Move all tensors to specified device."""
        return ModelInputs(
            expression=self.expression.to(device),
            cell_graph=self.cell_graph.to(device),
            gene_graph=self.gene_graph.to(device),
            batch_labels=self.batch_labels.to(device) if self.batch_labels is not None else None,
            time_labels=self.time_labels.to(device) if self.time_labels is not None else None,
            cell_types=self.cell_types,
            gene_mask=self.gene_mask.to(device) if self.gene_mask is not None else None,
            cell_mask=self.cell_mask.to(device) if self.cell_mask is not None else None
        )


@dataclass
class EncoderOutputs:
    """Standard output from encoder modules."""
    
    embeddings: Tensor                          # Main embeddings
    attention_weights: Optional[List[Tensor]] = None  # Attention maps
    importance_scores: Optional[Tensor] = None        # Feature importance
    auxiliary: Optional[Dict[str, Tensor]] = None     # Other outputs


@dataclass 
class ProjectorOutputs:
    """Standard output from projector modules."""
    
    projections: Tensor                         # Main projections
    meta_projections: Optional[Tensor] = None   # Higher-level projections
    masks: Optional[Tensor] = None              # Applied masks
    auxiliary: Optional[Dict[str, Tensor]] = None


@dataclass
class DecoderOutputs:
    """Standard output from decoder modules."""
    
    reconstruction: Tensor                      # Reconstructed data
    latent_features: Optional[List[Tensor]] = None  # Intermediate features
    attention_maps: Optional[List[Tensor]] = None   # Attention weights
    auxiliary: Optional[Dict[str, Tensor]] = None


@dataclass
class PredictorOutputs:
    """Standard output from predictor modules."""
    
    predictions: Dict[str, Tensor]              # Main predictions
    confidence: Optional[Tensor] = None         # Prediction confidence
    auxiliary: Optional[Dict[str, Tensor]] = None


@dataclass
class ModelOutputs:
    """Complete model outputs."""
    
    # Primary outputs
    reconstruction: Tensor                      # Reconstructed expression
    grn: Dict[str, Tensor]                     # Gene regulatory network
    
    # Embeddings
    cell_embeddings: Tensor                     # Cell embeddings
    gene_embeddings: Tensor                     # Gene embeddings
    pathway_features: Tensor                    # Pathway features
    
    # Optional outputs
    meta_pathway_features: Optional[Tensor] = None
    attention_weights: Optional[Dict[str, List[Tensor]]] = None
    
    # Loss components
    losses: Optional[Dict[str, Tensor]] = None
    
    # Auxiliary outputs
    auxiliary: Optional[Dict[str, Tensor]] = None


class DataBatch:
    """Container for batched data with utilities."""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to(self, device: Union[str, torch.device]) -> 'DataBatch':
        """Move all tensors to device."""
        batch = DataBatch()
        for key, value in self.__dict__.items():
            if isinstance(value, torch.Tensor):
                setattr(batch, key, value.to(device))
            else:
                setattr(batch, key, value)
        return batch
    
    def __getitem__(self, key: str):
        return getattr(self, key)
    
    def __setitem__(self, key: str, value):
        setattr(self, key, value)
    
    def keys(self):
        return self.__dict__.keys()
    
    def values(self):
        return self.__dict__.values()
    
    def items(self):
        return self.__dict__.items()


# Type aliases for common structures
EdgeIndex = Tensor  # [2, n_edges]
NodeFeatures = Tensor  # [n_nodes, feature_dim]
AdjacencyMatrix = Tensor  # [n_nodes, n_nodes]
Mask = Tensor  # Binary tensor


# Constants
DEFAULT_ACTIVATION = 'gelu'
DEFAULT_NORM = 'layer'
DEFAULT_DROPOUT = 0.1
DEFAULT_ATTENTION_HEADS = 8