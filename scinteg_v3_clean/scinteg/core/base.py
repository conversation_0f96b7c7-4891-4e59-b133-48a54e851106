"""
Base classes for ScINTEG components.

This module defines the abstract base classes that all ScINTEG components
should inherit from, ensuring consistent interfaces across the framework.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, Tuple, List
import torch
import torch.nn as nn
import json
import yaml


class BaseModule(nn.Module, ABC):
    """
    Abstract base class for all ScINTEG modules.
    
    This class ensures that all modules have consistent interfaces for:
    - Configuration management
    - Input/output validation
    - State serialization
    """
    
    def __init__(self, **kwargs):
        super().__init__()
        self.config = kwargs
        self._validate_config()
        self._build()
    
    @abstractmethod
    def _validate_config(self):
        """Validate configuration parameters."""
        pass
    
    @abstractmethod
    def _build(self):
        """Build the module architecture."""
        pass
    
    @abstractmethod
    def forward(self, *args, **kwargs):
        """Forward pass of the module."""
        pass
    
    def get_config(self) -> Dict[str, Any]:
        """Return configuration dictionary."""
        return self.config.copy()
    
    def save_config(self, path: str):
        """Save configuration to file."""
        with open(path, 'w') as f:
            if path.endswith('.yaml'):
                yaml.dump(self.config, f)
            else:
                json.dump(self.config, f, indent=2)
    
    @classmethod
    def from_config(cls, config: Union[Dict, str]):
        """Create instance from configuration."""
        if isinstance(config, str):
            with open(config, 'r') as f:
                if config.endswith('.yaml'):
                    config = yaml.safe_load(f)
                else:
                    config = json.load(f)
        return cls(**config)


class BaseEncoder(BaseModule):
    """Abstract base class for encoders."""
    
    @abstractmethod
    def encode(self, x: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """
        Encode input data to embeddings.
        
        Args:
            x: Input data tensor
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Encoded embeddings
        """
        pass
    
    def forward(self, x: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """Forward pass calls encode method."""
        return self.encode(x, *args, **kwargs)


class BaseDecoder(BaseModule):
    """Abstract base class for decoders."""
    
    @abstractmethod
    def decode(self, z: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """
        Decode embeddings to output space.
        
        Args:
            z: Encoded embeddings
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Decoded output
        """
        pass
    
    def forward(self, z: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """Forward pass calls decode method."""
        return self.decode(z, *args, **kwargs)


class BaseProjector(BaseModule):
    """Abstract base class for projectors."""
    
    @abstractmethod
    def project(self, embeddings: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """
        Project embeddings to target space.
        
        Args:
            embeddings: Input embeddings
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Projected features
        """
        pass
    
    def forward(self, embeddings: torch.Tensor, *args, **kwargs) -> torch.Tensor:
        """Forward pass calls project method."""
        return self.project(embeddings, *args, **kwargs)


class BasePredictor(BaseModule):
    """Abstract base class for predictors."""
    
    @abstractmethod
    def predict(self, features: torch.Tensor, *args, **kwargs) -> Dict[str, torch.Tensor]:
        """
        Make predictions from features.
        
        Args:
            features: Input features
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Dictionary of predictions
        """
        pass
    
    def forward(self, features: torch.Tensor, *args, **kwargs) -> Dict[str, torch.Tensor]:
        """Forward pass calls predict method."""
        return self.predict(features, *args, **kwargs)


class BaseLoss(nn.Module, ABC):
    """Abstract base class for loss functions."""
    
    def __init__(self, reduction: str = 'mean', **kwargs):
        super().__init__()
        self.reduction = reduction
        self.config = kwargs
    
    @abstractmethod
    def compute_loss(self, pred: torch.Tensor, target: torch.Tensor, 
                    *args, **kwargs) -> torch.Tensor:
        """
        Compute loss between predictions and targets.
        
        Args:
            pred: Predicted values
            target: Target values
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Loss value
        """
        pass
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor, 
                *args, **kwargs) -> torch.Tensor:
        """Forward pass calls compute_loss method."""
        return self.compute_loss(pred, target, *args, **kwargs)
    
    def _reduce(self, loss: torch.Tensor) -> torch.Tensor:
        """Apply reduction to loss tensor."""
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        elif self.reduction == 'none':
            return loss
        else:
            raise ValueError(f"Unknown reduction: {self.reduction}")