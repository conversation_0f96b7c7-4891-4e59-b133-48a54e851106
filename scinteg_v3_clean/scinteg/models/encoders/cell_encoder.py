"""
Standardized Cell Encoder for ScINTEG v3.

This module implements a cell encoder that learns low-dimensional embeddings
for cells using Graph Neural Networks on a cell-similarity graph.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional

from ...core.base import BaseEncoder
from ...core.interfaces import EncoderOutputs

# Try to import PyTorch Geometric, provide fallback if not available
try:
    from torch_geometric.nn import GCNConv, GATConv
    HAS_TORCH_GEOMETRIC = True
except ImportError:
    HAS_TORCH_GEOMETRIC = False
    # Simple fallback implementations
    class GCNConv(nn.Module):
        def __init__(self, in_channels, out_channels):
            super().__init__()
            self.linear = nn.Linear(in_channels, out_channels)
            
        def forward(self, x, edge_index):
            # Simple mean aggregation fallback
            _ = edge_index  # Acknowledge parameter
            return self.linear(x)
    
    class GATConv(nn.Module):
        def __init__(self, in_channels, out_channels):
            super().__init__()
            self.linear = nn.Linear(in_channels, out_channels)
            
        def forward(self, x, edge_index):
            _ = edge_index  # Acknowledge parameter
            return self.linear(x)


class StandardCellEncoder(BaseEncoder):
    """
    Standardized Cell Encoder using Graph Neural Networks.
    
    This encoder processes cell expression data through GNN layers to learn
    meaningful cell representations that capture both expression patterns
    and cellular relationships.
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 256,
        output_dim: int = 64,
        n_layers: int = 2,
        conv_type: str = "gcn",  # "gcn" or "gat"
        n_heads: int = 8,  # For GAT
        dropout: float = 0.1,
        use_batch_norm: bool = True,
        activation: str = "gelu",
        **kwargs
    ):
        """
        Initialize the StandardCellEncoder.
        
        Args:
            input_dim: Dimension of input cell features
            hidden_dim: Dimension of hidden layers
            output_dim: Dimension of output embeddings
            n_layers: Number of GNN layers
            conv_type: Type of convolution ("gcn" or "gat")
            n_heads: Number of attention heads (for GAT)
            dropout: Dropout probability
            use_batch_norm: Whether to use batch normalization
            activation: Activation function name
        """
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.n_layers = n_layers
        self.conv_type = conv_type.lower()
        self.n_heads = n_heads
        self.dropout = dropout
        self.use_batch_norm = use_batch_norm
        self.activation_name = activation
        
        super().__init__(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            n_layers=n_layers,
            conv_type=conv_type,
            n_heads=n_heads,
            dropout=dropout,
            use_batch_norm=use_batch_norm,
            activation=activation,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.input_dim > 0, "input_dim must be positive"
        assert self.hidden_dim > 0, "hidden_dim must be positive"
        assert self.output_dim > 0, "output_dim must be positive"
        assert self.n_layers >= 1, "n_layers must be at least 1"
        assert self.conv_type in ["gcn", "gat"], f"Unknown conv_type: {self.conv_type}"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
        
        if not HAS_TORCH_GEOMETRIC and self.conv_type == "gat":
            print("Warning: torch_geometric not available, falling back to simple linear layers")
    
    def _build(self):
        """Build the encoder architecture."""
        # Get activation function
        self.activation = self._get_activation()
        
        # Build GNN layers
        self.conv_layers = nn.ModuleList()
        self.norm_layers = nn.ModuleList() if self.use_batch_norm else None
        
        # Determine layer dimensions
        dims = self._get_layer_dims()
        
        # Create layers
        for i in range(self.n_layers):
            in_dim = dims[i]
            out_dim = dims[i + 1]
            
            # Create convolution layer
            if self.conv_type == "gcn":
                conv = GCNConv(in_dim, out_dim)
            elif self.conv_type == "gat" and HAS_TORCH_GEOMETRIC:
                # For GAT, output dimension is divided by number of heads
                conv = GATConv(in_dim, out_dim // self.n_heads, heads=self.n_heads)
            else:
                # Fallback
                conv = GCNConv(in_dim, out_dim)
            
            self.conv_layers.append(conv)
            
            # Add normalization
            if self.use_batch_norm:
                self.norm_layers.append(nn.BatchNorm1d(out_dim))
        
        # Optional projection head
        self.projection_head = None
        if self.config.get('use_projection_head', False):
            self.projection_head = nn.Sequential(
                nn.Linear(self.output_dim, self.output_dim),
                nn.BatchNorm1d(self.output_dim),
                self.activation,
                nn.Linear(self.output_dim, self.output_dim)
            )
    
    def _get_layer_dims(self):
        """Calculate dimensions for each layer."""
        if self.n_layers == 1:
            return [self.input_dim, self.output_dim]
        elif self.n_layers == 2:
            return [self.input_dim, self.hidden_dim, self.output_dim]
        else:
            # Multiple hidden layers
            dims = [self.input_dim]
            for _ in range(self.n_layers - 1):
                dims.append(self.hidden_dim)
            dims.append(self.output_dim)
            return dims
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'elu': nn.ELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'tanh': nn.Tanh()
        }
        return activations.get(self.activation_name, nn.GELU())
    
    def encode(
        self, 
        x: torch.Tensor,
        edge_index: torch.Tensor,
        edge_weight: Optional[torch.Tensor] = None,  # Currently unused
        return_attention: bool = False
    ) -> EncoderOutputs:
        """
        Encode cells to embeddings.
        
        Args:
            x: Cell feature tensor [n_cells, input_dim]
            edge_index: Graph connectivity [2, n_edges]
            edge_weight: Optional edge weights [n_edges]
            return_attention: Whether to return attention weights (GAT only)
            
        Returns:
            EncoderOutputs with cell embeddings and optional attention
        """
        h = x
        attention_weights = []
        
        # Pass through GNN layers
        for i, conv in enumerate(self.conv_layers):
            # Apply convolution
            if self.conv_type == "gat" and HAS_TORCH_GEOMETRIC and return_attention:
                h, attn = conv(h, edge_index, return_attention_weights=True)
                attention_weights.append(attn)
            else:
                h = conv(h, edge_index)
            
            # Apply normalization
            if self.use_batch_norm:
                h = self.norm_layers[i](h)
            
            # Apply activation and dropout (except last layer)
            if i < self.n_layers - 1:
                h = self.activation(h)
                h = F.dropout(h, p=self.dropout, training=self.training)
        
        # Optional projection head
        if self.projection_head is not None:
            h = self.projection_head(h)
        
        return EncoderOutputs(
            embeddings=h,
            attention_weights=attention_weights if attention_weights else None,
            importance_scores=None,
            auxiliary={
                'pre_projection': x,
                'n_edges': edge_index.shape[1]
            }
        )
    
    def forward(
        self,
        x: torch.Tensor,
        edge_index: torch.Tensor,
        **kwargs
    ) -> torch.Tensor:
        """
        Forward pass returning only embeddings for compatibility.
        
        Args:
            x: Cell features [n_cells, input_dim]
            edge_index: Graph connectivity [2, n_edges]
            **kwargs: Additional arguments
            
        Returns:
            Cell embeddings [n_cells, output_dim]
        """
        outputs = self.encode(x, edge_index, **kwargs)
        return outputs.embeddings