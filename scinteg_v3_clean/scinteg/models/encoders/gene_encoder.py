"""
Standardized Gene Encoder for ScINTEG v3.

This module implements a gene encoder that learns functional representations
of genes using graph neural networks with biological prior knowledge.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, List, Dict, Any, Tuple

from ...core.base import BaseEncoder
from ...core.interfaces import EncoderOutputs

# Try to import PyTorch Geometric
try:
    from torch_geometric.nn import GATv2Conv, global_mean_pool, global_max_pool
    from torch_geometric.utils import add_self_loops, dropout_edge
    HAS_TORCH_GEOMETRIC = True
except ImportError:
    HAS_TORCH_GEOMETRIC = False
    # Fallback implementations
    class GATv2Conv(nn.Module):
        def __init__(self, in_channels, out_channels, heads=1, **kwargs):
            super().__init__()
            self.linear = nn.Linear(in_channels, out_channels * heads)
            self.heads = heads
            
        def forward(self, x, edge_index, return_attention_weights=False):
            out = self.linear(x)
            if return_attention_weights:
                return out, (edge_index, None)
            return out
    
    def global_mean_pool(x, batch):
        return x.mean(dim=0, keepdim=True)
    
    def global_max_pool(x, batch):
        return x.max(dim=0, keepdim=True)[0]


class StandardGeneEncoder(BaseEncoder):
    """
    Standardized Gene Encoder using Graph Attention Networks.
    
    This encoder learns gene representations by incorporating:
    - Gene co-expression relationships
    - Transcription factor information
    - Pathway membership
    - Multi-scale attention mechanisms
    """
    
    def __init__(
        self,
        n_genes: int,
        feature_dim: int = 64,
        hidden_dim: int = 256,
        output_dim: int = 64,
        n_layers: int = 3,
        n_heads: int = 8,
        dropout: float = 0.1,
        use_batch_norm: bool = True,
        activation: str = "gelu",
        # Biological priors
        tf_indices: Optional[List[int]] = None,
        pathway_mask: Optional[torch.Tensor] = None,
        # Advanced features
        use_multi_scale: bool = True,
        use_positional_encoding: bool = True,
        **kwargs
    ):
        """
        Initialize the StandardGeneEncoder.
        
        Args:
            n_genes: Total number of genes
            feature_dim: Initial feature dimension
            hidden_dim: Hidden layer dimension
            output_dim: Output embedding dimension
            n_layers: Number of GAT layers
            n_heads: Number of attention heads
            dropout: Dropout probability
            use_batch_norm: Whether to use batch normalization
            activation: Activation function name
            tf_indices: List of transcription factor gene indices
            pathway_mask: Binary mask of pathway-gene relationships
            use_multi_scale: Whether to use multi-scale attention
            use_positional_encoding: Whether to use positional encoding
        """
        self.n_genes = n_genes
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.n_layers = n_layers
        self.n_heads = n_heads
        self.dropout = dropout
        self.use_batch_norm = use_batch_norm
        self.activation_name = activation
        self.tf_indices = tf_indices
        self.pathway_mask = pathway_mask
        self.use_multi_scale = use_multi_scale
        self.use_positional_encoding = use_positional_encoding
        
        super().__init__(
            n_genes=n_genes,
            feature_dim=feature_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            n_layers=n_layers,
            n_heads=n_heads,
            dropout=dropout,
            use_batch_norm=use_batch_norm,
            activation=activation,
            tf_indices=tf_indices,
            pathway_mask=pathway_mask,
            use_multi_scale=use_multi_scale,
            use_positional_encoding=use_positional_encoding,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.feature_dim > 0, "feature_dim must be positive"
        assert self.hidden_dim > 0, "hidden_dim must be positive"
        assert self.output_dim > 0, "output_dim must be positive"
        assert self.n_layers >= 1, "n_layers must be at least 1"
        assert self.n_heads >= 1, "n_heads must be at least 1"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
    
    def _build(self):
        """Build the encoder architecture."""
        # Gene embeddings
        self.gene_embedding = nn.Embedding(self.n_genes, self.feature_dim)
        
        # Positional encoding
        if self.use_positional_encoding:
            self.positional_encoding = self._create_positional_encoding()
        
        # Input projection
        self.input_projection = nn.Sequential(
            nn.Linear(self.feature_dim, self.hidden_dim),
            nn.LayerNorm(self.hidden_dim) if self.use_batch_norm else nn.Identity(),
            self._get_activation(),
            nn.Dropout(self.dropout)
        )
        
        # GAT layers
        self.gat_layers = nn.ModuleList()
        self.norm_layers = nn.ModuleList() if self.use_batch_norm else None
        
        for i in range(self.n_layers):
            in_dim = self.hidden_dim if i > 0 else self.hidden_dim
            out_dim = self.output_dim if i == self.n_layers - 1 else self.hidden_dim
            
            # GAT layer
            if HAS_TORCH_GEOMETRIC:
                gat = GATv2Conv(
                    in_dim, 
                    out_dim // self.n_heads if i < self.n_layers - 1 else out_dim,
                    heads=self.n_heads if i < self.n_layers - 1 else 1,
                    dropout=self.dropout,
                    add_self_loops=True,
                    concat=i < self.n_layers - 1
                )
            else:
                # Fallback that outputs correct dimension
                gat = GATv2Conv(in_dim, out_dim)
            
            self.gat_layers.append(gat)
            
            # Normalization
            if self.use_batch_norm and i < self.n_layers - 1:
                self.norm_layers.append(nn.BatchNorm1d(out_dim))
        
        # Output normalization
        self.output_norm = nn.LayerNorm(self.output_dim)
        
        # Gene importance prediction head
        self.importance_head = nn.Sequential(
            nn.Linear(self.output_dim, self.output_dim // 2),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.output_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # Biological attention modules
        if self.tf_indices is not None or self.pathway_mask is not None:
            self._build_biological_attention()
        
        self._init_weights()
    
    def _create_positional_encoding(self):
        """Create sinusoidal positional encoding."""
        pe = torch.zeros(self.n_genes, self.feature_dim)
        position = torch.arange(0, self.n_genes).unsqueeze(1).float()
        
        div_term = torch.exp(
            torch.arange(0, self.feature_dim, 2).float() * 
            -(math.log(10000.0) / self.feature_dim)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pe, requires_grad=False)
    
    def _build_biological_attention(self):
        """Build biological prior attention modules."""
        # TF-specific attention
        if self.tf_indices is not None:
            self.tf_attention = nn.Sequential(
                nn.Linear(self.feature_dim, self.feature_dim // 2),
                self._get_activation(),
                nn.Linear(self.feature_dim // 2, 1),
                nn.Sigmoid()
            )
        
        # Pathway-aware attention
        if self.pathway_mask is not None:
            n_pathways = self.pathway_mask.shape[0]
            self.pathway_attention = nn.Sequential(
                nn.Linear(self.feature_dim + n_pathways, self.feature_dim),
                self._get_activation(),
                nn.Linear(self.feature_dim, self.feature_dim),
                nn.Sigmoid()
            )
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'elu': nn.ELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'tanh': nn.Tanh()
        }
        return activations.get(self.activation_name, nn.GELU())
    
    def _init_weights(self):
        """Initialize weights."""
        # Initialize gene embeddings with small values
        nn.init.normal_(self.gene_embedding.weight, mean=0, std=0.02)
        
        # Xavier initialization for linear layers
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def _apply_biological_attention(self, x: torch.Tensor, gene_indices: torch.Tensor) -> torch.Tensor:
        """Apply biological prior-based attention."""
        attention_weights = torch.ones_like(x[..., 0:1])
        
        # TF-specific attention
        if hasattr(self, 'tf_attention') and self.tf_indices is not None:
            tf_tensor = torch.tensor(self.tf_indices, device=x.device)
            tf_mask = torch.isin(gene_indices, tf_tensor)
            tf_weights = self.tf_attention(x)
            attention_weights = torch.where(
                tf_mask.unsqueeze(-1),
                tf_weights * 2.0,  # Enhance TF importance
                attention_weights
            )
        
        # Pathway-aware attention
        if hasattr(self, 'pathway_attention') and self.pathway_mask is not None:
            # Get pathway membership for genes
            gene_pathways = self.pathway_mask[:, gene_indices].T.to(x.device)
            x_with_pathways = torch.cat([x, gene_pathways], dim=-1)
            pathway_weights = self.pathway_attention(x_with_pathways)
            attention_weights = attention_weights * pathway_weights[..., 0:1]
        
        return x * attention_weights
    
    def encode(
        self,
        gene_indices: torch.Tensor,
        edge_index: torch.Tensor,
        edge_weight: Optional[torch.Tensor] = None,
        batch: Optional[torch.Tensor] = None
    ) -> EncoderOutputs:
        """
        Encode genes to embeddings.
        
        Args:
            gene_indices: Gene indices [n_genes_batch]
            edge_index: Gene graph connectivity [2, n_edges]
            edge_weight: Optional edge weights [n_edges]
            batch: Optional batch indices for pooling
            
        Returns:
            EncoderOutputs with gene embeddings and metadata
        """
        # Get gene embeddings
        x = self.gene_embedding(gene_indices)
        
        # Add positional encoding
        if self.use_positional_encoding:
            x = x + self.positional_encoding[gene_indices]
        
        # Apply biological attention
        if hasattr(self, 'tf_attention') or hasattr(self, 'pathway_attention'):
            x = self._apply_biological_attention(x, gene_indices)
        
        # Input projection
        x = self.input_projection(x)
        
        # Pass through GAT layers
        attention_weights = []
        for i, gat in enumerate(self.gat_layers):
            # Apply GAT
            if HAS_TORCH_GEOMETRIC:
                x, (edge_idx, alpha) = gat(x, edge_index, return_attention_weights=True)
                attention_weights.append((edge_idx, alpha))
            else:
                x = gat(x, edge_index)
            
            # Apply normalization and activation (except last layer)
            if i < self.n_layers - 1:
                if self.use_batch_norm:
                    x = self.norm_layers[i](x)
                x = self._get_activation()(x)
                x = F.dropout(x, p=self.dropout, training=self.training)
        
        # Output normalization
        x = self.output_norm(x)
        
        # Compute gene importance
        importance = self.importance_head(x)
        
        # Global pooling if batch is provided
        global_features = None
        if batch is not None:
            global_mean = global_mean_pool(x, batch)
            global_max = global_max_pool(x, batch)
            global_features = torch.cat([global_mean, global_max], dim=-1)
        
        return EncoderOutputs(
            embeddings=x,
            attention_weights=attention_weights if attention_weights else None,
            importance_scores=importance,
            auxiliary={
                'global_features': global_features,
                'gene_indices': gene_indices,
                'n_edges': edge_index.shape[1]
            }
        )
    
    def forward(
        self,
        gene_indices: torch.Tensor,
        edge_index: torch.Tensor,
        **kwargs
    ) -> Tuple[torch.Tensor, List, torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass with legacy output format.
        
        Returns:
            Tuple of (embeddings, attention_weights, importance, global_features)
        """
        outputs = self.encode(gene_indices, edge_index, **kwargs)
        return (
            outputs.embeddings,
            outputs.attention_weights or [],
            outputs.importance_scores,
            outputs.auxiliary.get('global_features')
        )