"""
Gene Regulatory Network Predictor for ScINTEG v3.

This module infers gene regulatory relationships from gene embeddings
and attention patterns.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, List, Tuple, Any
import warnings

from ...core.base import BasePredictor
from ...core.interfaces import PredictorOutputs

# Try to import torch_sparse for efficient operations
try:
    import torch_sparse
    HAS_TORCH_SPARSE = True
except ImportError:
    HAS_TORCH_SPARSE = False


class StandardGRNPredictor(BasePredictor):
    """
    Standard GRN Predictor module.
    
    Infers gene regulatory networks using:
    1. Attention-based inference from gene encoder
    2. Embedding similarity-based inference
    3. Low-rank factorization for efficiency
    4. Degree scaling to prevent hub gene dominance
    """
    
    def __init__(
        self,
        n_genes: int,
        gene_embedding_dim: int = 64,
        mode: str = 'attention',
        temperature: float = 1.0,
        combination_weight: float = 0.5,
        # MLP parameters
        use_mlp: bool = False,
        mlp_hidden_dim: int = 64,
        # Score integration
        dynamic_score_mode: Optional[str] = 'multiply_weight',
        dynamic_score_transform: str = 'sigmoid',
        # Degree scaling
        use_degree_scaling: bool = True,
        degree_scaling_temperature: float = 0.25,
        # Sparsification
        top_k_edges_per_gene: Optional[int] = 50,
        # Additional parameters
        dropout: float = 0.1,
        activation: str = "relu",
        **kwargs
    ):
        """
        Initialize the StandardGRNPredictor.
        
        Args:
            n_genes: Number of genes
            gene_embedding_dim: Dimension of gene embeddings
            mode: GRN inference mode ('attention', 'embedding_similarity', 'low_rank', or 'both')
            temperature: Temperature for softmax in attention mode
            combination_weight: Weight for combining modes (if mode='both')
            use_mlp: Whether to use MLP for embedding-based inference
            mlp_hidden_dim: Hidden dimension of MLP
            dynamic_score_mode: How to incorporate scores ('multiply_weight', 'mlp_concat', None)
            dynamic_score_transform: Transform for scores ('sigmoid', 'softplus', 'none')
            use_degree_scaling: Whether to apply degree scaling
            degree_scaling_temperature: Temperature for degree scaling
            top_k_edges_per_gene: Number of top edges to keep per gene
            dropout: Dropout probability
            activation: Activation function name
        """
        self.n_genes = n_genes
        self.gene_embedding_dim = gene_embedding_dim
        self.mode = mode.lower()
        self.temperature = temperature
        self.combination_weight = combination_weight
        self.use_mlp = use_mlp
        self.mlp_hidden_dim = mlp_hidden_dim
        self.dynamic_score_mode = dynamic_score_mode
        self.dynamic_score_transform = dynamic_score_transform
        self.use_degree_scaling = use_degree_scaling
        self.degree_scaling_temperature = degree_scaling_temperature
        self.top_k_edges_per_gene = top_k_edges_per_gene
        self.dropout = dropout
        self.activation_name = activation
        
        super().__init__(
            n_genes=n_genes,
            gene_embedding_dim=gene_embedding_dim,
            mode=mode,
            temperature=temperature,
            combination_weight=combination_weight,
            use_mlp=use_mlp,
            mlp_hidden_dim=mlp_hidden_dim,
            dynamic_score_mode=dynamic_score_mode,
            dynamic_score_transform=dynamic_score_transform,
            use_degree_scaling=use_degree_scaling,
            degree_scaling_temperature=degree_scaling_temperature,
            top_k_edges_per_gene=top_k_edges_per_gene,
            dropout=dropout,
            activation=activation,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.gene_embedding_dim > 0, "gene_embedding_dim must be positive"
        assert self.mode in ['attention', 'embedding_similarity', 'low_rank', 'both'], \
            f"Unknown mode: {self.mode}"
        assert self.temperature > 0, "temperature must be positive"
        assert 0 <= self.combination_weight <= 1, "combination_weight must be in [0, 1]"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
        
        if self.dynamic_score_mode is not None:
            assert self.dynamic_score_mode in ['multiply_weight', 'mlp_concat'], \
                f"Unknown dynamic_score_mode: {self.dynamic_score_mode}"
    
    def _build(self):
        """Build the predictor architecture."""
        # Low-rank factorization for efficient inference
        if self.mode == 'low_rank':
            self.low_rank_dim = min(256, self.n_genes // 10, self.gene_embedding_dim)
            self.source_proj = nn.Linear(self.gene_embedding_dim, self.low_rank_dim)
            self.target_proj = nn.Linear(self.gene_embedding_dim, self.low_rank_dim)
            # Small initialization for sparse initial predictions
            nn.init.xavier_normal_(self.source_proj.weight, gain=0.1)
            nn.init.xavier_normal_(self.target_proj.weight, gain=0.1)
        
        # MLP for embedding-based inference
        if self.use_mlp:
            mlp_input_dim = 2 * self.gene_embedding_dim
            
            # Adjust input dimension for dynamic scores
            if self.dynamic_score_mode == 'mlp_concat':
                mlp_input_dim += 2  # Add source and target scores
            
            self.embedding_mlp = nn.Sequential(
                nn.Linear(mlp_input_dim, self.mlp_hidden_dim),
                self._get_activation(),
                nn.Dropout(self.dropout),
                nn.Linear(self.mlp_hidden_dim, self.mlp_hidden_dim // 2),
                self._get_activation(),
                nn.Dropout(self.dropout),
                nn.Linear(self.mlp_hidden_dim // 2, 1)
            )
        
        # Score transformation
        if self.dynamic_score_transform == 'sigmoid':
            self.score_transform = nn.Sigmoid()
        elif self.dynamic_score_transform == 'softplus':
            self.score_transform = nn.Softplus()
        else:
            self.score_transform = nn.Identity()
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'elu': nn.ELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'tanh': nn.Tanh()
        }
        return activations.get(self.activation_name, nn.ReLU())
    
    def predict(
        self,
        gene_embeddings: torch.Tensor,
        attention_weights: Optional[List[Tuple[torch.Tensor, torch.Tensor]]] = None,
        dynamic_scores: Optional[torch.Tensor] = None
    ) -> PredictorOutputs:
        """
        Predict gene regulatory network.
        
        Args:
            gene_embeddings: Gene embeddings [n_genes, embedding_dim]
            attention_weights: List of (edge_index, weights) from gene encoder
            dynamic_scores: Dynamic importance scores [n_genes]
            
        Returns:
            PredictorOutputs with GRN predictions
        """
        device = gene_embeddings.device
        n_genes = gene_embeddings.shape[0]
        
        # Get GRN based on mode
        if self.mode == 'attention':
            if attention_weights is None:
                raise ValueError("Attention weights required for attention mode")
            edge_index, edge_weight = self._process_attention_weights(attention_weights)
            
        elif self.mode == 'embedding_similarity':
            edge_index, edge_weight = self._compute_embedding_similarity(gene_embeddings)
            
        elif self.mode == 'low_rank':
            edge_index, edge_weight = self._compute_low_rank_grn(gene_embeddings)
            
        elif self.mode == 'both':
            # Combine attention and embedding-based approaches
            if attention_weights is not None:
                attn_edge_index, attn_edge_weight = self._process_attention_weights(attention_weights)
            else:
                warnings.warn("No attention weights provided, using only embedding similarity")
                attn_edge_index = torch.tensor([[],[]], dtype=torch.long, device=device)
                attn_edge_weight = torch.tensor([], dtype=torch.float, device=device)
            
            emb_edge_index, emb_edge_weight = self._compute_embedding_similarity(gene_embeddings)
            
            # Combine edges
            edge_index, edge_weight = self._combine_edges(
                attn_edge_index, attn_edge_weight,
                emb_edge_index, emb_edge_weight
            )
        
        else:
            raise ValueError(f"Unknown mode: {self.mode}")
        
        # Apply dynamic scores if provided
        if dynamic_scores is not None and self.dynamic_score_mode == 'multiply_weight':
            # Debug shapes
            if edge_weight.dim() > 1:
                warnings.warn(f"Edge weight before dynamic scores has wrong shape: {edge_weight.shape}")
            edge_weight = self._apply_dynamic_scores(
                edge_index, edge_weight, dynamic_scores
            )
            if edge_weight.dim() > 1:
                warnings.warn(f"Edge weight after dynamic scores has wrong shape: {edge_weight.shape}")
        
        # Apply degree scaling
        if self.use_degree_scaling and edge_weight.numel() > 0:
            edge_weight = self._apply_degree_scaling(edge_index, edge_weight, n_genes)
        
        # Sparsify if requested
        if self.top_k_edges_per_gene is not None and edge_weight.numel() > 0:
            try:
                edge_index, edge_weight = self._sparsify_grn(
                    edge_index, edge_weight, n_genes
                )
            except Exception as e:
                # If sparsification fails, use original edges
                warnings.warn(f"GRN sparsification failed: {e}. Using original edges.")
                # Ensure edge_weight is 1D
                if edge_weight.dim() > 1:
                    warnings.warn(f"Edge weight has wrong shape: {edge_weight.shape}")
                    # This shouldn't happen with correct implementation
        
        # Create dense adjacency matrix for output
        adj_matrix = torch.zeros(n_genes, n_genes, device=device)
        if edge_weight.numel() > 0:
            adj_matrix[edge_index[0], edge_index[1]] = edge_weight
        
        # Compute confidence scores
        confidence = self._compute_confidence(edge_weight) if edge_weight.numel() > 0 else None
        
        return PredictorOutputs(
            predictions={
                'edge_index': edge_index,
                'edge_weight': edge_weight,
                'adjacency_matrix': adj_matrix
            },
            confidence=confidence,
            auxiliary={
                'n_edges': edge_index.shape[1] if edge_index.numel() > 0 else 0,
                'mean_weight': edge_weight.mean().item() if edge_weight.numel() > 0 else 0.0,
                'sparsity': 1.0 - (edge_index.shape[1] / (n_genes * n_genes)) if edge_index.numel() > 0 else 1.0
            }
        )
    
    def _process_attention_weights(
        self,
        attention_details: List[Tuple[torch.Tensor, torch.Tensor]]
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Process attention weights from gene encoder."""
        if not attention_details:
            raise ValueError("Empty attention details")
        
        # Use last layer attention
        edge_index, attention_weights = attention_details[-1]
        
        # Handle multi-head attention
        if attention_weights.dim() > 1 and attention_weights.shape[1] > 1:
            attention_weights = attention_weights.mean(dim=1)
        elif attention_weights.dim() > 1:
            attention_weights = attention_weights.squeeze(1)
        
        # Apply temperature scaling
        attention_weights = attention_weights / self.temperature
        
        return edge_index, attention_weights
    
    def _compute_embedding_similarity(
        self,
        gene_embeddings: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute GRN based on embedding similarity."""
        n_genes = gene_embeddings.shape[0]
        device = gene_embeddings.device
        
        if self.use_mlp:
            # Use MLP to compute pairwise regulatory scores
            edge_list = []
            weight_list = []
            
            # Process in batches for memory efficiency
            batch_size = min(100, n_genes)
            
            for i in range(0, n_genes, batch_size):
                end_i = min(i + batch_size, n_genes)
                source_batch = gene_embeddings[i:end_i]  # [batch, dim]
                
                for j in range(0, n_genes, batch_size):
                    end_j = min(j + batch_size, n_genes)
                    target_batch = gene_embeddings[j:end_j]  # [batch, dim]
                    
                    # Compute pairwise features
                    source_expanded = source_batch.unsqueeze(1).expand(-1, end_j-j, -1)
                    target_expanded = target_batch.unsqueeze(0).expand(end_i-i, -1, -1)
                    
                    # Concatenate source and target features
                    pair_features = torch.cat([source_expanded, target_expanded], dim=-1)
                    pair_features = pair_features.reshape(-1, 2 * self.gene_embedding_dim)
                    
                    # Get regulatory scores
                    scores = self.embedding_mlp(pair_features).squeeze(-1)
                    scores = torch.sigmoid(scores)
                    
                    # Create edges for scores above threshold
                    scores_matrix = scores.reshape(end_i-i, end_j-j)
                    mask = scores_matrix > 0.1  # Threshold
                    
                    if mask.any():
                        indices = torch.nonzero(mask, as_tuple=True)
                        source_indices = indices[0] + i
                        target_indices = indices[1] + j
                        
                        edge_list.append(torch.stack([source_indices, target_indices]))
                        weight_list.append(scores_matrix[mask])
            
            if edge_list:
                edge_index = torch.cat(edge_list, dim=1)
                edge_weight = torch.cat(weight_list)
            else:
                edge_index = torch.tensor([[],[]], dtype=torch.long, device=device)
                edge_weight = torch.tensor([], dtype=torch.float, device=device)
        
        else:
            # Simple cosine similarity
            gene_embeddings_norm = F.normalize(gene_embeddings, p=2, dim=1)
            similarity_matrix = torch.mm(gene_embeddings_norm, gene_embeddings_norm.t())
            
            # Remove self-loops
            similarity_matrix.fill_diagonal_(0)
            
            # Keep only positive similarities
            mask = similarity_matrix > 0
            if mask.any():
                edge_index = torch.nonzero(mask, as_tuple=False).t()
                edge_weight = similarity_matrix[mask]
                
                # Debug: check if indices are valid
                assert edge_index.max() < n_genes, \
                    f"Invalid edge indices: max={edge_index.max().item()}, n_genes={n_genes}"
            else:
                edge_index = torch.tensor([[],[]], dtype=torch.long, device=device)
                edge_weight = torch.tensor([], dtype=torch.float, device=device)
        
        return edge_index, edge_weight
    
    def _compute_low_rank_grn(
        self,
        gene_embeddings: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute GRN using low-rank factorization."""
        # Project to low-rank space
        source_features = self.source_proj(gene_embeddings)  # [n_genes, low_rank]
        target_features = self.target_proj(gene_embeddings)  # [n_genes, low_rank]
        
        # Compute regulatory scores
        scores = torch.mm(source_features, target_features.t())  # [n_genes, n_genes]
        scores = torch.sigmoid(scores)
        
        # Remove self-loops
        scores.fill_diagonal_(0)
        
        # Threshold and create edges
        mask = scores > 0.1
        edge_index = torch.nonzero(mask, as_tuple=False).t()
        edge_weight = scores[mask]
        
        return edge_index, edge_weight
    
    def _combine_edges(
        self,
        edge_index1: torch.Tensor, edge_weight1: torch.Tensor,
        edge_index2: torch.Tensor, edge_weight2: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Combine edges from two sources."""
        device = edge_index1.device if edge_index1.numel() > 0 else edge_index2.device
        
        # Handle empty cases
        if edge_index1.numel() == 0:
            return edge_index2, edge_weight2
        if edge_index2.numel() == 0:
            return edge_index1, edge_weight1
        
        # Combine edges
        all_edges = torch.cat([edge_index1, edge_index2], dim=1)
        all_weights = torch.cat([
            edge_weight1 * self.combination_weight,
            edge_weight2 * (1 - self.combination_weight)
        ])
        
        # Remove duplicates by averaging weights
        unique_edges, inverse_indices = torch.unique(all_edges, dim=1, return_inverse=True)
        
        # Average weights for duplicate edges
        unique_weights = torch.zeros(unique_edges.shape[1], device=device)
        unique_weights.scatter_add_(0, inverse_indices, all_weights)
        counts = torch.zeros(unique_edges.shape[1], device=device)
        counts.scatter_add_(0, inverse_indices, torch.ones_like(all_weights))
        unique_weights = unique_weights / counts.clamp(min=1)
        
        return unique_edges, unique_weights
    
    def _apply_dynamic_scores(
        self,
        edge_index: torch.Tensor,
        edge_weight: torch.Tensor,
        dynamic_scores: torch.Tensor
    ) -> torch.Tensor:
        """Apply dynamic scores to edge weights."""
        # Transform scores
        scores = self.score_transform(dynamic_scores)
        
        # Ensure scores is the right shape (should be [n_genes] or [n_genes, 1])
        if scores.dim() > 1:
            scores = scores.squeeze(-1)
        
        # Get source and target scores
        source_scores = scores[edge_index[0]]
        target_scores = scores[edge_index[1]]
        
        # Combine scores (geometric mean)
        combined_scores = torch.sqrt(source_scores * target_scores)
        
        # Apply to weights
        return edge_weight * combined_scores
    
    def _apply_degree_scaling(
        self,
        edge_index: torch.Tensor,
        edge_weight: torch.Tensor,
        n_nodes: int
    ) -> torch.Tensor:
        """Apply degree scaling to prevent hub dominance."""
        # Compute out-degrees
        degrees = torch.zeros(n_nodes, device=edge_index.device)
        degrees.scatter_add_(0, edge_index[0], torch.ones_like(edge_index[0], dtype=torch.float))
        degrees = degrees + 1e-6  # Avoid division by zero
        
        # Get source degrees
        source_degrees = degrees[edge_index[0]]
        
        # Apply scaling: reduce weights for high-degree nodes
        scaling_factor = torch.sqrt(source_degrees)
        scaled_weights = edge_weight * torch.exp(-scaling_factor * self.degree_scaling_temperature)
        
        # Re-normalize per source node
        unique_sources = torch.unique(edge_index[0])
        normalized_weights = torch.zeros_like(scaled_weights)
        
        for source in unique_sources:
            mask = edge_index[0] == source
            if mask.any():
                source_weights = scaled_weights[mask]
                normalized_weights[mask] = F.softmax(source_weights, dim=0)
        
        return normalized_weights
    
    def _sparsify_grn(
        self,
        edge_index: torch.Tensor,
        edge_weight: torch.Tensor,
        n_genes: int
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Keep only top-k edges per gene."""
        if self.top_k_edges_per_gene is None:
            return edge_index, edge_weight
        
        # Ensure edge_weight is 1D
        if edge_weight.dim() > 1:
            raise ValueError(f"edge_weight must be 1D, got shape {edge_weight.shape}")
            
        # Ensure indices are valid
        if edge_index.numel() > 0:
            max_idx = edge_index.max().item()
            if max_idx >= n_genes:
                raise ValueError(f"Invalid edge indices: max={max_idx}, n_genes={n_genes}")
        
        # Process each source gene
        new_edges = []
        new_weights = []
        
        for gene_id in range(n_genes):
            mask = edge_index[0] == gene_id
            if not mask.any():
                continue
            
            gene_weights = edge_weight[mask]
            gene_targets = edge_index[1, mask]
            
            # Keep top-k
            k = min(self.top_k_edges_per_gene, gene_weights.shape[0])
            if k == 0:
                continue
                
            top_k_values, top_k_indices = torch.topk(gene_weights, k)
            
            # Add to new edges
            sources = torch.full((k,), gene_id, device=edge_index.device)
            targets = gene_targets[top_k_indices]
            
            new_edges.append(torch.stack([sources, targets]))
            new_weights.append(top_k_values)
        
        if new_edges:
            edge_index = torch.cat(new_edges, dim=1)
            edge_weight = torch.cat(new_weights)
        else:
            edge_index = torch.tensor([[],[]], dtype=torch.long, device=edge_index.device)
            edge_weight = torch.tensor([], dtype=torch.float, device=edge_index.device)
        
        return edge_index, edge_weight
    
    def _compute_confidence(self, edge_weight: torch.Tensor) -> torch.Tensor:
        """Compute confidence scores for predictions."""
        # Simple confidence based on weight distribution
        if edge_weight.numel() == 0:
            return torch.tensor(0.0)
        
        # Normalize weights to [0, 1]
        min_weight = edge_weight.min()
        max_weight = edge_weight.max()
        
        if max_weight > min_weight:
            normalized = (edge_weight - min_weight) / (max_weight - min_weight)
        else:
            normalized = torch.ones_like(edge_weight)
        
        # Confidence is mean of normalized weights
        return normalized.mean()
    
    def forward(
        self,
        gene_embeddings: torch.Tensor,
        attention_weights: Optional[List] = None,
        dynamic_scores: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for compatibility.
        
        Returns:
            Tuple of (edge_index, edge_weight)
        """
        outputs = self.predict(gene_embeddings, attention_weights, dynamic_scores)
        return (
            outputs.predictions['edge_index'],
            outputs.predictions['edge_weight']
        )