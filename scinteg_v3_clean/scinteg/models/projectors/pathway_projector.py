"""
Standardized Pathway Projector for ScINTEG v3.

This module projects cell embeddings onto pathway space using biological
pathway knowledge as an interpretable bottleneck.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any

from ...core.base import BaseProjector
from ...core.interfaces import ProjectorOutputs
from .masked_projection import MaskedProjection


class StandardPathwayProjector(BaseProjector):
    """
    Standard Pathway Projector module.
    
    Projects cell embeddings to pathway space through:
    1. Cell embedding to gene space mapping
    2. Masked projection to pathways using biological knowledge
    3. Activation and temperature scaling
    """
    
    def __init__(
        self,
        cell_dim: int,
        n_genes: int,
        n_pathways: int,
        pathway_gene_mask: torch.Tensor,
        use_soft_mask: bool = False,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
        activation: str = "elu",
        temperature_init: float = 10.0,
        **kwargs
    ):
        """
        Initialize the StandardPathwayProjector.
        
        Args:
            cell_dim: Dimension of cell embeddings from cell encoder
            n_genes: Number of genes
            n_pathways: Number of pathways
            pathway_gene_mask: Binary matrix [n_pathways, n_genes] defining associations
            use_soft_mask: Whether to use a learnable soft mask
            hidden_dim: Optional dimension for hidden layer
            dropout: Dropout probability
            activation: Activation function name
            temperature_init: Initial temperature value
        """
        self.cell_dim = cell_dim
        self.n_genes = n_genes
        self.n_pathways = n_pathways
        self.use_soft_mask = use_soft_mask
        self.hidden_dim = hidden_dim or (cell_dim + n_genes) // 2
        self.dropout = dropout
        self.activation_name = activation
        self.temperature_init = temperature_init
        
        # Store mask in config for reconstruction
        self.pathway_gene_mask = pathway_gene_mask
        
        super().__init__(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            use_soft_mask=use_soft_mask,
            hidden_dim=self.hidden_dim,
            dropout=dropout,
            activation=activation,
            temperature_init=temperature_init,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.cell_dim > 0, "cell_dim must be positive"
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.n_pathways > 0, "n_pathways must be positive"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
        assert self.temperature_init > 0, "temperature_init must be positive"
        
        # Validate mask shape
        assert self.pathway_gene_mask.shape == (self.n_pathways, self.n_genes), \
            f"pathway_gene_mask shape mismatch: expected ({self.n_pathways}, {self.n_genes}), " \
            f"got {self.pathway_gene_mask.shape}"
    
    def _build(self):
        """Build the projector architecture."""
        # Cell to gene space mapping
        self.cell_to_gene_map = nn.Sequential(
            nn.Linear(self.cell_dim, self.hidden_dim),
            nn.BatchNorm1d(self.hidden_dim),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.n_genes)
        )
        
        # Pathway projection with mask
        self.pathway_projection = MaskedProjection(
            in_features=self.n_genes,
            out_features=self.n_pathways,
            mask=self.pathway_gene_mask,
            soft_mask=self.use_soft_mask
        )
        
        # Activation
        self.activation = self._get_activation()
        
        # Temperature parameter for output scaling
        self.temperature = nn.Parameter(torch.tensor(self.temperature_init))
        
        # Output normalization
        self.output_norm = nn.LayerNorm(self.n_pathways)
        
        # Optional pathway importance prediction
        self.importance_head = nn.Sequential(
            nn.Linear(self.n_pathways, self.n_pathways // 2),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.n_pathways // 2, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'elu': nn.ELU(alpha=1.0),
            'gelu': nn.GELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'tanh': nn.Tanh()
        }
        return activations.get(self.activation_name, nn.ELU())
    
    def _init_weights(self):
        """Initialize weights for better gradient flow."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=1.414)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def project(
        self,
        cell_embeddings: torch.Tensor,
        return_intermediate: bool = False
    ) -> ProjectorOutputs:
        """
        Project cell embeddings to pathway space.
        
        Args:
            cell_embeddings: Cell embeddings [batch_size, cell_dim]
            return_intermediate: Whether to return intermediate representations
            
        Returns:
            ProjectorOutputs with pathway embeddings and metadata
        """
        batch_size = cell_embeddings.shape[0]
        
        # Map cell embeddings to gene space
        z_c_to_g = self.cell_to_gene_map(cell_embeddings)
        
        # Project to pathway space using masked projection
        z_p_raw = self.pathway_projection(z_c_to_g)
        
        # Apply activation
        z_p_activated = self.activation(z_p_raw)
        
        # Normalize and apply temperature scaling
        z_p_norm = self.output_norm(z_p_activated)
        z_p = z_p_norm * self.temperature
        
        # Compute pathway importance scores
        importance = self.importance_head(z_p_activated)
        
        # Prepare outputs
        outputs = ProjectorOutputs(
            projections=z_p,
            auxiliary={
                'pathway_importance': importance,
                'temperature': self.temperature.item(),
                'mask_sparsity': self.pathway_projection.get_sparsity(),
                'current_mask': self.pathway_projection.get_current_mask()
            }
        )
        
        if return_intermediate:
            outputs.auxiliary['gene_space'] = z_c_to_g
            outputs.auxiliary['pathway_raw'] = z_p_raw
            outputs.auxiliary['pathway_activated'] = z_p_activated
        
        return outputs
    
    def forward(self, z_c: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for backward compatibility.
        
        Args:
            z_c: Cell embeddings [batch_size, cell_dim]
            
        Returns:
            Pathway embeddings [batch_size, n_pathways]
        """
        outputs = self.project(z_c)
        return outputs.projections
    
    def get_current_mask(self) -> torch.Tensor:
        """Get the current pathway-gene mask."""
        return self.pathway_projection.get_current_mask()
    
    def get_pathway_gene_associations(self) -> Dict[int, torch.Tensor]:
        """
        Get gene indices associated with each pathway.
        
        Returns:
            Dictionary mapping pathway index to associated gene indices
        """
        mask = self.get_current_mask()
        associations = {}
        
        for pathway_idx in range(self.n_pathways):
            # Get genes with non-zero mask values
            gene_indices = torch.where(mask[pathway_idx] > 0.01)[0]
            associations[pathway_idx] = gene_indices
        
        return associations
    
    def get_config(self) -> Dict[str, Any]:
        """Get configuration for reconstruction."""
        config = super().get_config()
        # Don't include the mask tensor in config, it's too large
        config.pop('pathway_gene_mask', None)
        return config