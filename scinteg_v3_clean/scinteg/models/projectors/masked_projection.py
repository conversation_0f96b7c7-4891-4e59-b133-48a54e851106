"""
Masked Projection layer for ScINTEG v3.

This module implements a masked linear projection where a binary or continuous
mask controls which connections are allowed between input and output units.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional


class MaskedProjection(nn.Module):
    """
    Masked Projection layer with optional learnable soft mask.
    
    This layer enables biologically-informed projections by constraining
    connections based on prior knowledge (e.g., pathway-gene associations).
    """
    
    def __init__(
        self,
        in_features: int,
        out_features: int,
        mask: torch.Tensor,
        soft_mask: bool = False,
        bias: bool = True,
        init_gain: float = 2.0
    ):
        """
        Initialize the Masked Projection layer.
        
        Args:
            in_features: Size of each input sample
            out_features: Size of each output sample
            mask: Binary matrix [out_features, in_features] defining connections
            soft_mask: Whether to use a learnable soft mask
            bias: Whether to learn an additive bias
            init_gain: Gain for weight initialization
        """
        super().__init__()
        
        self.in_features = in_features
        self.out_features = out_features
        self.soft_mask = soft_mask
        
        # Initialize weights with <PERSON>/G<PERSON> initialization
        self.weight = nn.Parameter(torch.empty(out_features, in_features))
        nn.init.xavier_uniform_(self.weight, gain=init_gain)
        
        # Initialize bias if needed
        if bias:
            self.bias = nn.Parameter(torch.zeros(out_features))
        else:
            self.register_parameter('bias', None)
        
        # Initialize mask (either learnable or fixed)
        if soft_mask:
            # For soft mask, initialize with binary mask values transformed to logits
            self.learnable_mask = nn.Parameter(torch.empty(out_features, in_features))
            with torch.no_grad():
                # Convert binary mask to logits
                # sigmoid(3.0) ≈ 0.95 and sigmoid(-3.0) ≈ 0.05
                mask_float = mask.float()
                self.learnable_mask.data = mask_float * 6.0 - 3.0
        else:
            # For hard mask, register as buffer (non-learnable)
            self.register_buffer('fixed_mask', mask.float())
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass applying the masked projection.
        
        Args:
            x: Input tensor [batch_size, in_features]
            
        Returns:
            Output tensor [batch_size, out_features]
        """
        # Get current mask
        if self.soft_mask:
            # Apply sigmoid to convert logits to probabilities [0, 1]
            mask = torch.sigmoid(self.learnable_mask)
        else:
            mask = self.fixed_mask
        
        # Apply mask to weights and perform linear transformation
        masked_weight = self.weight * mask
        output = F.linear(x, masked_weight, self.bias)
        
        return output
    
    def get_current_mask(self) -> torch.Tensor:
        """
        Get the current mask (either learnable or fixed).
        
        Returns:
            The current mask tensor (sigmoid-activated if soft mask)
        """
        if self.soft_mask:
            return torch.sigmoid(self.learnable_mask)
        else:
            return self.fixed_mask
    
    def get_sparsity(self) -> float:
        """
        Calculate the sparsity of the current mask.
        
        Returns:
            Sparsity ratio (percentage of zero connections)
        """
        mask = self.get_current_mask()
        threshold = 0.01 if self.soft_mask else 0.5
        sparsity = (mask < threshold).float().mean().item()
        return sparsity
    
    def extra_repr(self) -> str:
        """String representation of the layer."""
        return f'in_features={self.in_features}, out_features={self.out_features}, soft_mask={self.soft_mask}'