"""
Hierarchical Pathway Projector for ScINTEG v3.

This module implements a two-level hierarchical projection that reduces
information loss by introducing meta-pathways.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any

from ...core.base import BaseProjector
from ...core.interfaces import ProjectorOutputs
from .masked_projection import MaskedProjection


class HierarchicalPathwayProjector(BaseProjector):
    """
    Hierarchical projection from cell embeddings to pathways.
    
    Key innovation: Two-level hierarchy with meta-pathways to reduce information loss:
    1. Fine-grained pathways capture specific biological processes
    2. Meta-pathways capture broader functional modules
    3. Skip connections preserve information across levels
    """
    
    def __init__(
        self,
        cell_dim: int,
        n_genes: int,
        n_pathways: int,
        n_meta_pathways: int,
        pathway_gene_mask: torch.Tensor,
        use_soft_mask: bool = True,
        hidden_dim: Optional[int] = None,
        dropout: float = 0.1,
        activation: str = "gelu",
        pathway_temperature_init: float = 5.0,
        meta_temperature_init: float = 10.0,
        use_skip_connections: bool = True,
        **kwargs
    ):
        """
        Initialize the HierarchicalPathwayProjector.
        
        Args:
            cell_dim: Dimension of cell embeddings
            n_genes: Number of genes
            n_pathways: Number of biological pathways
            n_meta_pathways: Number of meta-pathways (coarse-grained)
            pathway_gene_mask: Binary mask [n_pathways, n_genes]
            use_soft_mask: Whether to use learnable soft masks
            hidden_dim: Hidden dimension for transformations
            dropout: Dropout probability
            activation: Activation function name
            pathway_temperature_init: Initial temperature for pathways
            meta_temperature_init: Initial temperature for meta-pathways
            use_skip_connections: Whether to use skip connections
        """
        self.cell_dim = cell_dim
        self.n_genes = n_genes
        self.n_pathways = n_pathways
        self.n_meta_pathways = n_meta_pathways
        self.use_soft_mask = use_soft_mask
        self.hidden_dim = hidden_dim or max(cell_dim, n_genes)
        self.dropout = dropout
        self.activation_name = activation
        self.pathway_temperature_init = pathway_temperature_init
        self.meta_temperature_init = meta_temperature_init
        self.use_skip_connections = use_skip_connections
        
        # Store mask
        self.pathway_gene_mask = pathway_gene_mask
        
        super().__init__(
            cell_dim=cell_dim,
            n_genes=n_genes,
            n_pathways=n_pathways,
            n_meta_pathways=n_meta_pathways,
            use_soft_mask=use_soft_mask,
            hidden_dim=self.hidden_dim,
            dropout=dropout,
            activation=activation,
            pathway_temperature_init=pathway_temperature_init,
            meta_temperature_init=meta_temperature_init,
            use_skip_connections=use_skip_connections,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.cell_dim > 0, "cell_dim must be positive"
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.n_pathways > 0, "n_pathways must be positive"
        assert self.n_meta_pathways > 0, "n_meta_pathways must be positive"
        assert self.n_meta_pathways < self.n_pathways, "n_meta_pathways should be less than n_pathways"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
        
        # Validate mask shape
        assert self.pathway_gene_mask.shape == (self.n_pathways, self.n_genes), \
            f"pathway_gene_mask shape mismatch"
    
    def _build(self):
        """Build the projector architecture."""
        # Step 1: Map cell embeddings to gene space
        self.cell_to_gene_map = nn.Sequential(
            nn.Linear(self.cell_dim, self.hidden_dim),
            nn.BatchNorm1d(self.hidden_dim),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.n_genes)
        )
        
        # Step 2: Project genes to pathways with mask (fine-grained)
        self.gene_to_pathway = MaskedProjection(
            in_features=self.n_genes,
            out_features=self.n_pathways,
            mask=self.pathway_gene_mask,
            soft_mask=self.use_soft_mask,
            bias=True
        )
        
        # Step 3: Transform pathways (enhancement)
        self.pathway_transform = nn.Sequential(
            nn.Linear(self.n_pathways, self.n_pathways * 2),
            nn.LayerNorm(self.n_pathways * 2),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.n_pathways * 2, self.n_pathways)
        )
        
        # Step 4: Project pathways to meta-pathways (coarse-grained)
        self.pathway_to_meta = nn.Sequential(
            nn.Linear(self.n_pathways, self.hidden_dim),
            nn.LayerNorm(self.hidden_dim),
            self._get_activation(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.n_meta_pathways)
        )
        
        # Skip connection: Direct from cell embeddings to meta-pathways
        if self.use_skip_connections:
            self.cell_to_meta_skip = nn.Sequential(
                nn.Linear(self.cell_dim, self.hidden_dim),
                nn.LayerNorm(self.hidden_dim),
                self._get_activation(),
                nn.Dropout(self.dropout * 0.5),
                nn.Linear(self.hidden_dim, self.n_meta_pathways)
            )
            
            # Fusion weights for combining different paths
            self.fusion_weight = nn.Parameter(torch.ones(3) / 3)
        
        # Output normalization
        self.pathway_norm = nn.LayerNorm(self.n_pathways)
        self.meta_norm = nn.LayerNorm(self.n_meta_pathways)
        
        # Temperature parameters
        self.pathway_temperature = nn.Parameter(torch.tensor(self.pathway_temperature_init))
        self.meta_temperature = nn.Parameter(torch.tensor(self.meta_temperature_init))
        
        # Importance prediction heads
        self.pathway_importance = nn.Sequential(
            nn.Linear(self.n_pathways, self.n_pathways // 4),
            self._get_activation(),
            nn.Linear(self.n_pathways // 4, 1),
            nn.Sigmoid()
        )
        
        self.meta_importance = nn.Sequential(
            nn.Linear(self.n_meta_pathways, self.n_meta_pathways // 2),
            self._get_activation(),
            nn.Linear(self.n_meta_pathways // 2, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'elu': nn.ELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'silu': nn.SiLU()
        }
        return activations.get(self.activation_name, nn.GELU())
    
    def _init_weights(self):
        """Initialize weights for better gradient flow."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=1.414)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def project(
        self,
        cell_embeddings: torch.Tensor,
        return_both_levels: bool = True
    ) -> ProjectorOutputs:
        """
        Project cell embeddings through hierarchical pathway structure.
        
        Args:
            cell_embeddings: Cell embeddings [batch_size, cell_dim]
            return_both_levels: Whether to return both pathway levels
            
        Returns:
            ProjectorOutputs with hierarchical features
        """
        batch_size = cell_embeddings.shape[0]
        
        # Map cell embeddings to gene space
        z_c_to_g = self.cell_to_gene_map(cell_embeddings)
        
        # Project to pathway space using masked projection
        z_p_raw = self.gene_to_pathway(z_c_to_g)
        
        # Enhance pathway features with residual connection
        z_p_enhanced = z_p_raw + self.pathway_transform(z_p_raw)
        
        # Apply normalization and temperature to pathways
        z_p = self.pathway_norm(z_p_enhanced) * self.pathway_temperature
        
        # Project pathways to meta-pathways
        z_meta_from_pathways = self.pathway_to_meta(z_p_enhanced)
        
        if self.use_skip_connections:
            # Get skip connection from cells to meta-pathways
            z_meta_from_cells = self.cell_to_meta_skip(cell_embeddings)
            
            # Fuse different meta-pathway sources
            w = F.softmax(self.fusion_weight, dim=0)
            z_meta_combined = (
                w[0] * z_meta_from_pathways +
                w[1] * z_meta_from_cells +
                w[2] * (z_meta_from_pathways + z_meta_from_cells) / 2
            )
        else:
            z_meta_combined = z_meta_from_pathways
        
        # Apply normalization and temperature to meta-pathways
        z_meta = self.meta_norm(z_meta_combined) * self.meta_temperature
        
        # Compute importance scores
        pathway_importance = self.pathway_importance(z_p_enhanced)
        meta_importance = self.meta_importance(z_meta_combined)
        
        # Prepare outputs
        if return_both_levels:
            # For hierarchical mode, concatenate both levels
            combined_features = torch.cat([z_p, z_meta], dim=1)
            projections = combined_features
        else:
            # For compatibility mode, return only pathways
            projections = z_p
        
        outputs = ProjectorOutputs(
            projections=projections,
            meta_projections=z_meta,
            auxiliary={
                'pathway_features': z_p,
                'meta_features': z_meta,
                'pathway_importance': pathway_importance,
                'meta_importance': meta_importance,
                'pathway_temperature': self.pathway_temperature.item(),
                'meta_temperature': self.meta_temperature.item(),
                'mask_sparsity': self.gene_to_pathway.get_sparsity(),
                'current_mask': self.gene_to_pathway.get_current_mask()
            }
        )
        
        if self.use_skip_connections:
            outputs.auxiliary['fusion_weights'] = F.softmax(self.fusion_weight, dim=0).detach()
        
        return outputs
    
    def forward(
        self,
        z_c: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass for compatibility.
        
        Args:
            z_c: Cell embeddings [batch_size, cell_dim]
            
        Returns:
            Tuple of (pathway_features, meta_features)
        """
        outputs = self.project(z_c, return_both_levels=True)
        return (
            outputs.auxiliary['pathway_features'],
            outputs.auxiliary['meta_features']
        )
    
    def get_current_mask(self) -> torch.Tensor:
        """Get the current pathway-gene mask."""
        return self.gene_to_pathway.get_current_mask()
    
    def get_output_dim(self) -> int:
        """Get the output dimension based on configuration."""
        return self.n_pathways + self.n_meta_pathways