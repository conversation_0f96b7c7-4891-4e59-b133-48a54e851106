"""
Model components for ScINTEG v3.
"""

# Import all submodules
from . import encoders
from . import projectors  
from . import decoders
from . import predictors

# Import specific classes for convenience
from .encoders import StandardCellEncoder, StandardGeneEncoder
from .projectors import StandardPathwayProjector, HierarchicalPathwayProjector
from .decoders import StandardExpressionDecoder, StandardUNetDecoder
from .predictors import StandardGRNPredictor
from .scinteg import ScINTEGv3

__all__ = [
    # Submodules
    'encoders',
    'projectors',
    'decoders',
    'predictors',
    # Encoders
    'StandardCellEncoder',
    'StandardGeneEncoder',
    # Projectors
    'StandardPathwayProjector',
    'HierarchicalPathwayProjector',
    # Decoders
    'StandardExpressionDecoder',
    'StandardUNetDecoder',
    # Predictors
    'StandardGRNPredictor',
    # Main model
    'ScINTEGv3'
]