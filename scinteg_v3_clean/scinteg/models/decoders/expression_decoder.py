"""
Standardized Expression Decoder for ScINTEG v3.

This module reconstructs gene expression from pathway embeddings with
optional gene embedding modulation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Dict, Any, Union

from ...core.base import BaseDecoder
from ...core.interfaces import DecoderOutputs


class StandardExpressionDecoder(BaseDecoder):
    """
    Standard Expression Decoder module.
    
    Reconstructs gene expression from pathway embeddings with:
    1. Deep MLP transformation
    2. Optional gene embedding modulation
    3. Multiple loss type support (MSE, NB, ZINB)
    4. Skip connections for better gradient flow
    """
    
    def __init__(
        self,
        n_pathways: int,
        n_genes: int,
        gene_embedding_dim: int = 64,
        hidden_dims: Optional[list] = None,
        loss_type: str = 'mse',
        use_batch_norm: bool = True,
        dropout: float = 0.1,
        activation: str = "elu",
        # Gene modulation
        use_gene_modulation: bool = True,
        modulation_type: str = 'multiplicative',
        # Additional features
        use_skip_connection: bool = True,
        use_input_residual: bool = True,
        residual_weight_init: float = 0.5,
        **kwargs
    ):
        """
        Initialize the StandardExpressionDecoder.
        
        Args:
            n_pathways: Number of pathways (input dimension)
            n_genes: Number of genes (output dimension)
            gene_embedding_dim: Dimension of gene embeddings
            hidden_dims: List of hidden layer dimensions
            loss_type: Type of reconstruction loss ('mse', 'nb', 'zinb')
            use_batch_norm: Whether to use batch normalization
            dropout: Dropout probability
            activation: Activation function name
            use_gene_modulation: Whether to use gene embeddings
            modulation_type: Type of modulation ('multiplicative', 'additive', 'attention')
            use_skip_connection: Whether to use skip connections
            use_input_residual: Whether to add input residual
            residual_weight_init: Initial residual weight
        """
        self.n_pathways = n_pathways
        self.n_genes = n_genes
        self.gene_embedding_dim = gene_embedding_dim
        self.loss_type = loss_type.lower()
        self.use_batch_norm = use_batch_norm
        self.dropout = dropout
        self.activation_name = activation
        self.use_gene_modulation = use_gene_modulation
        self.modulation_type = modulation_type
        self.use_skip_connection = use_skip_connection
        self.use_input_residual = use_input_residual
        self.residual_weight_init = residual_weight_init
        
        # Default hidden dimensions
        if hidden_dims is None:
            hidden_dims = [2 * n_genes, int(1.5 * n_genes), n_genes]
        self.hidden_dims = hidden_dims
        
        super().__init__(
            n_pathways=n_pathways,
            n_genes=n_genes,
            gene_embedding_dim=gene_embedding_dim,
            hidden_dims=hidden_dims,
            loss_type=loss_type,
            use_batch_norm=use_batch_norm,
            dropout=dropout,
            activation=activation,
            use_gene_modulation=use_gene_modulation,
            modulation_type=modulation_type,
            use_skip_connection=use_skip_connection,
            use_input_residual=use_input_residual,
            residual_weight_init=residual_weight_init,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.n_pathways > 0, "n_pathways must be positive"
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.loss_type in ['mse', 'nb', 'zinb'], f"Unknown loss type: {self.loss_type}"
        assert self.modulation_type in ['multiplicative', 'additive', 'attention'], \
            f"Unknown modulation type: {self.modulation_type}"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
    
    def _build(self):
        """Build the decoder architecture."""
        # Build MLP layers
        layers = []
        
        # Input layer
        layers.append(nn.Linear(self.n_pathways, self.hidden_dims[0]))
        if self.use_batch_norm:
            layers.append(nn.BatchNorm1d(self.hidden_dims[0]))
        layers.append(self._get_activation())
        layers.append(nn.Dropout(self.dropout))
        
        # Hidden layers
        for i in range(len(self.hidden_dims) - 1):
            layers.append(nn.Linear(self.hidden_dims[i], self.hidden_dims[i+1]))
            if self.use_batch_norm:
                layers.append(nn.BatchNorm1d(self.hidden_dims[i+1]))
            layers.append(self._get_activation())
            layers.append(nn.Dropout(self.dropout))
        
        self.feature_layers = nn.Sequential(*layers)
        
        # Gene modulation components
        if self.use_gene_modulation:
            self._build_gene_modulation()
        
        # Output layers based on loss type
        final_hidden = self.hidden_dims[-1]
        self._build_output_heads(final_hidden)
        
        # Skip connection
        if self.use_skip_connection and self.n_pathways < self.n_genes:
            self.skip_projection = nn.Linear(self.n_pathways, self.n_genes)
        else:
            self.skip_projection = None
        
        # Input residual
        if self.use_input_residual:
            self.residual_weight = nn.Parameter(torch.tensor(self.residual_weight_init))
        
        self._init_weights()
    
    def _get_activation(self):
        """Get activation function."""
        activations = {
            'relu': nn.ReLU(),
            'elu': nn.ELU(alpha=1.0),
            'gelu': nn.GELU(),
            'leaky_relu': nn.LeakyReLU(0.2),
            'silu': nn.SiLU()
        }
        return activations.get(self.activation_name, nn.ELU())
    
    def _build_gene_modulation(self):
        """Build gene modulation components."""
        if self.modulation_type == 'multiplicative':
            self.gene_modulation = nn.Sequential(
                nn.Linear(self.gene_embedding_dim, self.gene_embedding_dim // 2),
                self._get_activation(),
                nn.Linear(self.gene_embedding_dim // 2, 1),
                nn.Sigmoid()
            )
        elif self.modulation_type == 'additive':
            self.gene_modulation = nn.Sequential(
                nn.Linear(self.gene_embedding_dim, self.gene_embedding_dim // 2),
                self._get_activation(),
                nn.Linear(self.gene_embedding_dim // 2, 1),
                nn.Tanh()
            )
        elif self.modulation_type == 'attention':
            embed_dim = self.hidden_dims[-1]
            num_heads = 4 if embed_dim % 4 == 0 else 1
            self.cross_attention = nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=self.dropout,
                batch_first=True
            )
            self.gene_projection = nn.Linear(self.gene_embedding_dim, embed_dim)
            self.attention_norm = nn.LayerNorm(embed_dim)
    
    def _build_output_heads(self, final_hidden: int):
        """Build output heads based on loss type."""
        if self.loss_type == 'nb':
            # Mean and dispersion for negative binomial
            self.mu_decoder = nn.Sequential(
                nn.Linear(final_hidden, self.n_genes),
                nn.Softplus()
            )
            self.theta_decoder = nn.Sequential(
                nn.Linear(final_hidden, self.n_genes),
                nn.Softplus()
            )
        elif self.loss_type == 'zinb':
            # Mean, dispersion, and dropout probability for ZINB
            self.mu_decoder = nn.Sequential(
                nn.Linear(final_hidden, self.n_genes),
                nn.Softplus()
            )
            self.theta_decoder = nn.Sequential(
                nn.Linear(final_hidden, self.n_genes),
                nn.Softplus()
            )
            self.pi_decoder = nn.Linear(final_hidden, self.n_genes)
        else:  # MSE
            self.decoder = nn.Sequential(
                nn.Linear(final_hidden, self.n_genes * 2),
                nn.LayerNorm(self.n_genes * 2),
                self._get_activation(),
                nn.Dropout(self.dropout * 0.5),
                nn.Linear(self.n_genes * 2, self.n_genes)
            )
    
    def _init_weights(self):
        """Initialize weights for better gradient flow."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight, gain=1.0)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.BatchNorm1d):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def decode(
        self,
        pathway_features: torch.Tensor,
        gene_embeddings: Optional[torch.Tensor] = None,
        input_expression: Optional[torch.Tensor] = None
    ) -> DecoderOutputs:
        """
        Decode pathway features to gene expression.
        
        Args:
            pathway_features: Pathway embeddings [batch_size, n_pathways]
            gene_embeddings: Gene embeddings [n_genes, gene_embedding_dim] (optional)
            input_expression: Original expression for residual [batch_size, n_genes] (optional)
            
        Returns:
            DecoderOutputs with reconstructed expression
        """
        batch_size = pathway_features.shape[0]
        
        # Process through feature layers
        features = self.feature_layers(pathway_features)
        
        # Apply gene modulation if available
        if self.use_gene_modulation and gene_embeddings is not None:
            features = self._apply_gene_modulation(features, gene_embeddings, batch_size)
        
        # Generate output based on loss type
        if self.loss_type == 'nb':
            mu = self.mu_decoder(features)
            theta = self.theta_decoder(features)
            
            # Apply gene modulation to outputs if needed
            if (self.use_gene_modulation and gene_embeddings is not None and 
                self.modulation_type in ['multiplicative', 'additive']):
                mu = self._modulate_output(mu, gene_embeddings, batch_size)
            
            reconstruction = mu  # Primary reconstruction is mean
            auxiliary = {'theta': theta}
            
        elif self.loss_type == 'zinb':
            mu = self.mu_decoder(features)
            theta = self.theta_decoder(features)
            pi_logits = self.pi_decoder(features)
            
            if (self.use_gene_modulation and gene_embeddings is not None and 
                self.modulation_type in ['multiplicative', 'additive']):
                mu = self._modulate_output(mu, gene_embeddings, batch_size)
            
            reconstruction = mu
            auxiliary = {'theta': theta, 'pi_logits': pi_logits}
            
        else:  # MSE
            reconstruction = self.decoder(features)
            
            # Apply skip connection
            if self.skip_projection is not None:
                skip_features = self.skip_projection(pathway_features)
                reconstruction = reconstruction + 0.1 * skip_features
            
            # Apply gene modulation
            if (self.use_gene_modulation and gene_embeddings is not None and 
                self.modulation_type in ['multiplicative', 'additive']):
                reconstruction = self._modulate_output(reconstruction, gene_embeddings, batch_size)
            
            auxiliary = {}
        
        # Apply input residual connection
        if self.use_input_residual and input_expression is not None:
            reconstruction = reconstruction + self.residual_weight * input_expression
        
        return DecoderOutputs(
            reconstruction=reconstruction,
            latent_features=[features],
            auxiliary=auxiliary
        )
    
    def _apply_gene_modulation(
        self,
        features: torch.Tensor,
        gene_embeddings: torch.Tensor,
        batch_size: int
    ) -> torch.Tensor:
        """Apply gene modulation to features."""
        if self.modulation_type == 'attention':
            # Project gene embeddings
            z_g_proj = self.gene_projection(gene_embeddings)
            z_g_proj = z_g_proj.unsqueeze(0).expand(batch_size, -1, -1)
            features_att = features.unsqueeze(1)
            
            # Apply cross-attention
            attended_features, _ = self.cross_attention(
                query=features_att,
                key=z_g_proj,
                value=z_g_proj
            )
            attended_features = attended_features.squeeze(1)
            
            # Residual connection and normalization
            features = self.attention_norm(features + attended_features)
        
        return features
    
    def _modulate_output(
        self,
        output: torch.Tensor,
        gene_embeddings: torch.Tensor,
        batch_size: int
    ) -> torch.Tensor:
        """Modulate output with gene factors."""
        gene_factors = self.gene_modulation(gene_embeddings).squeeze(-1)
        gene_factors = gene_factors.unsqueeze(0).expand(batch_size, -1)
        
        if self.modulation_type == 'multiplicative':
            # Scale by factors between 0.5 and 1.5
            output = output * (0.5 + gene_factors)
        else:  # additive
            # Add scaled factors
            output = output + output * gene_factors * 0.1
        
        return output
    
    def forward(
        self,
        z_p: torch.Tensor,
        z_g: Optional[torch.Tensor] = None,
        x_input: Optional[torch.Tensor] = None
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, ...]]:
        """
        Forward pass for compatibility.
        
        Args:
            z_p: Pathway embeddings [batch_size, n_pathways]
            z_g: Gene embeddings [n_genes, gene_embedding_dim] (optional)
            x_input: Original expression (optional)
            
        Returns:
            For MSE: reconstruction tensor
            For NB: (mu, theta) tuple
            For ZINB: (mu, theta, pi_logits) tuple
        """
        outputs = self.decode(z_p, z_g, x_input)
        
        if self.loss_type == 'nb':
            return outputs.reconstruction, outputs.auxiliary['theta']
        elif self.loss_type == 'zinb':
            return (outputs.reconstruction, 
                   outputs.auxiliary['theta'],
                   outputs.auxiliary['pi_logits'])
        else:
            return outputs.reconstruction