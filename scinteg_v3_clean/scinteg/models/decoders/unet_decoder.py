"""
U-Net Style Decoder for ScINTEG v3.

This module implements a U-Net inspired decoder that uses skip connections
and multi-scale processing for improved reconstruction quality.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Union, Tuple, Dict, Any

from ...core.base import BaseDecoder
from ...core.interfaces import DecoderOutputs


class ConvBlock(nn.Module):
    """Basic convolutional block with normalization and activation."""
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 1,
        groups: int = 1,
        use_batch_norm: bool = True,
        dropout: float = 0.1,
        activation: str = "gelu"
    ):
        super().__init__()
        
        self.conv = nn.Conv1d(
            in_channels, out_channels, kernel_size,
            padding=kernel_size // 2, groups=groups
        )
        self.norm = nn.BatchNorm1d(out_channels) if use_batch_norm else nn.Identity()
        
        # Activation
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'elu': nn.ELU(),
            'silu': nn.SiLU()
        }
        self.activation = activations.get(activation, nn.GELU())
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through conv block."""
        x = self.conv(x)
        x = self.norm(x)
        x = self.activation(x)
        x = self.dropout(x)
        return x


class AttentionBlock(nn.Module):
    """Self-attention block for feature refinement."""
    
    def __init__(self, dim: int, num_heads: int = 4, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(
            dim, num_heads, dropout=dropout, batch_first=True
        )
        self.norm = nn.LayerNorm(dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Apply self-attention."""
        # x shape: [batch, channels, length] -> [batch, length, channels]
        x = x.transpose(1, 2)
        
        # Self-attention with residual
        attn_out, _ = self.attention(x, x, x)
        x = self.norm(x + attn_out)
        
        # Back to [batch, channels, length]
        return x.transpose(1, 2)


class StandardUNetDecoder(BaseDecoder):
    """
    U-Net style decoder for gene expression reconstruction.
    
    Key features:
    - Encoder path: Progressively increases feature dimensions
    - Decoder path: Progressively reconstructs gene expression
    - Skip connections: Preserve fine-grained information
    - Multi-scale processing: Captures both local and global patterns
    - Optional attention mechanisms for feature refinement
    """
    
    def __init__(
        self,
        n_pathways: int,
        n_genes: int,
        pathway_gene_mask: Optional[torch.Tensor] = None,
        n_levels: int = 3,
        hidden_dims: Optional[List[int]] = None,
        use_skip_connections: bool = True,
        use_attention: bool = False,
        use_efficient_conv: bool = False,
        use_hierarchical_input: bool = False,
        n_meta_pathways: int = 0,
        dropout: float = 0.1,
        activation: str = "gelu",
        final_activation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize StandardUNetDecoder.
        
        Args:
            n_pathways: Number of pathways
            n_genes: Number of genes
            pathway_gene_mask: Binary mask [n_pathways, n_genes] (optional)
            n_levels: Number of U-Net levels
            hidden_dims: Hidden dimensions for each level
            use_skip_connections: Whether to use skip connections
            use_attention: Whether to use attention blocks
            use_efficient_conv: Use grouped convolutions for efficiency
            use_hierarchical_input: Accept both pathway and meta-pathway features
            n_meta_pathways: Number of meta-pathways (if hierarchical)
            dropout: Dropout probability
            activation: Activation function name
            final_activation: Final layer activation (None for linear)
        """
        self.n_pathways = n_pathways
        self.n_genes = n_genes
        self.n_levels = n_levels
        self.use_skip_connections = use_skip_connections
        self.use_attention = use_attention
        self.use_efficient_conv = use_efficient_conv
        self.use_hierarchical_input = use_hierarchical_input
        self.n_meta_pathways = n_meta_pathways
        self.dropout = dropout
        self.activation_name = activation
        self.final_activation_name = final_activation
        
        # Default hidden dimensions
        if hidden_dims is None:
            hidden_dims = [64 * (2**i) for i in range(n_levels)]
        self.hidden_dims = hidden_dims
        
        # Store mask if provided
        self.pathway_gene_mask = pathway_gene_mask
        
        super().__init__(
            n_pathways=n_pathways,
            n_genes=n_genes,
            n_levels=n_levels,
            hidden_dims=hidden_dims,
            use_skip_connections=use_skip_connections,
            use_attention=use_attention,
            use_efficient_conv=use_efficient_conv,
            use_hierarchical_input=use_hierarchical_input,
            n_meta_pathways=n_meta_pathways,
            dropout=dropout,
            activation=activation,
            final_activation=final_activation,
            **kwargs
        )
    
    def _validate_config(self):
        """Validate configuration parameters."""
        assert self.n_pathways > 0, "n_pathways must be positive"
        assert self.n_genes > 0, "n_genes must be positive"
        assert self.n_levels >= 1, "n_levels must be at least 1"
        assert len(self.hidden_dims) == self.n_levels, "hidden_dims length must match n_levels"
        assert 0 <= self.dropout < 1, "dropout must be in [0, 1)"
        
        if self.use_hierarchical_input:
            assert self.n_meta_pathways > 0, "n_meta_pathways must be positive for hierarchical input"
    
    def _build(self):
        """Build the U-Net decoder architecture."""
        # Input projection
        input_dim = self.n_pathways
        if self.use_hierarchical_input and self.n_meta_pathways > 0:
            input_dim = self.n_pathways + self.n_meta_pathways
        
        self.input_proj = nn.Linear(input_dim, self.hidden_dims[0])
        
        # Build encoder path (downsampling)
        self._build_encoder_path()
        
        # Bottleneck
        self.bottleneck = ConvBlock(
            self.hidden_dims[-1], self.hidden_dims[-1],
            kernel_size=3, dropout=self.dropout,
            activation=self.activation_name
        )
        
        if self.use_attention:
            self.bottleneck_attention = AttentionBlock(
                self.hidden_dims[-1], dropout=self.dropout
            )
        
        # Build decoder path (upsampling)
        self._build_decoder_path()
        
        # Final projection to gene space
        self.output_proj = nn.Conv1d(self.hidden_dims[0], self.n_genes, kernel_size=1)
        
        # Optional final activation
        if self.final_activation_name:
            activations = {
                'sigmoid': nn.Sigmoid(),
                'tanh': nn.Tanh(),
                'softplus': nn.Softplus()
            }
            self.final_activation = activations.get(self.final_activation_name, nn.Identity())
        else:
            self.final_activation = nn.Identity()
        
        # Register pathway mask if provided
        if self.pathway_gene_mask is not None:
            self.register_buffer('mask', self.pathway_gene_mask)
        
        self._init_weights()
    
    def _build_encoder_path(self):
        """Build encoder (downsampling) path."""
        self.encoder_blocks = nn.ModuleList()
        self.encoder_pools = nn.ModuleList()
        
        for i in range(self.n_levels):
            in_dim = self.hidden_dims[i] if i == 0 else self.hidden_dims[i-1]
            out_dim = self.hidden_dims[i]
            
            # Use grouped convolutions for efficiency if requested
            groups = min(in_dim, out_dim) // 4 if self.use_efficient_conv else 1
            
            block = ConvBlock(
                in_dim, out_dim, kernel_size=3, groups=groups,
                dropout=self.dropout, activation=self.activation_name
            )
            self.encoder_blocks.append(block)
            
            if i < self.n_levels - 1:
                # Pooling for downsampling
                pool = nn.Conv1d(out_dim, out_dim, kernel_size=2, stride=2)
                self.encoder_pools.append(pool)
    
    def _build_decoder_path(self):
        """Build decoder (upsampling) path."""
        self.decoder_blocks = nn.ModuleList()
        self.decoder_upsamples = nn.ModuleList()
        
        if self.use_skip_connections:
            self.skip_convs = nn.ModuleList()
        
        for i in range(self.n_levels - 1, 0, -1):
            # Upsample
            upsample = nn.ConvTranspose1d(
                self.hidden_dims[i], self.hidden_dims[i-1],
                kernel_size=2, stride=2
            )
            self.decoder_upsamples.append(upsample)
            
            # Decoder block
            in_dim = self.hidden_dims[i-1]
            if self.use_skip_connections:
                in_dim *= 2  # Skip connection doubles channels
                
                # 1x1 conv to combine skip connection
                skip_conv = nn.Conv1d(in_dim, self.hidden_dims[i-1], kernel_size=1)
                self.skip_convs.append(skip_conv)
            
            groups = min(self.hidden_dims[i-1], self.hidden_dims[i-1]) // 4 if self.use_efficient_conv else 1
            block = ConvBlock(
                self.hidden_dims[i-1], self.hidden_dims[i-1],
                kernel_size=3, groups=groups,
                dropout=self.dropout, activation=self.activation_name
            )
            self.decoder_blocks.append(block)
            
            if self.use_attention and i == self.n_levels - 1:
                # Add attention at highest resolution
                self.decoder_attention = AttentionBlock(
                    self.hidden_dims[i-1], dropout=self.dropout
                )
    
    def _init_weights(self):
        """Initialize weights for better gradient flow."""
        for module in self.modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.ConvTranspose1d)):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def decode(
        self,
        pathway_features: torch.Tensor,
        meta_features: Optional[torch.Tensor] = None
    ) -> DecoderOutputs:
        """
        Decode pathway features to gene expression using U-Net architecture.
        
        Args:
            pathway_features: Pathway embeddings [batch_size, n_pathways]
            meta_features: Meta-pathway features [batch_size, n_meta_pathways] (optional)
            
        Returns:
            DecoderOutputs with reconstructed expression
        """
        batch_size = pathway_features.shape[0]
        
        # Prepare input
        if self.use_hierarchical_input and meta_features is not None:
            x = torch.cat([pathway_features, meta_features], dim=1)
        else:
            x = pathway_features
        
        # Initial projection
        x = self.input_proj(x)  # [batch, hidden_dims[0]]
        
        # Expand spatial dimension to allow pooling
        # Calculate minimum spatial size needed
        min_spatial_size = 2 ** (self.n_levels - 1)
        x = x.unsqueeze(-1).expand(-1, -1, min_spatial_size)  # [batch, hidden_dims[0], min_spatial_size]
        
        # Encoder path with skip connections
        skip_features = []
        for i, (block, pool) in enumerate(zip(self.encoder_blocks[:-1], self.encoder_pools)):
            x = block(x)
            if self.use_skip_connections:
                skip_features.append(x)
            x = pool(x)
        
        # Last encoder block (no pooling)
        x = self.encoder_blocks[-1](x)
        
        # Bottleneck
        x = self.bottleneck(x)
        if self.use_attention:
            x = self.bottleneck_attention(x)
        
        # Decoder path
        latent_features = [x.squeeze(-1).mean(dim=1)]  # Store bottleneck features
        
        for i, (upsample, block) in enumerate(zip(self.decoder_upsamples, self.decoder_blocks)):
            x = upsample(x)
            
            if self.use_skip_connections:
                # Get corresponding skip connection
                skip = skip_features[-(i+1)]
                
                # Ensure spatial dimensions match
                if x.shape[-1] != skip.shape[-1]:
                    # Adjust size if needed
                    diff = skip.shape[-1] - x.shape[-1]
                    if diff > 0:
                        x = F.pad(x, (0, diff))
                    else:
                        skip = F.pad(skip, (0, -diff))
                
                # Concatenate and combine
                x = torch.cat([x, skip], dim=1)
                x = self.skip_convs[i](x)
            
            x = block(x)
            
            if hasattr(self, 'decoder_attention') and i == len(self.decoder_blocks) - 1:
                x = self.decoder_attention(x)
        
        # Final projection to gene space
        x = self.output_proj(x)  # [batch, n_genes, spatial]
        
        # Global pooling to remove spatial dimension
        reconstruction = x.mean(dim=-1)  # [batch, n_genes]
        
        # Apply final activation
        reconstruction = self.final_activation(reconstruction)
        
        # Apply mask if available
        if hasattr(self, 'mask'):
            # Create gene-wise scaling based on pathway connectivity
            gene_scaling = self.mask.sum(dim=0).float() / self.mask.sum(dim=0).max()
            gene_scaling = gene_scaling.unsqueeze(0)
            reconstruction = reconstruction * (0.5 + 0.5 * gene_scaling)
        
        return DecoderOutputs(
            reconstruction=reconstruction,
            latent_features=latent_features,
            auxiliary={
                'skip_features': skip_features if self.use_skip_connections else None,
                'final_features': x.squeeze(-1)
            }
        )
    
    def forward(
        self,
        z_p: torch.Tensor,
        z_meta: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Forward pass for compatibility.
        
        Args:
            z_p: Pathway features [batch_size, n_pathways]
            z_meta: Meta-pathway features (optional)
            
        Returns:
            Reconstructed expression [batch_size, n_genes]
        """
        outputs = self.decode(z_p, z_meta)
        return outputs.reconstruction