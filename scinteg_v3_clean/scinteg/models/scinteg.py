"""
ScINTEG v3: Standardized Single-cell Integrative Network and Gene regulation model.

This is the main model class that integrates all components for:
- Cell and gene embedding learning
- Gene regulatory network inference  
- Pathway activity analysis
- Expression reconstruction
- Temporal dynamics modeling (optional)
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, List, Tuple, Union, Any

from ..core.interfaces import ModelOutputs
from .encoders import StandardCellEncoder, StandardGeneEncoder
from .projectors import StandardPathwayProjector, HierarchicalPathwayProjector
from .decoders import StandardExpressionDecoder, StandardUNetDecoder
from .predictors import StandardGRNPredictor
from ..training.losses import ScINTEGLoss


class ScINTEGv3(nn.Module):
    """
    ScINTEG v3: Standardized architecture with improved modularity.
    
    Key improvements over v2:
    - Standardized interfaces for all components
    - Hierarchical pathway projection option
    - U-Net decoder option
    - Better configuration management
    - Improved error handling and validation
    """
    
    def __init__(
        self,
        # Core dimensions
        n_cells: int,
        n_genes: int,
        n_pathways: int,
        pathway_gene_mask: torch.Tensor,
        # Optional dimensions
        n_meta_pathways: Optional[int] = None,
        cell_input_dim: Optional[int] = None,
        # Embedding dimensions
        cell_embedding_dim: int = 128,
        gene_embedding_dim: int = 64,
        # Hidden dimensions
        cell_hidden_dim: int = 256,
        gene_hidden_dim: int = 128,
        # Architecture choices
        use_hierarchical_pathways: bool = False,
        use_unet_decoder: bool = False,
        decoder_type: str = 'expression',  # 'expression' or 'unet'
        # Component configurations
        cell_encoder_config: Optional[Dict[str, Any]] = None,
        gene_encoder_config: Optional[Dict[str, Any]] = None,
        projector_config: Optional[Dict[str, Any]] = None,
        decoder_config: Optional[Dict[str, Any]] = None,
        grn_predictor_config: Optional[Dict[str, Any]] = None,
        # Loss configuration
        reconstruction_loss_type: str = 'mse',
        # Other options
        dropout: float = 0.1,
        **kwargs
    ):
        """
        Initialize ScINTEG v3.
        
        Args:
            n_cells: Number of cells
            n_genes: Number of genes
            n_pathways: Number of pathways
            pathway_gene_mask: Binary mask [n_pathways, n_genes]
            n_meta_pathways: Number of meta-pathways (for hierarchical)
            cell_input_dim: Input dimension for cells (default: n_genes)
            cell_embedding_dim: Cell embedding dimension
            gene_embedding_dim: Gene embedding dimension
            cell_hidden_dim: Hidden dimension for cell encoder
            gene_hidden_dim: Hidden dimension for gene encoder
            use_hierarchical_pathways: Whether to use hierarchical projection
            use_unet_decoder: Whether to use U-Net decoder
            decoder_type: Type of decoder ('expression' or 'unet')
            cell_encoder_config: Additional config for cell encoder
            gene_encoder_config: Additional config for gene encoder
            projector_config: Additional config for projector
            decoder_config: Additional config for decoder
            grn_predictor_config: Additional config for GRN predictor
            reconstruction_loss_type: Type of reconstruction loss
            dropout: Dropout probability
        """
        super().__init__()
        
        # Validate inputs
        self._validate_inputs(
            n_cells, n_genes, n_pathways, pathway_gene_mask,
            n_meta_pathways, use_hierarchical_pathways
        )
        
        # Store dimensions
        self.n_cells = n_cells
        self.n_genes = n_genes
        self.n_pathways = n_pathways
        self.n_meta_pathways = n_meta_pathways
        self.cell_input_dim = cell_input_dim or n_genes
        self.cell_embedding_dim = cell_embedding_dim
        self.gene_embedding_dim = gene_embedding_dim
        
        # Store options
        self.use_hierarchical_pathways = use_hierarchical_pathways
        self.use_unet_decoder = use_unet_decoder or decoder_type == 'unet'
        self.reconstruction_loss_type = reconstruction_loss_type
        
        # Default configs
        cell_encoder_config = cell_encoder_config or {}
        gene_encoder_config = gene_encoder_config or {}
        projector_config = projector_config or {}
        decoder_config = decoder_config or {}
        grn_predictor_config = grn_predictor_config or {}
        
        # Build components
        self._build_encoders(
            cell_hidden_dim, gene_hidden_dim, dropout,
            cell_encoder_config, gene_encoder_config
        )
        
        self._build_projector(
            pathway_gene_mask, dropout, projector_config
        )
        
        self._build_decoder(dropout, decoder_config)
        
        self._build_grn_predictor(grn_predictor_config)
        
        # Initialize weights
        self._initialize_weights()
        
        # Build loss function
        self._build_loss_function(kwargs.get('loss_config', {}))
    
    def _validate_inputs(
        self,
        n_cells: int,
        n_genes: int,
        n_pathways: int,
        pathway_gene_mask: torch.Tensor,
        n_meta_pathways: Optional[int],
        use_hierarchical_pathways: bool
    ):
        """Validate input parameters."""
        assert n_cells > 0, "n_cells must be positive"
        assert n_genes > 0, "n_genes must be positive"
        assert n_pathways > 0, "n_pathways must be positive"
        
        assert pathway_gene_mask.shape == (n_pathways, n_genes), \
            f"pathway_gene_mask shape mismatch: expected ({n_pathways}, {n_genes}), " \
            f"got {pathway_gene_mask.shape}"
        
        if use_hierarchical_pathways:
            assert n_meta_pathways is not None and n_meta_pathways > 0, \
                "n_meta_pathways required for hierarchical pathways"
            assert n_meta_pathways < n_pathways, \
                "n_meta_pathways should be less than n_pathways"
    
    def _build_encoders(
        self,
        cell_hidden_dim: int,
        gene_hidden_dim: int,
        dropout: float,
        cell_encoder_config: Dict[str, Any],
        gene_encoder_config: Dict[str, Any]
    ):
        """Build cell and gene encoders."""
        # Cell encoder - avoid parameter conflicts
        cell_config = cell_encoder_config.copy()
        cell_config.pop('input_dim', None)  # Remove if exists
        cell_config.pop('hidden_dim', None)
        cell_config.pop('output_dim', None)
        cell_config.pop('dropout', None)
        
        self.cell_encoder = StandardCellEncoder(
            input_dim=self.cell_input_dim,
            hidden_dim=cell_hidden_dim,
            output_dim=self.cell_embedding_dim,
            dropout=dropout,
            **cell_config
        )
        
        # Gene encoder - avoid parameter conflicts
        gene_config = gene_encoder_config.copy()
        gene_config.pop('n_genes', None)  # Remove if exists
        gene_config.pop('hidden_dim', None)
        gene_config.pop('output_dim', None)
        gene_config.pop('dropout', None)
        
        self.gene_encoder = StandardGeneEncoder(
            n_genes=self.n_genes,
            hidden_dim=gene_hidden_dim,
            output_dim=self.gene_embedding_dim,
            dropout=dropout,
            **gene_config
        )
    
    def _build_projector(
        self,
        pathway_gene_mask: torch.Tensor,
        dropout: float,
        projector_config: Dict[str, Any]
    ):
        """Build pathway projector."""
        # Avoid parameter conflicts
        proj_config = projector_config.copy()
        proj_config.pop('cell_dim', None)
        proj_config.pop('n_genes', None)
        proj_config.pop('n_pathways', None)
        proj_config.pop('n_meta_pathways', None)
        proj_config.pop('pathway_gene_mask', None)
        proj_config.pop('dropout', None)
        
        if self.use_hierarchical_pathways:
            self.pathway_projector = HierarchicalPathwayProjector(
                cell_dim=self.cell_embedding_dim,
                n_genes=self.n_genes,
                n_pathways=self.n_pathways,
                n_meta_pathways=self.n_meta_pathways,
                pathway_gene_mask=pathway_gene_mask,
                dropout=dropout,
                **proj_config
            )
        else:
            self.pathway_projector = StandardPathwayProjector(
                cell_dim=self.cell_embedding_dim,
                n_genes=self.n_genes,
                n_pathways=self.n_pathways,
                pathway_gene_mask=pathway_gene_mask,
                dropout=dropout,
                **proj_config
            )
    
    def _build_decoder(
        self,
        dropout: float,
        decoder_config: Dict[str, Any]
    ):
        """Build expression decoder."""
        # Determine input dimension for decoder
        if self.use_hierarchical_pathways:
            decoder_input_dim = self.n_pathways + self.n_meta_pathways
        else:
            decoder_input_dim = self.n_pathways
        
        # Avoid parameter conflicts
        dec_config = decoder_config.copy()
        dec_config.pop('n_pathways', None)
        dec_config.pop('n_genes', None)
        dec_config.pop('pathway_gene_mask', None)
        dec_config.pop('use_hierarchical_input', None)
        dec_config.pop('n_meta_pathways', None)
        dec_config.pop('gene_embedding_dim', None)
        dec_config.pop('loss_type', None)
        dec_config.pop('dropout', None)
        
        if self.use_unet_decoder:
            self.expression_decoder = StandardUNetDecoder(
                n_pathways=self.n_pathways,
                n_genes=self.n_genes,
                pathway_gene_mask=self.pathway_projector.get_current_mask(),
                use_hierarchical_input=self.use_hierarchical_pathways,
                n_meta_pathways=self.n_meta_pathways or 0,
                dropout=dropout,
                **dec_config
            )
        else:
            self.expression_decoder = StandardExpressionDecoder(
                n_pathways=decoder_input_dim,
                n_genes=self.n_genes,
                gene_embedding_dim=self.gene_embedding_dim,
                loss_type=self.reconstruction_loss_type,
                dropout=dropout,
                **dec_config
            )
    
    def _build_grn_predictor(
        self,
        grn_predictor_config: Dict[str, Any]
    ):
        """Build GRN predictor."""
        # Avoid parameter conflicts
        grn_config = grn_predictor_config.copy()
        grn_config.pop('n_genes', None)
        grn_config.pop('gene_embedding_dim', None)
        
        self.grn_predictor = StandardGRNPredictor(
            n_genes=self.n_genes,
            gene_embedding_dim=self.gene_embedding_dim,
            **grn_config
        )
    
    def _build_loss_function(
        self,
        loss_config: Dict[str, Any]
    ):
        """Build loss function."""
        self.loss_fn = ScINTEGLoss(
            reconstruction_type=self.reconstruction_loss_type,
            **loss_config
        )
    
    def _initialize_weights(self):
        """Initialize model weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, std=0.02)
    
    def forward(
        self,
        x: torch.Tensor,
        cell_graph: torch.Tensor,
        gene_graph: torch.Tensor,
        gene_indices: Optional[torch.Tensor] = None,
        batch_labels: Optional[torch.Tensor] = None,
        return_intermediates: bool = False
    ) -> Union[ModelOutputs, Dict[str, torch.Tensor]]:
        """
        Forward pass through ScINTEG v3.
        
        Args:
            x: Expression matrix [n_cells, n_genes]
            cell_graph: Cell similarity graph [2, n_cell_edges]
            gene_graph: Gene co-expression graph [2, n_gene_edges]
            gene_indices: Gene indices for encoder (default: all genes)
            batch_labels: Batch labels for cells (optional)
            return_intermediates: Whether to return intermediate outputs
            
        Returns:
            ModelOutputs or dictionary of outputs
        """
        batch_size = x.shape[0]
        device = x.device
        
        # Default gene indices
        if gene_indices is None:
            gene_indices = torch.arange(self.n_genes, device=device)
        
        # 1. Encode cells
        cell_outputs = self.cell_encoder.encode(x, cell_graph)
        z_c = cell_outputs.embeddings  # [batch_size, cell_embedding_dim]
        
        # 2. Encode genes
        gene_outputs = self.gene_encoder.encode(
            gene_indices, gene_graph,
            batch=None  # Genes are shared across batch
        )
        z_g = gene_outputs.embeddings  # [n_genes, gene_embedding_dim]
        gene_attention = gene_outputs.attention_weights
        
        # 3. Project to pathway space
        if self.use_hierarchical_pathways:
            projector_outputs = self.pathway_projector.project(z_c, return_both_levels=True)
            z_p = projector_outputs.auxiliary['pathway_features']
            z_meta = projector_outputs.auxiliary['meta_features']
        else:
            projector_outputs = self.pathway_projector.project(z_c)
            z_p = projector_outputs.projections
            z_meta = None
        
        # 4. Reconstruct expression
        if self.use_unet_decoder:
            decoder_outputs = self.expression_decoder.decode(z_p, z_meta)
            x_recon = decoder_outputs.reconstruction
        else:
            # For standard decoder, handle hierarchical features
            if self.use_hierarchical_pathways and z_meta is not None:
                # Concatenate pathway and meta-pathway features
                z_combined = torch.cat([z_p, z_meta], dim=1)
                decoder_outputs = self.expression_decoder.decode(
                    z_combined, z_g, x
                )
            else:
                decoder_outputs = self.expression_decoder.decode(
                    z_p, z_g, x
                )
            x_recon = decoder_outputs.reconstruction
        
        # 5. Predict GRN
        grn_outputs = self.grn_predictor.predict(
            z_g, gene_attention,
            dynamic_scores=gene_outputs.importance_scores
        )
        
        # Prepare outputs
        if return_intermediates:
            # Return detailed dictionary
            outputs = {
                # Primary outputs
                'reconstruction': x_recon,
                'grn_edge_index': grn_outputs.predictions['edge_index'],
                'grn_edge_weight': grn_outputs.predictions['edge_weight'],
                'grn_adjacency': grn_outputs.predictions['adjacency_matrix'],
                # Embeddings
                'cell_embeddings': z_c,
                'gene_embeddings': z_g,
                'pathway_features': z_p,
                'meta_pathway_features': z_meta,
                # Attention and importance
                'gene_attention': gene_attention,
                'gene_importance': gene_outputs.importance_scores,
                'pathway_importance': projector_outputs.auxiliary.get('pathway_importance'),
                # Auxiliary
                'pathway_mask': projector_outputs.auxiliary.get('current_mask'),
                'grn_confidence': grn_outputs.confidence
            }
            
            # Add decoder-specific outputs
            if self.reconstruction_loss_type in ['nb', 'zinb']:
                outputs.update(decoder_outputs.auxiliary)
            
            # Add n_genes for GRN regularization
            outputs['n_genes'] = self.n_genes
            
            return outputs
        
        else:
            # Return standardized ModelOutputs
            return ModelOutputs(
                reconstruction=x_recon,
                grn={
                    'edge_index': grn_outputs.predictions['edge_index'],
                    'edge_weight': grn_outputs.predictions['edge_weight'],
                    'adjacency_matrix': grn_outputs.predictions['adjacency_matrix']
                },
                cell_embeddings=z_c,
                gene_embeddings=z_g,
                pathway_features=z_p,
                meta_pathway_features=z_meta,
                attention_weights={
                    'gene_attention': gene_attention
                },
                auxiliary={
                    'gene_importance': gene_outputs.importance_scores,
                    'grn_stats': grn_outputs.auxiliary,
                    # Add decoder outputs for NB/ZINB
                    **(decoder_outputs.auxiliary if self.reconstruction_loss_type in ['nb', 'zinb'] else {})
                }
            )
    
    def compute_loss(
        self,
        outputs: Union[ModelOutputs, Dict[str, torch.Tensor]],
        targets: torch.Tensor,
        return_components: bool = False
    ) -> Union[torch.Tensor, Tuple[torch.Tensor, Dict[str, torch.Tensor]]]:
        """Compute loss given outputs and targets."""
        # Convert ModelOutputs to dict if needed
        if isinstance(outputs, ModelOutputs):
            outputs_dict = {
                'reconstruction': outputs.reconstruction,
                'grn_edge_weight': outputs.grn['edge_weight'],
                'grn_edge_index': outputs.grn['edge_index'],
                'pathway_features': outputs.pathway_features,
                'n_genes': self.n_genes
            }
            # Add auxiliary outputs for NB/ZINB
            if outputs.auxiliary and self.reconstruction_loss_type in ['nb', 'zinb']:
                outputs_dict.update(outputs.auxiliary)
        else:
            outputs_dict = outputs
        
        return self.loss_fn(outputs_dict, targets, return_components)
    
    def get_pathway_gene_associations(self) -> Dict[int, torch.Tensor]:
        """Get current pathway-gene associations."""
        return self.pathway_projector.get_pathway_gene_associations()
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get model configuration for saving/loading."""
        config = {
            # Core dimensions
            'n_cells': self.n_cells,
            'n_genes': self.n_genes,
            'n_pathways': self.n_pathways,
            'n_meta_pathways': self.n_meta_pathways,
            'cell_input_dim': self.cell_input_dim,
            # Embedding dimensions
            'cell_embedding_dim': self.cell_embedding_dim,
            'gene_embedding_dim': self.gene_embedding_dim,
            # Architecture choices
            'use_hierarchical_pathways': self.use_hierarchical_pathways,
            'use_unet_decoder': self.use_unet_decoder,
            'reconstruction_loss_type': self.reconstruction_loss_type,
            # Component configs
            'cell_encoder_config': self.cell_encoder.get_config(),
            'gene_encoder_config': self.gene_encoder.get_config(),
            'projector_config': self.pathway_projector.get_config(),
            'decoder_config': self.expression_decoder.get_config(),
            'grn_predictor_config': self.grn_predictor.get_config()
        }
        return config
    
    @classmethod
    def from_config(
        cls,
        config: Dict[str, Any],
        pathway_gene_mask: torch.Tensor
    ) -> 'ScINTEGv3':
        """Create model from configuration."""
        # Extract component configs
        component_configs = {
            'cell_encoder_config': config.pop('cell_encoder_config', {}),
            'gene_encoder_config': config.pop('gene_encoder_config', {}),
            'projector_config': config.pop('projector_config', {}),
            'decoder_config': config.pop('decoder_config', {}),
            'grn_predictor_config': config.pop('grn_predictor_config', {})
        }
        
        # Create model
        return cls(
            pathway_gene_mask=pathway_gene_mask,
            **config,
            **component_configs
        )