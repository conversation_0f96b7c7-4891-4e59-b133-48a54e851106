# ScINTEG v3 深度学习框架全面审查提示词

## 背景说明
你现在需要对`scinteg_v3_clean`进行全面的代码审查。这是一个用于单细胞RNA-seq数据分析的深度学习框架，专门用于推断基因调控网络(GRN)。该框架使用图神经网络将细胞-基因二分图结构与生物学先验知识进行整合。

## 审查任务

### 1. 项目完成状态评估
请分析并报告：
- 核心功能模块的实现完整性（已完成/部分完成/未开始）
- 缺失的关键功能或组件
- TODO注释和未实现的占位符函数
- 测试覆盖率和测试完整性
- 文档完整性（API文档、使用示例、配置说明）
- 依赖管理和版本兼容性

### 2. 数据处理流程审查
详细分析以下内容：
- **输入数据格式支持**：
  - 支持的数据格式（h5ad, csv, mtx等）
  - 数据验证和错误处理机制
  - 批量数据处理能力
  
- **预处理管道**：
  - 数据标准化方法（log transformation, scaling等）
  - 质量控制步骤（过滤低质量细胞/基因）
  - 批次效应校正方法（Harmony, Combat等）
  - 特征选择策略（高变基因选择等）
  
- **数据增强策略**：
  - 实现的增强技术
  - 增强参数的可配置性
  - 增强对模型性能的影响

### 3. 数据流向分析
绘制并说明完整的数据流向图，包括：
- 从原始数据到模型输入的转换过程
- 各个处理阶段的数据维度变化
- 中间数据结构和缓存机制
- 批处理和数据加载器的实现
- 图结构构建过程（k-NN图、二分图等）

### 4. 模型架构深度分析
详细审查模型的各个组件：

- **编码器(Encoders)**：
  - 细胞编码器和基因编码器的具体实现
  - GCN/GAT层的配置和参数
  - 激活函数和正则化策略
  
- **投影器(Projectors)**：
  - 标准投影器vs层次投影器的实现差异
  - 路径整合机制
  - 软掩码学习策略
  
- **解码器(Decoders)**：
  - MLP解码器vs U-Net解码器
  - 重构策略和损失函数选择
  
- **预测器(Predictors)**：
  - GRN预测模块的实现
  - 因果关系推断机制

### 5. 训练过程审查
分析训练流程的各个方面：
- **训练策略**：
  - 三阶段训练的具体实现
  - 每个阶段的目标和参数设置
  - 阶段转换的条件和时机
  
- **损失函数**：
  - 各种损失函数的实现（MSE, NB, ZINB, Adaptive Huber）
  - 损失权重的动态调整
  - 正则化项的使用
  
- **优化器配置**：
  - 学习率调度策略
  - 梯度裁剪和稳定性措施
  - 早停机制
  
- **性能优化**：
  - GPU利用率
  - 混合精度训练支持
  - 分布式训练能力

### 6. 下游分析功能
评估框架提供的分析工具：
- GRN推断和可视化
- 细胞类型注释功能
- 轨迹推断支持
- 差异表达分析
- 路径富集分析
- 结果导出格式和兼容性

### 7. 函数调用关系分析
提供以下信息：
- 主要函数的调用图
- 关键代码路径的执行流程
- 循环依赖检测
- 性能瓶颈识别
- 内存使用模式分析

## 输出要求

### 格式规范
1. 使用Markdown格式组织报告
2. 为每个审查部分提供：
   - 现状总结
   - 发现的问题（按严重程度分类）
   - 改进建议（具体且可操作）
   - 代码示例（如适用）

### 问题分类
将发现的问题按以下级别分类：
- 🔴 **关键问题**：影响核心功能，需立即修复
- 🟡 **重要问题**：影响性能或可维护性，应尽快解决
- 🟢 **建议改进**：可以提升代码质量但不紧急

### 重点关注领域
特别注意以下方面：
1. **代码质量**：
   - 代码复用性和模块化程度
   - 错误处理的完整性
   - 日志记录的充分性
   
2. **性能考虑**：
   - 大规模数据集的处理能力
   - 内存效率
   - 计算效率
   
3. **可扩展性**：
   - 新模型组件的添加难度
   - 配置系统的灵活性
   - 插件架构支持
   
4. **最佳实践遵循**：
   - PyTorch最佳实践
   - 单细胞分析标准流程
   - 代码风格一致性

## 执行步骤建议

1. **初始扫描**：快速浏览整个代码库结构
2. **深度分析**：按模块逐个深入分析
3. **集成测试**：运行示例和测试用例
4. **性能评估**：使用真实数据测试性能
5. **文档审查**：检查文档完整性和准确性
6. **总结报告**：整合所有发现并提供行动计划

## 附加要求

- 提供具体的代码改进示例
- 标注需要立即关注的安全或性能问题
- 建议合理的开发优先级
- 如发现设计模式问题，提供重构建议
- 评估与其他单细胞工具（Scanpy、Seurat）的互操作性

请基于以上指导，对`scinteg_v3_clean`进行全面、系统的审查，并提供详细的分析报告。